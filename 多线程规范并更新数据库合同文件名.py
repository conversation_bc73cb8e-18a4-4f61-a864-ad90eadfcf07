import os
import re
import time
import logging
from glob import glob
from docx import Document
import PyPDF2
import sqlite3
from win32com.shell import shell, shellcon
import concurrent.futures
from tqdm import tqdm
import threading
import unicodedata

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("contract_rename.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

# 线程本地存储，用于每个线程独立的数据库连接
thread_local = threading.local()


def convert_fullwidth_to_halfwidth(text):
    """将全角字符转换为半角字符"""
    # 使用unicodedata.normalize将全角字符转换为半角字符
    text = unicodedata.normalize("NFKC", text)

    # 特殊处理一些可能未被正确转换的中文标点
    char_map = {
        "（": "(",
        "）": ")",
        "：": ":",
        "，": ",",
        "。": ".",
        '"': '"',
        '"': '"',
        """: "'",
        """: "'",
        "！": "!",
        "？": "?",
        "；": ";",
        "【": "[",
        "】": "]",
        "『": "[",
        "』": "]",
        "「": "[",
        "」": "]",
        "、": ",",
        "～": "~",
        "《": "<",
        "》": ">",
    }

    for full, half in char_map.items():
        text = text.replace(full, half)

    return text


# 支持的文件扩展名
EXTS = [".doc", ".docx", ".pdf"]
# EXTS = ['.pdf']
CONTRACT_DIR = r"D:\user\合同汇总"
DB_PATH = r"d:\user\PythonProject\ZConline\app.db"

# 最大线程数
MAX_WORKERS = 1

# 合同编号正则
PATTERNS = [
    re.compile(r"ZC\d{2}-A\d{3}"),
    # 新规则：CRRC+2位数字+1位字母或数字+3位数字+1位字母或数字+7位数字
    re.compile(r"CRRC\d{2}[A-Z0-9]\d{3}[A-Z0-9]\d{7}"),
]


def get_db_connection():
    """获取线程本地的数据库连接"""
    if not hasattr(thread_local, "conn"):
        thread_local.conn = sqlite3.connect(DB_PATH)
    return thread_local.conn


def move_to_recycle_bin(filepath):
    """移动文件到回收站"""
    if os.path.exists(filepath):
        try:
            res = shell.SHFileOperation(
                (
                    0,
                    shellcon.FO_DELETE,
                    filepath,
                    None,
                    shellcon.FOF_SILENT
                    | shellcon.FOF_ALLOWUNDO
                    | shellcon.FOF_NOCONFIRMATION,
                    None,
                    None,
                )
            )
            if res[1] != 0:
                logger.error(f"删除文件{filepath}失败")
            else:
                logger.info(f"删除文件{filepath}成功")
        except Exception as e:
            logger.error(f"删除文件失败 {filepath}: {e}")
    else:
        logger.warning(f"文件：{filepath}不存在！")


def rename_file_if_match_pattern(file_path):
    """根据模式重命名文件"""
    basename = os.path.basename(file_path)
    name, ext = os.path.splitext(basename)

    matched_pattern = None
    match = None
    for pattern in PATTERNS:
        match = pattern.search(name)
        if match and match.end() == len(name):  # 确保匹配出现在字符串结尾
            matched_pattern = pattern
            break

    if matched_pattern and match:
        contract_no = match.group(0)
        logger.debug(
            f"basename: {basename}, name: {name}, contract_no: {contract_no}, match.start(): {match.start()}"
        )
        if not contract_no:
            logger.error(f"合同号为空，跳过重命名: {basename}")
            return
        prefix = name[: match.start()]
        if prefix:
            new_name_without_ext = f"{contract_no}_{prefix}"
        else:
            new_name_without_ext = contract_no
        new_name = f"{new_name_without_ext}{ext}"
        if not new_name_without_ext.strip():
            logger.error(f"新文件名主体为空，跳过重命名: {basename}")
            return
        new_path = os.path.join(os.path.dirname(file_path), new_name)
        try:
            os.rename(file_path, new_path)
            logger.info(f"重命名成功: {basename} -> {new_name}")
        except Exception as e:
            logger.error(f"重命名失败: {basename}, 错误: {e}")
            move_to_recycle_bin(new_path)
    else:
        logger.info(f"未找到匹配的正则结尾: {basename}")


# 判断文件名是否已含合同号
def extract_contract_no_from_filename(filename):
    """从文件名中提取合同号"""
    for pat in PATTERNS:
        m = pat.search(filename)
        if m:
            return m.group(0)
    return None


# 从文本中提取合同号
def extract_contract_no_from_text(text):
    """从文本中提取合同号"""
    for pat in PATTERNS:
        m = pat.search(text)
        if m:
            return m.group(0)
    return None


# 读取docx文本
def read_docx_text(path):
    """读取docx文件内容"""
    try:
        doc = Document(path)
        return "\n".join([p.text for p in doc.paragraphs])
    except Exception as e:
        logger.error(f"读取docx文件失败: {path}, 错误: {e}")
        return ""


# 读取pdf文本
def read_pdf_text(path):
    """读取pdf文件内容"""
    # 首先尝试使用PyPDF2直接提取文本
    try:
        with open(path, "rb") as f:
            reader = PyPDF2.PdfReader(f)
            text = ""
            for page in reader.pages:
                extracted = page.extract_text()
                if extracted:
                    text += extracted
            if text.strip():  # 如果有内容，返回结果
                return text
    except Exception as e:
        logger.error(f"PyPDF2 读取失败: {e}")
    return ""


# 读取doc文本
def read_doc_text(path):
    """读取doc文件内容"""
    try:
        import win32com.client

        word = win32com.client.Dispatch("Word.Application")
        word.Visible = False
        doc = word.Documents.Open(path)
        text = doc.Content.Text
        doc.Close()
        word.Quit()
        return text
    except Exception as e:
        logger.error(f"读取doc文件失败: {path}, 错误: {e}")
        return ""


def process_file(file):
    """处理单个文件"""
    logger.info(f"开始处理文件: {file}")

    basename = os.path.basename(file)
    name, ext = os.path.splitext(basename)
    ClearName = name

    # 下面为原有未找到合同号的处理逻辑
    contract_no = extract_contract_no_from_filename(basename)
    if contract_no:
        if re.match(
            rf"^{re.escape(contract_no)}[\s_\-]", basename, flags=re.IGNORECASE
        ):
            new_path = os.path.join(CONTRACT_DIR, basename)
            update_or_insert_data_o_db(contract_no, new_path)
            return
        pattern = rf"^{contract_no}[\s_\-]*"
        rest_name = re.sub(pattern, "", basename, flags=re.IGNORECASE)
        name, ext1 = os.path.splitext(rest_name)
        if len(ext1) == 0:
            # 已经规范，无需重命名
            new_path = os.path.join(CONTRACT_DIR, basename)
            update_or_insert_data_o_db(contract_no, new_path)
            return
        pattern = rf".*?{re.escape(contract_no)}$"
        new_name_without_ext = re.sub(pattern, "", name, flags=re.IGNORECASE).rstrip(
            "_"
        )
        new_name = f"{new_name_without_ext}{ext1}"
        new_name = re.sub(
            rf"({re.escape(contract_no)})[\s_\-]+\1",
            r"\1",
            new_name,
            flags=re.IGNORECASE,
        )
        new_path = os.path.join(CONTRACT_DIR, new_name)
        if file != new_path:
            try:
                os.rename(file, new_path)
                logger.info(f"重命名成功: {basename} -> {new_name}")
                update_or_insert_data_o_db(contract_no, new_path)
            except Exception as e:
                logger.error(f"重命名失败: {basename} -> {new_name}, 错误: {e}")
                move_to_recycle_bin(file)
    else:
        text = ""
        if file.lower().endswith(".docx"):
            text = read_docx_text(file)
        elif file.lower().endswith(".pdf"):
            try:
                text = read_pdf_text(file)
            except Exception as e:
                logger.error(f"读取pdf文件失败: {file}, 错误: {e}")
        elif file.lower().endswith(".doc"):
            text = read_doc_text(file)
        idx = text.find("合同编号")
        if idx != -1:
            search_text = text[idx : idx + 100]
            contract_no = extract_contract_no_from_text(search_text)
        if not contract_no:
            contract_no = extract_contract_no_from_text(text)
        if contract_no:
            basename_contract_id = extract_contract_no_from_text(basename)
            if basename_contract_id and basename_contract_id != contract_no:
                new_name = re.sub(
                    basename_contract_id,
                    contract_no,
                    basename,
                    flags=re.IGNORECASE,
                )
                new_path = os.path.join(CONTRACT_DIR, new_name)
                if file != new_path:
                    try:
                        os.rename(file, new_path)
                        logger.info(f"重命名: {basename} -> {new_name}")
                    except Exception:
                        move_to_recycle_bin(file)
            elif basename_contract_id:
                new_path = os.path.join(CONTRACT_DIR, basename)
            else:
                new_name = f"{contract_no}_{basename}"
                new_name = re.sub(
                    rf"({re.escape(contract_no)})[\s_\-]+\1",
                    r"\1",
                    new_name,
                    flags=re.IGNORECASE,
                )
                new_path = os.path.join(CONTRACT_DIR, new_name)
                if file != new_path:
                    try:
                        os.rename(file, new_path)
                        logger.info(f"重命名: {basename} -> {new_name}")
                    except Exception:
                        move_to_recycle_bin(file)
            update_or_insert_data_o_db(contract_no, new_path)
        else:
            logger.warning(f"未找到合同号: {basename}")


def update_or_insert_data_o_db(contract_no, new_path):
    try:  # 获取线程本地的数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            f"select conname,provider,conmoney,taxrate,operator from contract WHERE conid='{contract_no}'"
        )
        result = cursor.fetchone()
        if result:
            conname, provider, conmoney, taxrate, operator = result
            cursor.execute(
                f"select 1 from 品类及合同文件 WHERE 合同编号='{contract_no}'"
            )
            if cursor.fetchone():
                cursor.execute(
                    "UPDATE 品类及合同文件 SET 合同名称=?, 对手方名称=?, 合同金额=?, 约定税率=?, 合同文件名=? WHERE 合同编号=?",
                    (
                        conname,
                        provider,
                        conmoney,
                        taxrate,
                        new_path,
                        contract_no,
                    ),
                )
                conn.commit()
                logger.info(f"更新{contract_no}成功!")
            else:
                if "周云飞" in operator:
                    category_name = "信息化软硬件及工装设备类-工装类-工装-工装模具-维修"
                    category_id = "371-07"
                elif any(
                    name in operator
                    for name in ["郑东", "罗怀磊", "黄俊", "田云", "刘丽"]
                ):
                    category_name = "信息化软硬件及工装设备类-设备类-设备工程服务-设备工程服务-设备维修类"
                    category_id = "321-01"
                elif "龙东旭" in operator:
                    category_name = (
                        "信息化软硬件及工装设备类-设备类-设备采购-设备采购-杂项设备"
                    )
                    category_id = "301-10"
                else:
                    category_name = "工程类类-工程基建类-工程施工-工程施工-工程维修"
                    category_id = "403-05"
                cursor.execute(
                    "insert into 品类及合同文件(合同编号, 合同名称,合同金额,约定税率,对手方名称,品类编码,品类名称,合同文件名) values (?,?,?,?,?,?,?,?)",
                    (
                        contract_no,
                        conname,
                        conmoney,
                        taxrate,
                        provider,
                        category_id,
                        category_name,
                        new_path,
                    ),
                )
                conn.commit()
                logger.info(f"插入新合同记录: {contract_no}成功!")
        else:
            logger.warning(f"未找到合同记录: {contract_no}")
    except Exception as e:
        logger.error(f"处理文件失败: {new_path}, 错误: {e}")

    # 不关闭连接，因为它是线程本地的，会在线程结束时自动关闭


def preprocess_file(file):
    """预处理文件名，去除空格等特殊字符，并将全角字符转换为半角字符"""
    # 分离文件路径和文件名
    dirname = os.path.dirname(file)
    basename = os.path.basename(file)

    # 转换文件名中的全角字符为半角字符
    basename = convert_fullwidth_to_halfwidth(basename)

    # 其他清理操作
    basename = (
        basename.replace(" ", "").replace(r"()", "").replace(r"__", "_")
    ).strip()

    # 重新组合路径
    new_path = os.path.join(dirname, basename)

    if file != new_path:
        if not os.path.exists(new_path):
            if os.path.exists(file):
                try:
                    os.rename(file, new_path)
                    logger.info(f"预处理重命名成功: {file} -> {new_path}")
                    return new_path
                except Exception as e:
                    logger.error(f"预处理重命名失败: {file}, 错误: {e}")
                    return file
            else:
                logger.warning(f"{file}文件缺失，请检查！")
                return None
        else:
            try:
                move_to_recycle_bin(file)
                logger.info(f"已移动到回收站: {file}")
                return new_path
            except Exception as e:
                logger.error(f"移动到回收站失败: {file}, 错误: {e}")
                return None
    return file


def main():
    """主函数：使用多线程处理文件"""
    start_time = time.time()
    logger.info("开始批量处理合同文件")

    # 清空日志文件
    log_path = os.path.join(os.path.dirname(__file__), "contract_rename.log")
    with open(log_path, "w", encoding="utf-8"):
        pass

    # 获取所有PDF文件
    files = []
    for ext in EXTS:
        files.extend(glob(os.path.join(CONTRACT_DIR, f"*{ext}")))

    total_files = len(files)
    logger.info(f"找到 {total_files} 个文件需要处理")

    if total_files == 0:
        logger.warning(f"目录 {CONTRACT_DIR} 中没有找到需要处理的文件")
        return

    # 预处理文件名
    logger.info("开始预处理文件名...")
    preprocessed_files = []
    with tqdm(total=total_files, desc="预处理文件") as pbar:
        for file in files:
            result = preprocess_file(file)
            if result:
                preprocessed_files.append(result)
            pbar.update(1)

    # 使用线程池处理文件
    logger.info(f"使用 {MAX_WORKERS} 个线程开始处理文件...")
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 提交所有任务
        future_to_file = {
            executor.submit(process_file, file): file for file in preprocessed_files
        }

        # 使用tqdm显示进度
        with tqdm(total=len(preprocessed_files), desc="处理文件") as pbar:
            for future in concurrent.futures.as_completed(future_to_file):
                file = future_to_file[future]
                try:
                    future.result()  # 获取处理结果
                except Exception as e:
                    logger.error(f"处理文件时发生错误: {file}, 错误: {e}")
                finally:
                    pbar.update(1)

    # 计算总耗时
    end_time = time.time()
    total_time = end_time - start_time
    logger.info(f"批量处理完成！总耗时: {total_time:.2f} 秒")


if __name__ == "__main__":
    main()
