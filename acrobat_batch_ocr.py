#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-06-13 18:36:58
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-15 12:30:19
# 文件相对于项目的路径   : \Crawl_AI\acrobat_batch_ocr.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
import os
import time
import win32com.client
from PDF转换 import read_pdf_text

# 配置你的PDF目录和输出目录
PDF_DIR = r'D:\user\合同汇总'
OUTPUT_DIR = r'D:\user\合同汇总\ocr_result'

if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# 获取所有PDF文件
pdf_files = [f for f in os.listdir(PDF_DIR) if f.lower().endswith('.pdf')]

# 启动Acrobat应用
acrobat = win32com.client.Dispatch('AcroExch.App')

for pdf_name in pdf_files:
    pdf_path = os.path.join(PDF_DIR, pdf_name)
    if read_pdf_text(pdf_path):
        print(f'跳过: {pdf_path}')
        continue
    output_path = os.path.join(OUTPUT_DIR, os.path.splitext(pdf_name)[0] + '_ocr.pdf')
    try:
        avdoc = win32com.client.Dispatch('AcroExch.AVDoc')
        if not avdoc.Open(pdf_path, ''):
            print(f'无法打开: {pdf_path}')
            continue
        acrobat.Show()  # 可选，让窗口可见
        avdoc.BringToFront()
        # 执行OCR命令（菜单命令可能因语言不同而不同）
        # 英文版通常为 RecognizeTextInThisFile，中文版可尝试 TextRecognition:RecognizeTextInThisFile
        try:
            avdoc.ExecuteThisJavaScript('app.execMenuItem("RecognizeTextInThisFile")')
        except Exception:
            try:
                avdoc.ExecuteThisJavaScript(
                    'app.execMenuItem("TextRecognition:RecognizeTextInThisFile")'
                )
            except Exception as e2:
                print(f'无法执行OCR命令: {pdf_name}, {e2}')
                avdoc.Close(True)
                continue
        # 等待OCR完成（简单sleep，可根据实际情况优化）
        time.sleep(10)  # 视文件大小调整
        # 保存为新文件
        pdDoc = avdoc.GetPDDoc()
        pdDoc.Save(1, output_path)
        avdoc.Close(True)
        print(f'完成: {pdf_name} -> {output_path}')
    except Exception as e:
        print(f'处理失败: {pdf_name}, 错误: {e}')

# 关闭Acrobat
acrobat.Exit()
print('批量OCR完成！')
