#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-05-31 21:48:56
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-15 15:46:17
# 文件相对于项目的路径   : \Crawl_AI\填写品类并上传合同文件.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-05-31 11:14:39
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-05-31 20:47:08
# 文件相对于项目的路径   : \Crawl_AI\填写品类并上传合同文件.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#


import time
import logging
import traceback
import inspect
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
import sys
from typing import Optional
from dataclasses import dataclass
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
)
from time import sleep
import sqlite3
from datetime import datetime
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import os
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),  # 文件使用UTF-8编码
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


def log_exception_location(func_name: str = None, additional_info: str = None):
    """
    记录异常发生的详细位置信息
    :param func_name: 函数名（可选）
    :param additional_info: 额外信息（可选）
    """
    try:
        # 获取当前帧信息
        current_frame = inspect.currentframe()
        caller_frame = current_frame.f_back

        # 获取调用者的详细信息
        filename = caller_frame.f_code.co_filename
        line_number = caller_frame.f_lineno
        function_name = caller_frame.f_code.co_name

        # 如果没有提供func_name，使用调用者的函数名
        if not func_name:
            func_name = function_name

        # 记录位置信息
        location_info = f'位置: {filename}:{line_number} 在函数 {func_name}()'
        if additional_info:
            location_info += f' - {additional_info}'

        logger.error(f'异常位置信息: {location_info}')

        # 记录完整的堆栈跟踪
        logger.error('完整堆栈跟踪:')
        logger.error(traceback.format_exc())

    except Exception as e:
        logger.error(f'记录异常位置时发生错误: {e}')


@dataclass
class Config:
    """配置类"""

    CHROME_DRIVER_PATH: str = (
        r'C:\Program Files\Google\Chrome\Application\chromedriver.exe'
    )
    LOGIN_URL: str = (
        'https://sso.crrcgo.cc/login?returnUrl=https%3A%2F%2Fwww.crrcgo.cc%2F%23%2FhomePage'
        # 'https://sso.crrcgo.cc/login?v=1&client=cangqiong&returnUrl=https%3A%2F%2Fportal.crrcgo.cc%2F%2F%3F&isPortalMobile=false'
    )
    USERNAME: str = '010800006291'
    PASSWORD: str = 'Z6h2en91@'
    MAX_RETRIES: int = 3
    WAIT_TIMEOUT: int = 20  # 增加等待时间
    PAGE_LOAD_TIMEOUT: int = 30  # 页面加载超时时间
    RETRY_INTERVAL: int = 2  # 重试间隔时间


def robust_execute(cursor, sql, params=(), max_retries=5, retry_interval=1):
    for attempt in range(max_retries):
        try:
            cursor.execute(sql, params)
            return
        except sqlite3.OperationalError as e:
            if 'database is locked' in str(e):
                if attempt < max_retries - 1:
                    time.sleep(retry_interval)
                    continue
            raise


class ContractCrawler:
    def __init__(self, config: Config):
        self.config = config
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        try:
            from ddddocr import DdddOcr

            self.ocr = DdddOcr(show_ad=False)
        except ImportError as e:
            logger.error('OCR初始化失败，请确保已安装 ddddocr: %s', e)
            sys.exit("缺少 ddddocr 库，请运行 'pip install ddddocr'")

    def setup_driver(self):
        if self.driver and self.wait:
            return
        options = webdriver.ChromeOptions()
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('log-level=3')
        options.add_experimental_option('excludeSwitches', ['enable-logging'])
        options.page_load_strategy = 'normal'
        service = Service(self.config.CHROME_DRIVER_PATH)
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.maximize_window()
        self.driver.set_page_load_timeout(self.config.PAGE_LOAD_TIMEOUT)
        self.wait = WebDriverWait(self.driver, self.config.WAIT_TIMEOUT)
        logger.info('浏览器初始化完成')

    def handle_captcha(self) -> str:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        max_captcha_retries = 3  # 限制验证码刷新次数

        for captcha_attempt in range(max_captcha_retries):
            try:
                logger.info(
                    f'验证码识别尝试 {captcha_attempt + 1}/{max_captcha_retries}'
                )

                # 快速定位验证码图片
                captcha_img = self.wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            "//img[@src='/getCode' and contains(@class, 'validImg')]",
                        )
                    )
                )

                # 检查验证码图片是否完全加载
                if not self._wait_for_captcha_loaded(captcha_img):
                    logger.warning('验证码图片加载超时，尝试刷新')
                    if captcha_attempt < max_captcha_retries - 1:
                        self._refresh_captcha()
                        continue
                    else:
                        raise Exception('验证码图片加载失败')

                # 快速截图
                img_bytes = captcha_img.screenshot_as_png
                if not img_bytes:
                    logger.warning('验证码截图失败，尝试刷新')
                    if captcha_attempt < max_captcha_retries - 1:
                        self._refresh_captcha()
                        continue
                    else:
                        raise Exception('无法获取验证码截图')

                # 优化的OCR识别
                start_time = time.time()
                code = self._fast_ocr_recognition(img_bytes)
                ocr_time = time.time() - start_time
                logger.info(f'OCR识别耗时: {ocr_time:.2f}秒')

                # 验证识别结果
                if self._validate_captcha_code(code):
                    logger.info(f'验证码识别成功: {code}')
                    return code
                else:
                    logger.warning(f'验证码识别结果异常: {code}')
                    if captcha_attempt < max_captcha_retries - 1:
                        self._refresh_captcha()
                        continue
                    else:
                        logger.error('验证码识别多次失败，使用最后一次结果')
                        return code if code else '0000'

            except (TimeoutException, NoSuchElementException) as e:
                log_exception_location('handle_captcha', '验证码图片元素查找失败')
                logger.error('未找到验证码图片元素: %s', e)
                if captcha_attempt < max_captcha_retries - 1:
                    time.sleep(1)
                    continue
                else:
                    raise
            except Exception as e:
                log_exception_location(
                    'handle_captcha', f'验证码处理过程异常，尝试{captcha_attempt + 1}'
                )
                logger.error('验证码处理失败: %s', e)
                if captcha_attempt < max_captcha_retries - 1:
                    time.sleep(1)
                    continue
                else:
                    raise

        # 如果所有尝试都失败，返回默认值
        logger.error('验证码识别彻底失败，返回默认值')
        return '0000'

    def _wait_for_captcha_loaded(self, captcha_img, timeout=3) -> bool:
        """
        等待验证码图片完全加载
        :param captcha_img: 验证码图片元素
        :param timeout: 超时时间（秒）
        :return: 是否加载完成
        """
        try:
            start_time = time.time()

            while time.time() - start_time < timeout:
                # 检查图片的自然尺寸
                img_width = self.driver.execute_script(
                    'return arguments[0].naturalWidth;', captcha_img
                )
                img_height = self.driver.execute_script(
                    'return arguments[0].naturalHeight;', captcha_img
                )

                # 检查图片是否有效加载（尺寸大于0）
                if img_width > 0 and img_height > 0:
                    # 额外等待100ms确保图片完全渲染
                    time.sleep(0.1)
                    logger.debug(f'验证码图片加载完成: {img_width}x{img_height}')
                    return True

                time.sleep(0.1)  # 短暂等待后重试

            logger.warning(f'验证码图片加载超时({timeout}秒)')
            return False

        except Exception as e:
            logger.warning(f'检查验证码图片加载状态失败: {e}')
            # 发生异常时等待较短时间作为fallback
            time.sleep(0.5)
            return True

    def _fast_ocr_recognition(self, img_bytes) -> str:
        """
        快速OCR识别
        :param img_bytes: 图片字节数据
        :return: 识别结果
        """
        try:
            # 设置较短的超时时间避免长时间等待
            code = self.ocr.classification(img_bytes)

            # 处理不同的返回格式
            if isinstance(code, dict) and 'result' in code:
                code = code['result']
            elif isinstance(code, dict) and 'text' in code:
                code = code['text']

            # 确保返回字符串格式
            if code is not None:
                code = str(code).strip()

            return code

        except Exception as e:
            logger.warning(f'OCR识别异常: {e}')
            return None

    def _validate_captcha_code(self, code) -> bool:
        """
        验证识别的验证码是否有效
        :param code: 识别的验证码
        :return: 是否有效
        """
        if not code:
            return False

        # 转换为字符串并清理
        code_str = str(code).strip()

        # 检查长度（通常验证码为4位）
        if len(code_str) != 4:
            logger.debug(f'验证码长度异常: {len(code_str)} (期望: 4)')
            return False

        # 检查是否包含有效字符（字母数字）
        if not code_str.isalnum():
            logger.debug(f'验证码包含无效字符: {code_str}')
            return False

        # 检查是否为明显的识别错误（如全是相同字符）
        if len(set(code_str)) == 1:
            logger.debug(f'验证码识别可能错误（全相同字符）: {code_str}')
            return False

        return True

    def _refresh_captcha(self) -> None:
        """
        刷新验证码
        """
        try:
            logger.info('正在刷新验证码...')

            # 查找刷新按钮的多种方式
            refresh_selectors = [
                "//img[@src='/getCode']/following-sibling::span",
                "//span[contains(@class, 'refresh')]",
                "//a[contains(@onclick, 'refreshCode')]",
                "//*[contains(@class, 'captcha-refresh')]",
            ]

            refresh_button = None
            for selector in refresh_selectors:
                try:
                    refresh_button = self.driver.find_element(By.XPATH, selector)
                    if refresh_button.is_displayed():
                        break
                except Exception:
                    continue

            if refresh_button:
                refresh_button.click()
                logger.info('已点击验证码刷新按钮')
                # 等待新验证码加载
                time.sleep(0.8)
            else:
                logger.warning('未找到验证码刷新按钮，尝试直接刷新图片')
                # 直接点击验证码图片进行刷新
                captcha_img = self.driver.find_element(
                    By.XPATH, "//img[@src='/getCode' and contains(@class, 'validImg')]"
                )
                captcha_img.click()
                time.sleep(0.8)

        except Exception as e:
            logger.warning(f'刷新验证码失败: {e}')
            time.sleep(0.5)

    def login(self) -> bool:
        """
        执行登录操作
        :return: 登录是否成功
        """
        try:
            logger.info('开始登录操作')

            # 首先检查是否已经登录
            try:
                # 检查当前URL是否已经在portal页面
                current_url = self.driver.current_url
                if 'portal.crrcgo.cc' in current_url:
                    logger.info('检测到已在portal页面')
                    # 检查是否有电子采购按钮（表示已登录）
                    try:
                        e_procurement_button = self.driver.find_element(
                            By.XPATH,
                            "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]",
                        )
                        if e_procurement_button.is_displayed():
                            logger.info(
                                '发现电子采购按钮，已登录状态，直接进入电子采购'
                            )
                            e_procurement_button.click()
                            try:
                                self.wait.until(EC.number_of_windows_to_be(2))
                                self.driver.switch_to.window(
                                    self.driver.window_handles[-1]
                                )
                                self.wait.until(
                                    EC.presence_of_element_located(
                                        (By.TAG_NAME, 'body')
                                    )
                                )
                                logger.info('电子采购页面已加载')
                                sleep(5)
                                return True
                            except TimeoutException:
                                logger.warning('等待新窗口超时，尝试直接继续')
                                if len(self.driver.window_handles) > 1:
                                    self.driver.switch_to.window(
                                        self.driver.window_handles[-1]
                                    )
                                    return True
                    except Exception:
                        logger.debug('未找到电子采购按钮，可能需要重新登录')

                # 检查是否在采购系统页面
                if (
                    'zxcg.crrcgo.cc' in current_url
                    or 'procurement' in current_url.lower()
                ):
                    logger.info('检测到已在采购系统页面，登录状态有效')
                    return True

            except Exception as e:
                logger.debug(f'登录状态检查失败: {e}，继续执行正常登录流程')

            # 如果不是已登录状态，执行正常登录流程
            logger.info('需要执行登录流程')
            self.driver.get(self.config.LOGIN_URL)
            logger.info('正在访问登录页面...')

            # 等待登录页面加载
            self.wait.until(EC.presence_of_element_located((By.NAME, 'username')))
            # self.wait.until(EC.presence_of_element_located((By.NAME, 'password')))
            self.wait.until(
                EC.presence_of_element_located((By.NAME, 'password_visible'))
            )
            username_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, 'username'))
            )
            password_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, 'password_visible'))
            )

            username_input.send_keys(self.config.USERNAME)
            password_input.send_keys(self.config.PASSWORD)
            sleep(0.5)
            self.driver.find_element(By.ID, 'loginBtnCode').click()
            return True
            """
            for attempt in range(self.config.MAX_RETRIES):
                try:
                    # 快速获取验证码
                    sleep(2)
                    start_time = time.time()
                    code = self.handle_captcha()
                    captcha_time = time.time() - start_time
                    logger.info(f'验证码获取耗时: {captcha_time:.2f}秒')

                    # 快速定位并清空验证码输入框
                    validcode_input = self.wait.until(
                        EC.presence_of_element_located((By.NAME, 'validCode'))
                    )
                    validcode_input.clear()

                    # 快速输入验证码
                    validcode_input.send_keys(code)
                    logger.info(f'验证码输入完成: {code}')

                    # 等待验证码确实填充到输入框中再点击登录按钮
                    max_wait_attempts = 10  # 最多等待5秒
                    for wait_attempt in range(max_wait_attempts):
                        current_value = validcode_input.get_attribute('value') or ''
                        if current_value.strip() == code.strip():
                            logger.info('验证码已成功填充到输入框')
                            break
                        elif wait_attempt < max_wait_attempts - 1:
                            logger.debug(
                                f"等待验证码填充完成... 当前值: '{current_value}', 期望值: '{code}'"
                            )
                            sleep(1)
                        else:
                            logger.warning('验证码填充等待超时，继续尝试登录')
                            break

                    # 点击登录按钮，减少验证码过期风险
                    login_button = self.wait.until(
                        EC.element_to_be_clickable((By.CLASS_NAME, 'loginBtn'))
                    )
                    sleep(2)  # 稍微减少等待时间
                    login_button.click()
                    logger.info('已点击登录按钮')

                    # 快速检查登录结果
                    login_result = self._check_login_result(timeout=8)

                    if login_result == 'success':
                        logger.info('登录成功，准备进入电子采购')
                        e_procurement_button = self.wait.until(
                            EC.element_to_be_clickable(
                                (
                                    By.XPATH,
                                    "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]",
                                )
                            )
                        )
                        logger.info('找到电子采购按钮')
                        e_procurement_button.click()

                        try:
                            self.wait.until(EC.number_of_windows_to_be(2))
                            self.driver.switch_to.window(self.driver.window_handles[-1])
                            self.wait.until(
                                EC.presence_of_element_located((By.TAG_NAME, 'body'))
                            )
                            logger.info('电子采购页面已加载')
                            sleep(5)
                            return True
                        except TimeoutException:
                            logger.warning('等待新窗口超时，尝试直接继续')
                            if len(self.driver.window_handles) > 1:
                                self.driver.switch_to.window(
                                    self.driver.window_handles[-1]
                                )
                                return True
                            raise Exception('无法切换到新窗口')

                    elif login_result == 'captcha_error':
                        logger.warning(
                            f'验证码错误，尝试 {attempt + 1}/{self.config.MAX_RETRIES}'
                        )
                        if attempt < self.config.MAX_RETRIES - 1:
                            # 短暂等待后重试
                            time.sleep(0.5)
                            continue
                        else:
                            raise Exception('验证码多次错误，登录失败')

                    elif login_result == 'other_error':
                        error_msg = self._get_error_message()
                        logger.error(f'登录失败，错误信息: {error_msg}')
                        raise Exception(f'登录失败: {error_msg}')

                    else:
                        logger.warning('登录结果未知，继续重试')
                        if attempt < self.config.MAX_RETRIES - 1:
                            time.sleep(1)
                            continue
                        else:
                            raise Exception('登录超时或失败')

                except Exception as e:
                    logger.warning('登录尝试 %d 失败: %s', attempt + 1, e)
                    if attempt == self.config.MAX_RETRIES - 1:
                        log_exception_location(
                            'login', f'登录重试{self.config.MAX_RETRIES}次后仍失败'
                        )
                        raise

            return False
        """
        except Exception as e:
            log_exception_location('login', '登录过程发生严重错误')
            logger.error('登录过程发生错误: %s', e)
            raise

    def _check_login_result(self, timeout=15) -> str:  # 增加超时时间从8秒到15秒
        """
        快速检查登录结果
        :param timeout: 超时时间（秒）
        :return: 'success', 'captcha_error', 'other_error', 'timeout'
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # 检查是否有错误消息
                error_elements = self.driver.find_elements(
                    By.CLASS_NAME, 'el-message__content'
                )
                if error_elements:
                    error_text = error_elements[0].text
                    if '验证码错误' in error_text or '验证码不正确' in error_text:
                        logger.info('检测到验证码错误消息')
                        return 'captcha_error'
                    elif error_text:
                        logger.info(f'检测到其他错误消息: {error_text}')
                        return 'other_error'

                # 检查是否进入了主页面（登录成功）
                try:
                    e_procurement_button = self.driver.find_element(
                        By.XPATH,
                        "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]",
                    )
                    if e_procurement_button.is_displayed():
                        logger.info('检测到电子采购按钮，登录成功')
                        return 'success'
                except Exception:
                    pass

                # 检查登录按钮状态（如果还在登录页面）
                try:
                    login_button = self.driver.find_element(By.CLASS_NAME, 'loginBtn')
                    if login_button.is_displayed():
                        # 还在登录页面，检查是否有加载状态
                        button_text = login_button.text or login_button.get_attribute(
                            'value'
                        )
                        if (
                            '登录中' in button_text
                            or 'loading' in login_button.get_attribute('class').lower()
                        ):
                            # 正在登录中，继续等待
                            logger.debug('登录按钮显示正在加载中...')
                            pass
                        else:
                            # 登录按钮恢复正常，可能登录失败
                            time.sleep(0.5)  # 短暂等待确保页面状态稳定
                    else:
                        # 登录按钮不可见，可能已经跳转
                        logger.debug('登录按钮不可见，可能正在跳转...')
                        pass
                except Exception:
                    # 找不到登录按钮，可能已经跳转
                    logger.debug('未找到登录按钮，可能已经跳转...')
                    pass

                # 检查页面URL是否发生变化
                try:
                    current_url = self.driver.current_url
                    if 'portal.crrcgo.cc' in current_url:
                        logger.debug('检测到URL已跳转到portal页面')
                        # 给页面多一点时间加载完全
                        time.sleep(2)
                        # 再次检查电子采购按钮
                        try:
                            e_procurement_button = self.driver.find_element(
                                By.XPATH,
                                "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]",
                            )
                            if e_procurement_button.is_displayed():
                                logger.info('URL跳转后找到电子采购按钮，登录成功')
                                return 'success'
                        except Exception:
                            logger.debug('URL已跳转但尚未找到电子采购按钮，继续等待...')
                except Exception:
                    pass

                time.sleep(0.5)  # 短暂等待后重新检查

            except Exception as e:
                logger.debug(f'检查登录结果时出现异常: {e}')
                time.sleep(0.5)

        logger.warning(f'登录结果检查超时({timeout}秒)')
        return 'timeout'

    def _get_error_message(self) -> str:
        """
        获取页面上的错误消息
        :return: 错误消息文本
        """
        try:
            error_selectors = [
                '.el-message__content',
                '.error-message',
                '.alert-danger',
                "[class*='error']",
                '.message',
            ]

            for selector in error_selectors:
                try:
                    error_elements = self.driver.find_elements(
                        By.CSS_SELECTOR, selector
                    )
                    for element in error_elements:
                        if element.is_displayed() and element.text:
                            return element.text
                except Exception:
                    continue

            return '未知错误'

        except Exception as e:
            logger.debug(f'获取错误消息失败: {e}')
            return '获取错误消息失败'

    def navigate_to_contracts(self) -> None:
        """
        简化版：导航到合同备案页面
        步骤：
        1. 点击左侧菜单的电子合同管理
        2. 点击采购合同备案
        3. 切换到tab-content-95130002的iframe
        """
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        try:
            menus = self.driver.find_elements(By.CLASS_NAME, 'eachModule')
            for menu in menus:
                if '电子采购' in menu.text:
                    menu.click()
                    break
            sleep(2)
            handles = self.driver.window_handles
            self.driver.switch_to.window(handles[-1])
            # 等待并点击电子合同管理菜单
            contract_menu = self.wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, '//*[@id="theme-side"]/div[1]/div[11]')
                )
            )
            contract_menu.click()
            # 等待并点击采购合同备案菜单
            sub_menu = self.wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, '//*[@id="theme-side"]/div[1]/div[11]/div/div[2]/a')
                )
            )
            sub_menu.click()
            # 等待页面加载
            sleep(2)
            # 切换到合同备案iframe
            self.driver.switch_to.default_content()
            iframe = self.wait.until(
                EC.presence_of_element_located((By.ID, 'tab-content-95130002'))
            )
            self.driver.switch_to.frame(iframe)
        except Exception as e:
            log_exception_location(
                'navigate_to_contracts', '导航到合同备案页面时发生错误'
            )
            logger.error(f'导航到合同备案页面时发生错误: {e}')
            raise

    def _debug_print_menu_elements(self):
        """调试方法：打印页面可用的菜单元素"""
        try:
            logger.debug('开始调试打印页面菜单元素...')

            # 查找侧边栏菜单
            debug_script = """
            var menuInfo = {
                themeElements: [],
                contractElements: [],
                allLinks: []
            };
            
            // 查找主题侧边栏
            var themeSide = document.getElementById('theme-side');
            if (themeSide) {
                var divs = themeSide.querySelectorAll('div');
                for (var i = 0; i < Math.min(20, divs.length); i++) {
                    var div = divs[i];
                    var text = (div.textContent || div.innerText || '').trim();
                    if (text && text.length < 50) {
                        menuInfo.themeElements.push({
                            index: i,
                            text: text,
                            id: div.id || '',
                            className: div.className || ''
                        });
                    }
                }
            }
            
            // 查找包含"合同"的元素
            var contractElements = document.querySelectorAll('*');
            for (var j = 0; j < contractElements.length; j++) {
                var elem = contractElements[j];
                var text = (elem.textContent || elem.innerText || '').trim();
                if (text.includes('合同') && text.length < 30) {
                    menuInfo.contractElements.push({
                        tagName: elem.tagName,
                        text: text,
                        id: elem.id || '',
                        className: elem.className || ''
                    });
                }
            }
            
            // 查找所有链接
            var links = document.querySelectorAll('a');
            for (var k = 0; k < Math.min(10, links.length); k++) {
                var link = links[k];
                var text = (link.textContent || link.innerText || '').trim();
                var href = link.getAttribute('href') || '';
                if (text || href) {
                    menuInfo.allLinks.push({
                        text: text,
                        href: href,
                        id: link.id || '',
                        className: link.className || ''
                    });
                }
            }
            
            return menuInfo;
            """

            menu_info = self.driver.execute_script(debug_script)
            logger.debug(f'页面菜单调试信息: {menu_info}')

        except Exception as e:
            logger.debug(f'调试打印菜单元素失败: {e}')

    def _refresh_and_wait(self):
        """刷新页面并等待加载完成"""
        try:
            logger.info('正在刷新页面...')
            self.driver.refresh()
            sleep(3)  # 等待页面开始加载

            # 等待页面加载完成
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, 'body')))
            sleep(2)  # 额外等待确保页面完全加载

            logger.info('页面刷新完成')
        except Exception as e:
            log_exception_location('_refresh_and_wait', '页面刷新时出现异常')
            logger.warning(f'页面刷新时出现异常: {e}')

    def crawl_data(self) -> None:
        try:
            self.setup_driver()
            if not self.login():
                raise Exception('登录失败')
            self.navigate_to_contracts()
            self.write_to_tables()

        except Exception as e:
            log_exception_location('crawl_data', '数据抓取过程发生错误')
            logger.error('数据抓取过程发生错误: %s', e)
            raise
        finally:
            if self.driver:
                try:
                    self.driver.quit()
                    logger.info('浏览器已关闭')
                except Exception as e:
                    log_exception_location('crawl_data', '关闭浏览器时发生错误')
                    logger.warning(f'关闭浏览器时发生错误: {e}')

    def write_to_tables(self) -> None:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        try:
            with sqlite3.connect(
                r'd:\user\PythonProject\ZConline\app.db', timeout=60
            ) as conn:
                conn.execute('PRAGMA journal_mode=WAL;')
                cursor = conn.cursor()

                page = 1

                # 先尝试跳转到第{page}页
                logger.info('尝试直接跳转到开始页...')
                if not self._ensure_main_iframe():
                    logger.error('无法切换到主表格iframe，无法进行跳页操作')
                    return

                if self._jump_to_page(page):
                    logger.info(f'成功跳转到第{page}页，从第{page}页开始处理')
                    # 等待页面完全稳定
                    sleep(3)
                else:
                    logger.warning(f'跳转到第{page}页失败，从第{page}页开始处理')

                while True:
                    try:
                        # 确保在主表格iframe中（但不要关闭所有iframe！）
                        logger.debug(f'开始处理第{page}页，确保在主表格iframe中')
                        if not self._ensure_main_iframe():
                            logger.error(f'无法切换到主表格iframe，跳过第{page}页')
                            break

                        # 等待表格数据区加载
                        self.wait.until(
                            lambda d: d.find_element(
                                By.CSS_SELECTOR, 'div.mini-grid-rows-view'
                            )
                        )

                        # 额外等待确保页面数据完全稳定
                        sleep(2)

                        # 获取所有行的基本信息（只获取行ID和必要信息，避免陈旧引用）
                        row_infos = self._get_current_page_row_infos()

                        if not row_infos:
                            logger.info('未找到数据行，可能已到最后一页，退出循环')
                            break

                        logger.info(
                            f'第{page}页开始逐行检查，共找到 {len(row_infos)} 行数据'
                        )

                        # 处理每一行
                        for i, row_info in enumerate(row_infos):
                            try:
                                row_id = row_info['row_id']
                                contract_no = row_info['contract_no']
                                audit_status = row_info['audit_status']

                                logger.debug(
                                    f'第{page}页第{i + 1}行: 合同号={contract_no}, 状态={audit_status}'
                                )

                                if audit_status == '编辑中' and contract_no:
                                    logger.info(
                                        f'发现编辑中的合同: {contract_no}，检查是否有对应文件'
                                    )
                                    cursor.execute(
                                        'SELECT 合同文件名 FROM 品类及合同文件 WHERE 合同编号=?',
                                        (contract_no,),
                                    )
                                    result = cursor.fetchone()
                                    if (
                                        result
                                        and result[0]
                                        and str(result[0]).strip() != ''
                                    ):
                                        logger.info(
                                            f'✅ 找到需要处理的合同: {contract_no}，文件名: {result[0]}'
                                        )

                                        # 在点击编辑按钮前，确保在主表格iframe中
                                        if not self._ensure_main_iframe():
                                            logger.warning(
                                                f'合同 {contract_no}: 无法确保在主表格iframe中，跳过'
                                            )
                                            continue
                                        sleep(1)
                                        # 使用行ID精确定位并点击编辑按钮
                                        edit_clicked = (
                                            self._click_edit_button_by_row_id(
                                                row_id, contract_no
                                            )
                                        )

                                        if edit_clicked:
                                            logger.info(
                                                f'成功点击合同 {contract_no} 的编辑按钮'
                                            )

                                            # 等待编辑页面加载
                                            sleep(3)

                                            # 切换到合同编辑iframe检测物料
                                            contract_edit_iframe_name = self._get_current_contract_edit_iframe_name()
                                            if contract_edit_iframe_name:
                                                self.driver.switch_to.frame(
                                                    contract_edit_iframe_name
                                                )
                                                logger.info(
                                                    f'合同 {contract_no}: 已切换到合同编辑iframe进行物料检测'
                                                )

                                            Companyinput = self.driver.find_element(
                                                By.ID, 'sellnsrsbh$text'
                                            )
                                            CompanyID = Companyinput.get_attribute(
                                                'value'
                                            )
                                            contains_digit = bool(
                                                re.search(r'\d', CompanyID)
                                            )
                                            if not contains_digit:
                                                sellunitname = self.driver.find_element(
                                                    By.ID, 'sellunitname$text'
                                                ).get_attribute('value')
                                                button = self.driver.find_element(
                                                    By.XPATH,
                                                    '//*[@id="mini-5$body$1"]/div/div[2]/div[2]/div/div/div[2]/div[1]/a/span',
                                                )
                                                button.click()

                                                contract_edit_iframe_name = self._get_current_contract_edit_iframe_name()
                                                if contract_edit_iframe_name:
                                                    self.driver.switch_to.frame(
                                                        contract_edit_iframe_name
                                                    )
                                                    logger.info(
                                                        f'合同 {contract_no}: 已切换到挑选供应商iframe'
                                                    )

                                                dwmc = self.driver.find_element(
                                                    By.ID, 'dwmc$text'
                                                )
                                                dwmc.clear()
                                                dwmc.send_keys(sellunitname)
                                                dwmc.send_keys(Keys.ENTER)
                                                sleep(0.5)
                                                self.driver.find_element(
                                                    By.ID, 'mini-6$checkcolumn$21$1'
                                                ).click()
                                                self.driver.find_element(
                                                    By.XPATH,
                                                    '/html/body/div[2]/div[3]/div/a/span',
                                                ).click()

                                                contract_edit_iframe_name = self._get_current_contract_edit_iframe_name()
                                                if contract_edit_iframe_name:
                                                    self.driver.switch_to.frame(
                                                        contract_edit_iframe_name
                                                    )
                                                    logger.info(
                                                        f'合同 {contract_no}: 已切换到合同编辑iframe进行物料检测'
                                                    )

                                            # 首先检测是否已存在物料F
                                            material_exists = False
                                            contract_ok = False
                                            try:
                                                # 查找class name为"mini-grid-table mini-grid-rowstable"的table

                                                # 1. 先定位rows-view
                                                rows_view = self.driver.find_element(
                                                    By.CSS_SELECTOR,
                                                    'div.mini-grid-rows-view',
                                                )
                                                self.driver.execute_script(
                                                    'arguments[0].scrollIntoView({block: "end", behavior: "smooth"});',
                                                    rows_view,
                                                )
                                                sleep(1)  # 增加等待时间,确保滚动完成

                                                # 2. 在rows-view下找table
                                                """
                                                table = rows_view.find_element(
                                                    By.CSS_SELECTOR,
                                                    'table.mini-grid-table.mini-grid-rowstable',
                                                )

                                                # 3. 在table下找所有数据行
                                                trs = table.find_elements(
                                                    By.CSS_SELECTOR,
                                                    'tr.mini-grid-row, tr.mini-grid-row-alt',
                                                )

                                                # 4. 遍历数据行
                                                # for tr in trs:
                                                #     print(
                                                #         tr.get_attribute('outerHTML'),
                                                #         tr.is_displayed(),
                                                #     )
                                                if len(trs) == 1:
                                                    tr_id = trs[0].get_attribute('id')
                                                    div_id = (
                                                        tr_id.replace(
                                                            'row2', 'checkcolumn'
                                                        )
                                                        + '$2'
                                                    )
                                                    self.driver.find_element(
                                                        By.ID, div_id
                                                    ).click()
                                                    self.driver.find_element(
                                                        By.XPATH,
                                                        '//*[@id="mini-5$body$1"]/div/div[5]/div[2]/div[1]/a[2]/span',
                                                    ).click()
                                                    e = self.driver.find_element(
                                                        By.CLASS_NAME,
                                                        'mini-button.mini-corner-all.mini-button-textOnly.mini-messagebox-button.ok.mini-button-state-primary',
                                                    )
                                                    button_id = e.get_attribute('id')
                                                    xpath = (
                                                        f'//*[@id="{button_id}"]/span'
                                                    )
                                                    self.driver.find_element(
                                                        By.XPATH, xpath
                                                    ).click()
                                                    sleep(2)
                                                    
                                                    Has_no_Material = len(trs) > 0
                                                    # material_table = self.driver.find_element(
                                                    #     By.CSS_SELECTOR,
                                                    #     'table.mini-grid-table.mini-grid-rowstable',
                                                    # )
                                                    Has_no_Material = False
                                                    """
                                                try:
                                                    hide_tr = self.driver.find_element(
                                                        By.XPATH,
                                                        '//*[@id="datagrid1"]/div/div[2]/div[2]/div[2]/table/tbody/tr[2]',
                                                    )
                                                    td = hide_tr.find_elements(
                                                        By.TAG_NAME, 'td'
                                                    )[1]
                                                    # self.driver.execute_script(
                                                    #     "arguments[0].classList.remove('mini-disabled');",
                                                    #     td,
                                                    # )
                                                    # td.click()
                                                    td_id = td.get_attribute('id')
                                                    checkbox_id = (
                                                        td_id.split('$')[0].strip()
                                                        + 'checkall'
                                                    )
                                                    sleep(0.5)
                                                    checkbox = self.driver.find_element(
                                                        By.ID, checkbox_id
                                                    )
                                                    checkbox_class_name = (
                                                        checkbox.get_attribute('class')
                                                    )
                                                    if (
                                                        'mini-disabled'
                                                        not in checkbox_class_name
                                                    ):
                                                        checkbox.click()
                                                        sleep(0.5)
                                                        delete_buttons = (
                                                            self.driver.find_elements(
                                                                By.CLASS_NAME,
                                                                'mini-button-text',
                                                            )
                                                        )
                                                        for (
                                                            delete_button
                                                        ) in delete_buttons:
                                                            text = delete_button.text
                                                            if '删除物料' in text:
                                                                delete_button.click()
                                                                break
                                                        sleep(0.5)
                                                    else:
                                                        self.driver.find_element(
                                                            By.XPATH,
                                                            '//*[@id="mini-93$body$1"]/div/div[5]/div[2]/div[1]/a[2]/span',
                                                        ).click()
                                                        sleep(1.5)
                                                        e = self.driver.find_element(
                                                            By.CLASS_NAME,
                                                            'mini-button.mini-corner-all.mini-button-textOnly.mini-messagebox-button.ok.mini-button-state-primary',
                                                        )
                                                        button_id = e.get_attribute(
                                                            'id'
                                                        )
                                                        xpath = f'//*[@id="{button_id}"]/span'
                                                        self.driver.find_element(
                                                            By.XPATH, xpath
                                                        ).click()
                                                        sleep(2)
                                                    material_exists = False
                                                except Exception:
                                                    logger.info(
                                                        f'合同 {contract_no}: 未找到物料表格，需要新增物料'
                                                    )
                                                    material_exists = False

                                            except Exception as check_e:
                                                logger.warning(
                                                    f'合同 {contract_no}: 检测物料时出错: {check_e}，默认进入新增物料流程'
                                                )
                                                material_exists = False

                                            # 根据物料检测结果决定处理流程
                                            if material_exists:
                                                # 已有物料，直接进入合同编辑页面进行完整流程处理
                                                logger.info(
                                                    f'合同 {contract_no}: 已有物料，直接处理文件上传'
                                                )

                                                # 直接调用合同编辑页面处理流程，包含文件上传、保存、提报等完整流程
                                                if self._handle_contract_edit_page_actions(
                                                    contract_no, cursor
                                                ):
                                                    logger.info(
                                                        f'合同 {contract_no}: 已有物料的合同处理完成'
                                                    )
                                                    contract_ok = True
                                                else:
                                                    logger.warning(
                                                        f'合同 {contract_no}: 已有物料的合同处理失败'
                                                    )
                                            else:
                                                # 没有物料，进入新增物料流程
                                                logger.info(
                                                    f'合同 {contract_no}: 没有物料，进入新增物料流程'
                                                )
                                                if self._enter_material_edit_frame_and_click_add():
                                                    logger.info(
                                                        f'合同 {contract_no}: 已进入新增物料页面'
                                                    )
                                                    sleep(2)

                                                    # 填写新增物料数据
                                                    if self._fill_material_data(
                                                        cursor, contract_no
                                                    ):
                                                        logger.info(
                                                            f'合同 {contract_no}: 新增物料数据填写完成'
                                                        )

                                                        material_exists = True
                                                        # 新增物料完成后，返回合同编辑页面进行文件上传和提交
                                                        logger.info(
                                                            f'合同 {contract_no}: 新增物料完成，开始处理文件上传和提交'
                                                        )

                                                        if self._handle_contract_edit_page_actions(
                                                            contract_no, cursor
                                                        ):
                                                            logger.info(
                                                                f'合同 {contract_no}: 新增物料的合同处理完成'
                                                            )
                                                            material_exists = True
                                                            contract_ok = True
                                                        else:
                                                            logger.warning(
                                                                f'合同 {contract_no}: 新增物料的合同处理失败'
                                                            )
                                                            material_exists = False
                                                    else:
                                                        logger.warning(
                                                            f'合同 {contract_no}: 新增物料数据填写失败'
                                                        )
                                                        material_exists = False
                                                else:
                                                    logger.warning(
                                                        f'合同 {contract_no}: 无法进入新增物料页面'
                                                    )
                                                    material_exists = False
                                            if contract_ok and material_exists:
                                                self._handle_contract_submit(
                                                    contract_no, cursor
                                                )

                                                self._handle_approval_dialog(
                                                    contract_no
                                                )
                                        else:
                                            logger.warning(
                                                f'无法点击合同 {contract_no} 的编辑按钮，跳过该合同'
                                            )

                                        # 处理完每个合同后，确保回到主表格iframe
                                        # 这里会清理任何剩余的弹出iframe
                                        logger.debug(
                                            f'合同 {contract_no} 处理完成，清理弹出iframe并回到主表格'
                                        )
                                        self._close_popup_iframes_only()  # 只关闭弹出iframe，不关闭主表格iframe
                                        self._ensure_main_iframe()
                                        sleep(1)  # 短暂等待，避免操作过快
                                    else:
                                        logger.info(
                                            f'❌ 合同 {contract_no} 无对应文件，跳过'
                                        )
                                else:
                                    logger.info(
                                        f"⏭️ 合同 {contract_no} 状态为 '{audit_status}'，非编辑中状态，跳过"
                                    )
                            except Exception as row_e:
                                logger.error(
                                    f'处理行 {row_info.get("row_id", "unknown")} 时出错: {row_e}'
                                )

                                # 出错后也要确保回到主表格iframe
                                try:
                                    self._close_popup_iframes_only()
                                    self._ensure_main_iframe()
                                except Exception:
                                    pass
                                continue

                        # 翻页逻辑
                        try:
                            # 翻页前只清理弹出iframe，保持主表格iframe
                            logger.debug(
                                f'第{page}页处理完成，准备翻页前清理弹出iframe'
                            )
                            self._close_popup_iframes_only()

                            # 确保在主表格iframe中
                            if not self._ensure_main_iframe():
                                logger.error('翻页前无法切换到主表格iframe，退出循环')
                                break

                            # 检查并处理遮罩层
                            self._handle_mask_if_present()

                            # 优先尝试直接跳转到下一页
                            next_page = page + 1
                            if self._jump_to_page(next_page):
                                page = next_page
                                logger.info(f'直接跳转成功，现在在第{page}页')
                            else:
                                # 如果直接跳转失败，使用传统的点击下一页按钮
                                logger.info('直接跳页失败，尝试点击下一页按钮')
                                if not self._safe_click_next_page():
                                    logger.info('已到最后一页，退出循环')
                                    break
                                page += 1
                                logger.info(f'点击下一页成功，现在在第{page}页')

                            # 等待新一页数据区加载
                            self.wait.until(
                                lambda d: d.find_element(
                                    By.CSS_SELECTOR, 'div.mini-grid-rows-view'
                                )
                            )
                            sleep(3)  # 增加等待时间，确保数据完全加载并稳定

                        except Exception as e:
                            logger.info(f'翻页异常，可能已到最后一页: {e}')
                            break
                    except Exception as e:
                        log_exception_location(
                            'write_to_tables', f'分页主循环异常，页码：{page}'
                        )
                        logger.error(f'分页主循环异常: {e}')
                        break

        except Exception as e:
            log_exception_location('write_to_tables', 'write_to_tables执行失败')
            logger.error(f'write_to_tables执行失败: {e}')
            raise

    def _record_successful_contract(
        self, cursor, contract, provider, reg_str, end_str, notax_amount
    ) -> None:
        """记录成功处理的合同到数据库"""
        try:
            # 准备数据字典
            data = {
                '合同编号': contract.get('conid'),
                '合同名称': contract.get('conname'),
                '供应商': contract.get('provider'),
                '合同金额': contract.get('conmoney'),
                '税率': contract.get('taxrate'),
                '不含税金额': notax_amount,
                '组织机构代码': provider.get('Organization_Code'),
                '统一社会信用代码': provider.get('Tax_Identification_Number'),
                '合同开始日期': reg_str,
                '合同结束日期': end_str,
                '登记时间': contract.get('registrationtime'),
                '合同状态': '已保存',
                '处理时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }

            # 构建SQL语句
            fields = list(data.keys())
            values = list(data.values())
            fields_str = ', '.join([f'"{field}"' for field in fields])
            placeholders = ', '.join(['?' for _ in values])

            sql = f'INSERT OR REPLACE INTO 已备案合同明细 ({fields_str}) VALUES ({placeholders})'

            robust_execute(cursor, sql, values)
            logger.info(f'[成功] 合同记录已保存到数据库: {contract.get("conid")}')

        except Exception as e:
            log_exception_location(
                '_record_successful_contract', '记录成功合同时发生错误'
            )
            logger.error(f'记录成功合同时发生错误: {e}')
            raise

    def _record_failed_contract(self, cursor, contract_no: str, error_msg: str) -> None:
        """记录失败的合同"""
        try:
            # 首先尝试添加必要的列（如果不存在）
            try:
                robust_execute(
                    cursor, 'ALTER TABLE 已备案合同明细 ADD COLUMN 错误信息 TEXT'
                )
                logger.info('已添加错误信息列到数据库表')
            except sqlite3.OperationalError as e:
                if 'duplicate column name' not in str(e).lower():
                    logger.warning(f'添加错误信息列时出现警告: {e}')

            try:
                robust_execute(
                    cursor, 'ALTER TABLE 已备案合同明细 ADD COLUMN 处理时间 TEXT'
                )
                logger.info('已添加处理时间列到数据库表')
            except sqlite3.OperationalError as e:
                if 'duplicate column name' not in str(e).lower():
                    logger.warning(f'添加处理时间列时出现警告: {e}')

            robust_execute(
                cursor,
                """
                INSERT OR REPLACE INTO 已备案合同明细 
                (合同编号, 合同状态, 错误信息, 处理时间) 
                VALUES (?, ?, ?, ?)
            """,
                (
                    contract_no,
                    '处理失败',
                    error_msg,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                ),
            )
            logger.info(f'[失败] 合同失败记录已保存: {contract_no} - {error_msg}')
        except Exception as e:
            log_exception_location('_record_failed_contract', '记录失败合同时发生错误')
            logger.error(f'记录失败合同时发生错误: {e}')
            # 如果还是失败，尝试只记录基本信息
            try:
                robust_execute(
                    cursor,
                    """
                    INSERT OR REPLACE INTO 已备案合同明细 
                    (合同编号, 合同状态) 
                    VALUES (?, ?)
                """,
                    (contract_no, '处理失败'),
                )
                logger.info(f'[失败] 合同失败记录已保存（最简版本）: {contract_no}')
            except Exception as e2:
                log_exception_location(
                    '_record_failed_contract', '记录失败合同（最简版本）时发生错误'
                )
                logger.error(f'记录失败合同（最简版本）时发生错误: {e2}')
                # 不再抛出异常，避免程序中断

    def _handle_mask_if_present(self) -> bool:
        """
        检查并处理页面遮罩层
        :return: 是否成功处理遮罩层
        """
        try:
            # 检查是否有遮罩层
            mask_script = """
            var masks = document.querySelectorAll('.mini-mask-background');
            var visibleMasks = [];
            for (var i = 0; i < masks.length; i++) {
                var mask = masks[i];
                var style = window.getComputedStyle(mask);
                if (style.display !== 'none' && style.visibility !== 'hidden' && 
                    mask.offsetWidth > 0 && mask.offsetHeight > 0) {
                    visibleMasks.push({
                        id: mask.id || 'no-id',
                        className: mask.className,
                        display: style.display,
                        zIndex: style.zIndex
                    });
                }
            }
            return visibleMasks;
            """

            visible_masks = self.driver.execute_script(mask_script)

            if visible_masks:
                logger.warning(
                    f'检测到{len(visible_masks)}个可见遮罩层: {visible_masks}'
                )

                # 尝试移除遮罩层
                remove_mask_script = """
                var masks = document.querySelectorAll('.mini-mask-background');
                var removed = 0;
                for (var i = 0; i < masks.length; i++) {
                    var mask = masks[i];
                    if (mask.style.display !== 'none') {
                        mask.style.display = 'none';
                        mask.style.visibility = 'hidden';
                        removed++;
                    }
                }
                return removed;
                """

                removed_count = self.driver.execute_script(remove_mask_script)
                if removed_count > 0:
                    logger.info(f'成功移除{removed_count}个遮罩层')
                    sleep(0.5)  # 等待移除生效
                    return True
                else:
                    logger.warning('未能移除遮罩层')
                    return False
            else:
                logger.debug('未检测到遮罩层')
                return True

        except Exception as e:
            log_exception_location('_handle_mask_if_present', '处理遮罩层时发生异常')
            logger.warning(f'处理遮罩层时发生异常: {e}')
            return False

    def _safe_click_next_page(self) -> bool:
        """
        安全地点击下一页按钮
        :return: 是否成功点击（False表示已到最后一页）
        """
        try:
            # 查找下一页按钮
            next_btn_selectors = [
                'a.pagination-button.pagination-next',
                "a[action='next']",
                '.pagination-next',
                'a.mini-pager-next',
            ]

            next_btn = None
            for selector in next_btn_selectors:
                try:
                    next_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if next_btn and next_btn.is_displayed():
                        break
                except Exception:
                    continue

            if not next_btn:
                logger.info('未找到下一页按钮')
                return False

            # 检查按钮是否被禁用
            btn_classes = next_btn.get_attribute('class') or ''
            if 'disabled' in btn_classes.lower():
                logger.info('下一页按钮已禁用，已到最后一页')
                return False

            # 检查按钮是否可点击
            if not next_btn.is_enabled():
                logger.info('下一页按钮不可用，已到最后一页')
                return False

            # 尝试多种点击方法
            click_methods = [
                ('直接点击', lambda: next_btn.click()),
                (
                    'JavaScript点击',
                    lambda: self.driver.execute_script(
                        'arguments[0].click();', next_btn
                    ),
                ),
                ('Action点击', lambda: self._action_click(next_btn)),
                ('坐标点击', lambda: self._coordinate_click(next_btn)),
            ]

            for method_name, click_func in click_methods:
                try:
                    logger.debug(f'尝试使用{method_name}点击下一页按钮')
                    click_func()
                    sleep(0.5)  # 等待点击生效

                    # 验证是否成功翻页（检查页面是否有变化）
                    if self._verify_page_changed():
                        logger.info(f'使用{method_name}成功点击下一页按钮')
                        return True
                    else:
                        logger.warning(f'{method_name}点击后页面未发生变化')

                except Exception as click_e:
                    logger.warning(f'{method_name}失败: {click_e}')
                    continue

            logger.error('所有点击方法都失败了')
            return False

        except Exception as e:
            log_exception_location(
                '_safe_click_next_page', '安全点击下一页按钮时发生异常'
            )
            logger.error(f'安全点击下一页按钮时发生异常: {e}')
            return False

    def _action_click(self, element):
        """使用Action链点击元素"""
        actions = ActionChains(self.driver)
        actions.move_to_element(element).click().perform()

    def _coordinate_click(self, element):
        """使用坐标点击元素"""
        actions = ActionChains(self.driver)
        # 获取元素位置和大小
        location = element.location
        size = element.size
        # 点击元素中心
        x = location['x'] + size['width'] // 2
        y = location['y'] + size['height'] // 2
        actions.move_by_offset(x, y).click().perform()

    def _verify_page_changed(self) -> bool:
        """
        验证页面是否已发生变化（翻页成功）
        :return: 是否翻页成功
        """
        try:
            # 等待一小段时间让页面响应
            sleep(1)

            # 检查是否有加载指示器
            loading_indicators = [
                '.mini-grid-loading',
                '.loading',
                '.mini-mask',
                "[class*='loading']",
            ]

            for indicator in loading_indicators:
                try:
                    loading_element = self.driver.find_element(
                        By.CSS_SELECTOR, indicator
                    )
                    if loading_element and loading_element.is_displayed():
                        logger.debug('检测到加载指示器，等待加载完成')
                        # 等待加载完成
                        WebDriverWait(self.driver, 5).until(
                            lambda d: not d.find_element(
                                By.CSS_SELECTOR, indicator
                            ).is_displayed()
                        )
                        break
                except Exception:
                    continue

            # 检查表格数据是否重新加载
            try:
                self.wait.until(
                    lambda d: d.find_element(By.CSS_SELECTOR, 'div.mini-grid-rows-view')
                )
                return True
            except Exception:
                return False

        except Exception as e:
            logger.warning(f'验证页面变化时出错: {e}')
            return True  # 默认认为成功，避免程序卡住

    def _click_edit_button_by_row_id(self, row_id: str, contract_no: str) -> bool:
        """
        根据行ID精确定位并点击编辑按钮
        :param row_id: 行的ID，格式如 "mini-40$row2$80"
        :param contract_no: 合同编号（用于日志）
        :return: 是否成功点击
        """
        try:
            logger.debug(f'使用行ID {row_id} 精确定位合同 {contract_no} 的编辑按钮')

            # 先关闭所有弹出iframe
            self._close_all_pop_iframes()

            # 确保在正确的iframe中
            self.driver.switch_to.default_content()
            iframe = self.wait.until(
                EC.presence_of_element_located((By.ID, 'tab-content-95130002'))
            )
            self.driver.switch_to.frame(iframe)

            # 先滚动表格确保所有行可见
            try:
                rows_view = self.driver.find_element(
                    By.CSS_SELECTOR, 'div.mini-grid-rows-view'
                )
                self.driver.execute_script(
                    'arguments[0].scrollTop = arguments[0].scrollHeight;', rows_view
                )
                sleep(0.5)
                self.driver.execute_script('arguments[0].scrollTop = 0;', rows_view)
                sleep(0.5)
            except Exception as e:
                logger.debug(f'滚动表格时出错: {e}')

            # 尝试多种方法查找行元素
            row_element = None

            # 方法1：直接通过ID查找
            try:
                row_element = self.driver.find_element(By.ID, row_id)
                logger.debug(f'通过ID直接找到行元素: {row_id}')
            except:
                logger.debug(f'直接通过ID未找到行元素: {row_id}')

            # 方法2：通过CSS选择器查找
            if not row_element:
                try:
                    escaped_row_id = row_id.replace('$', '\\$')
                    row_element = self.driver.find_element(
                        By.CSS_SELECTOR, f'tr[id="{escaped_row_id}"]'
                    )
                    logger.debug(f'通过CSS选择器找到行元素: {row_id}')
                except:
                    logger.debug(f'通过CSS选择器未找到行元素: {row_id}')

            # 方法3：通过XPath查找
            if not row_element:
                try:
                    row_element = self.driver.find_element(
                        By.XPATH, f'//tr[@id="{row_id}"]'
                    )
                    logger.debug(f'通过XPath找到行元素: {row_id}')
                except:
                    logger.debug(f'通过XPath未找到行元素: {row_id}')

            # 方法4：遍历所有行查找匹配的ID
            if not row_element:
                try:
                    table = self.driver.find_element(
                        By.CSS_SELECTOR, 'table.mini-grid-table.mini-grid-rowstable'
                    )
                    all_rows = table.find_elements(
                        By.CSS_SELECTOR, 'tr.mini-grid-row, tr.mini-grid-row-alt'
                    )
                    for row in all_rows:
                        if row.get_attribute('id') == row_id:
                            row_element = row
                            logger.debug(f'通过遍历找到行元素: {row_id}')
                            break
                except:
                    logger.debug('遍历查找行元素失败')

            if not row_element:
                logger.error(f'合同 {contract_no}: 所有方法都无法找到行ID {row_id}')
                return False

            # 滚动到行元素，确保完全可见
            self.driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center'});", row_element
            )
            sleep(0.3)

            # 在该行中查找第3个单元格（操作列，索引为2）
            # 根据用户提供的HTML，编辑按钮在第3个td中
            cells = row_element.find_elements(By.CSS_SELECTOR, 'td.mini-grid-cell')
            if len(cells) < 3:
                logger.error(
                    f'合同 {contract_no}: 行 {row_id} 列数不足，只有 {len(cells)} 列'
                )
                return False

            # 获取操作列（第3列，索引2）
            op_cell = cells[1]  # 根据HTML结构，编辑按钮在第3列

            # 在操作列中查找编辑按钮
            edit_buttons = op_cell.find_elements(
                By.CSS_SELECTOR, 'div.action-icon.icon-edit'
            )

            if not edit_buttons:
                logger.warning(
                    f'合同 {contract_no}: 在行 {row_id} 的操作列中未找到编辑按钮'
                )
                # 输出调试信息
                cell_html = op_cell.get_attribute('outerHTML')
                logger.debug(f'操作列HTML: {cell_html[:200]}...')
                return False

            # 尝试点击第一个找到的编辑按钮
            edit_button = edit_buttons[0]

            # 检查按钮是否可见和可点击
            if not edit_button.is_displayed():
                logger.warning(f'合同 {contract_no}: 编辑按钮不可见')
                return False

            if not edit_button.is_enabled():
                logger.warning(f'合同 {contract_no}: 编辑按钮不可点击')
                return False

            # 再次滚动到按钮位置，确保绝对可见
            self.driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center'});", edit_button
            )
            sleep(0.5)

            # 尝试多种点击方法
            click_methods = [
                ('直接点击', lambda: edit_button.click()),
                (
                    'JavaScript点击',
                    lambda: self.driver.execute_script(
                        'arguments[0].click();', edit_button
                    ),
                ),
                (
                    'ActionChains点击',
                    lambda: ActionChains(self.driver)
                    .move_to_element(edit_button)
                    .click()
                    .perform(),
                ),
            ]

            for method_name, click_func in click_methods:
                try:
                    logger.debug(f'尝试使用{method_name}点击编辑按钮')
                    click_func()
                    sleep(0.5)

                    # 验证是否成功（检查是否出现编辑页面或弹窗）
                    if self._verify_edit_page_opened():
                        logger.info(
                            f'合同 {contract_no}: 使用{method_name}成功点击编辑按钮'
                        )
                        return True
                    else:
                        logger.debug(
                            f'合同 {contract_no}: {method_name}点击后未检测到编辑页面'
                        )

                except Exception as click_e:
                    logger.warning(f'合同 {contract_no}: {method_name}失败: {click_e}')
                    continue

            logger.error(f'合同 {contract_no}: 所有点击方法都失败')
            return False

        except Exception as e:
            log_exception_location(
                '_click_edit_button_by_row_id',
                f'根据行ID点击编辑按钮失败，行ID：{row_id}',
            )
            logger.error(f'根据行ID {row_id} 点击编辑按钮时发生异常: {e}')
            return False

    def _verify_edit_page_opened(self) -> bool:
        """
        验证编辑页面是否已打开
        :return: 是否成功打开编辑页面
        """
        try:
            # 等待短暂时间让页面响应
            sleep(0.5)

            # 检查是否有新的iframe出现（编辑页面通常在iframe中）
            try:
                self.driver.switch_to.default_content()
                # 查找可能的编辑iframe
                edit_iframes = self.driver.find_elements(
                    By.CSS_SELECTOR, "iframe[name^='mini-iframe-']"
                )
                for iframe in edit_iframes:
                    iframe_name = iframe.get_attribute('name')
                    if iframe_name and iframe_name != 'tab-content-95130002':
                        logger.debug(f'检测到可能的编辑iframe: {iframe_name}')
                        return True
            except Exception:
                pass

            # 检查是否有编辑窗口
            try:
                edit_windows = self.driver.find_elements(
                    By.CSS_SELECTOR, '.mini-window'
                )
                for window in edit_windows:
                    if window.is_displayed():
                        logger.debug('检测到编辑窗口')
                        return True
            except Exception:
                pass

            # 检查页面标题是否包含编辑相关关键词
            try:
                page_title = self.driver.title
                if any(
                    keyword in page_title
                    for keyword in ['编辑', '修改', '详情', 'edit']
                ):
                    logger.debug(f'页面标题包含编辑关键词: {page_title}')
                    return True
            except Exception:
                pass

            return False

        except Exception as e:
            logger.debug(f'验证编辑页面是否打开时出错: {e}')
            return False

    def _enter_material_edit_frame_and_click_add(self) -> bool:
        """
        等待合同编辑页面完全加载，动态检测iframe后点击"新增物料"按钮
        :return: 是否成功进入新增物料页面
        """
        try:
            logger.info('等待合同编辑页面加载...')

            # 1. 首先等待合同编辑页面加载
            if not self._wait_for_contract_edit_page_loaded():
                logger.error('合同编辑页面未能成功加载')
                return False

            # 2. 等待合同编辑iframe出现并可用
            contract_edit_iframe_name = self._get_current_contract_edit_iframe_name()
            # self._wait_for_contract_edit_iframe_ready()
            if not contract_edit_iframe_name:
                logger.error('合同编辑iframe未能成功加载')
                return False

            # 3. 进入合同编辑iframe并点击"新增物料"按钮
            try:
                logger.info(f'尝试切换到{contract_edit_iframe_name}...')
                WebDriverWait(self.driver, 10).until(
                    EC.frame_to_be_available_and_switch_to_it(
                        (By.NAME, contract_edit_iframe_name)
                    )
                )
                logger.info(f'已成功切换到{contract_edit_iframe_name}')

                # 等待iframe内容加载
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, 'body'))
                )
                sleep(2)  # 额外等待确保页面渲染完成

            except Exception as e:
                logger.error(f'无法切换到{contract_edit_iframe_name}: {e}')
                return False

            # 4. 查找并点击"新增物料"按钮
            add_button = self._find_add_material_button()
            if not add_button:
                logger.error("未找到'新增物料'按钮")
                return False

            # 5. 点击按钮（不使用可能破坏iframe的方法）
            try:
                logger.info("尝试点击'新增物料'按钮...")
                add_button.click()
                logger.info("成功点击'新增物料'按钮")
            except Exception as e:
                logger.warning(f'直接点击失败，尝试JavaScript点击: {e}')
                try:
                    self.driver.execute_script('arguments[0].click();', add_button)
                    logger.info("JavaScript点击'新增物料'按钮成功")
                except Exception as js_e:
                    logger.error(f'JavaScript点击也失败: {js_e}')
                    return False

            # 6. 切回默认内容，等待新增物料页面iframe出现
            self.driver.switch_to.default_content()

            # 7. 根据合同编辑iframe名称推算新增物料iframe名称
            material_iframe_name = self._get_current_material_add_iframe_name(
                contract_edit_iframe_name
            )

            if not material_iframe_name:
                logger.error('无法推算新增物料iframe名称')
                return False
            material_iframe_name1 = material_iframe_name.replace(
                material_iframe_name.split('-')[-1],
                str(int(material_iframe_name.split('-')[-1]) + 2),
            )

            logger.info(
                f'已切回默认内容，等待新增物料页面{material_iframe_name}加载...'
            )

            # 8. 等待新增物料iframe出现
            try:
                WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.NAME, material_iframe_name))
                )
                logger.info(f'找到新增物料页面{material_iframe_name}')
                WebDriverWait(self.driver, 15).until(
                    EC.frame_to_be_available_and_switch_to_it(
                        (By.NAME, material_iframe_name)
                    )
                )
            except TimeoutException:
                try:
                    WebDriverWait(self.driver, 20).until(
                        EC.presence_of_element_located((By.NAME, material_iframe_name1))
                    )
                    logger.info(f'找到新增物料页面{material_iframe_name}')
                    # 确保iframe完全加载后再切换
                    WebDriverWait(self.driver, 15).until(
                        EC.frame_to_be_available_and_switch_to_it(
                            (By.NAME, material_iframe_name1)
                        )
                    )
                    logger.info(f'已切换到新增物料页面{material_iframe_name}')
                except TimeoutException:
                    logger.error(f'等待新增物料页面{material_iframe_name}超时')
                    return False
            # 9. 验证新增物料页面加载完成
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, 'body'))
                )
                sleep(2)  # 额外等待确保页面渲染完成
                logger.info('新增物料页面已完全加载，准备填写数据')
                return True

            except Exception as e:
                logger.error(f'新增物料页面验证失败: {e}')
                return False

        except Exception as e:
            log_exception_location(
                '_enter_material_edit_frame_and_click_add', '进入新增物料页面失败'
            )
            logger.error(f'进入新增物料页面时发生异常: {e}')
            return False

    def _wait_for_contract_edit_page_loaded(self) -> bool:
        """
        等待合同编辑页面完全加载（通过动态检测iframe和页面元素）
        :return: 是否成功加载
        """
        try:
            logger.debug('等待合同编辑页面加载完成...')

            # 等待合同编辑iframe出现，最多等待30秒
            for attempt in range(60):  # 30秒，每500ms检查一次
                try:
                    # 1. 动态获取当前合同编辑iframe名称
                    contract_edit_iframe_name = (
                        self._get_current_contract_edit_iframe_name()
                    )

                    if contract_edit_iframe_name:
                        logger.info(
                            f'检测到合同编辑iframe: {contract_edit_iframe_name}'
                        )

                        # 2. 尝试切换到合同编辑iframe检查内容
                        try:
                            iframe_element = self.driver.find_element(
                                By.NAME, contract_edit_iframe_name
                            )
                            self.driver.switch_to.frame(iframe_element)

                            # 3. 检查iframe内是否有body元素（基本加载完成）
                            WebDriverWait(self.driver, 5).until(
                                EC.presence_of_element_located((By.TAG_NAME, 'body'))
                            )

                            # 4. 检查是否有合同编辑相关的元素
                            page_indicators = [
                                "//button[contains(text(), '新增物料')]",
                                "//span[contains(text(), '新增物料')]",
                                "//input[contains(@value, '新增物料')]",
                                "//a[contains(text(), '新增物料')]",
                                '//form',  # 表单元素
                                '//table',  # 表格元素
                                "//*[contains(text(), '物料')]",
                                "//*[contains(text(), '合同')]",
                            ]

                            page_loaded = False
                            for selector in page_indicators:
                                try:
                                    element = self.driver.find_element(
                                        By.XPATH, selector
                                    )
                                    if element:
                                        logger.info(
                                            f'检测到合同编辑页面元素: {selector}'
                                        )
                                        page_loaded = True
                                        break
                                except Exception:
                                    continue

                            # 切回默认内容
                            self.driver.switch_to.default_content()

                            if page_loaded:
                                logger.info(
                                    f'合同编辑页面已成功加载 (iframe: {contract_edit_iframe_name})'
                                )
                                return True
                            else:
                                logger.debug(
                                    f'{contract_edit_iframe_name}内容还未完全加载，继续等待...'
                                )

                        except Exception as switch_e:
                            logger.debug(
                                f'切换到{contract_edit_iframe_name}检查内容失败: {switch_e}'
                            )
                            # 确保切回默认内容
                            try:
                                self.driver.switch_to.default_content()
                            except Exception:
                                pass

                    sleep(0.5)

                except Exception as e:
                    logger.debug(f'检测合同编辑页面时出错: {e}')
                    sleep(0.5)
                    continue

            logger.warning('等待合同编辑页面加载超时')
            return False

        except Exception as e:
            logger.error(f'等待合同编辑页面加载时发生异常: {e}')
            return False

    def _fill_material_data(self, cursor, contract_no: str) -> bool:
        """
        填写新增物料页面的数据
        :param cursor: 数据库游标
        :param contract_no: 合同编号
        :return: 是否成功填写数据
        """
        fill_success = False

        # 等待新增物料页面完全加载
        try:
            sleep(2)
            # 等待"修改保存"按钮出现并可点击，这表示页面已完全加载
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="icon-save"]/span'))
            )

            logger.info(
                f'合同 {contract_no}: 新增物料页面加载完成（修改保存按钮已出现）'
            )

        except Exception as e:
            logger.warning(f'合同 {contract_no}: 等待修改保存按钮出现时出错: {e}')
            # 如果等待失败，继续尝试执行，但可能会有问题
            sleep(2)

        try:
            logger.info(f'开始为合同 {contract_no} 填写新增物料数据')

            # 从数据库获取合同对应的物料数据和合同金额
            cursor.execute(
                'SELECT 合同文件名, 品类编码, 品类名称,合同金额 FROM 品类及合同文件 WHERE 合同编号=?',
                (contract_no,),
            )
            result = cursor.fetchone()

            if not result:
                logger.error(f'合同 {contract_no}: 未找到对应的物料数据')
                return False

            # 将tuple转换为list以便修改，并设置默认值
            result = list(result)
            if not result[1] or not result[2] or not result[3]:  # 品类编码
                fill_success = False

            contract_file, category_code, category_name, contract_amount = result
            if not contract_amount:
                contract_amount = 1

            logger.info(
                f'合同 {contract_no}: 物料数据 - 文件名: {contract_file}, 品类编码: {category_code}, 品类名称: {category_name}, 合同金额: {contract_amount}'
            )

            # 等待页面完全加载
            sleep(1)

            # 填写数据的成功标志
            fill_success = True

            # 1. 填写物料数量：固定为 "1"
            if not self._fill_field_safely(
                '物料数量',
                '1',
                [
                    'shoubiaonum',
                    'quantity',
                    'amount',
                    'count',
                    'num',
                    'sl',
                    'materialQuantity',
                    'wuliaosl',
                ],
            ):
                logger.warning(f'合同 {contract_no}: 物料数量填写失败')
                fill_success = False
            else:
                self._fill_field_safely(
                    '物料数量',
                    '1',
                    [
                        'shoubiaonum',
                        'quantity',
                        'amount',
                        'count',
                        'num',
                        'sl',
                        'materialQuantity',
                        'wuliaosl',
                    ],
                )

            # 2. 选择计量单位：固定为 "ST，项"
            # unit_filled = self._fill_field_safely(
            #     "计量单位",
            #     "ST，项",
            #     [
            #         "jiliangunit",
            #         "unit",
            #         "danwei",
            #         "dw",
            #         "measureUnit",
            #         "jldw",
            #         "unitSelect",
            #     ],
            # )
            input = self.driver.find_element(By.ID, 'jiliangunit$text')
            input.clear()
            input.send_keys('项')
            sleep(0.5)
            input.send_keys(Keys.ENTER)
            sleep(0.5)

            # if not unit_filled:
            #     # 如果直接填写失败，尝试使用专门的combobox函数
            #     logger.info(
            #         f"合同 {contract_no}: 直接填写计量单位失败，尝试使用下拉选择"
            #     )
            #     unit_filled = self._fill_miniui_combobox(
            #         "计量单位", "ST，项", ["jiliangunit"]
            #     )

            # if not unit_filled:
            #     logger.warning(f"合同 {contract_no}: 计量单位填写失败")
            #     fill_success = False

            # 3. 填写单价（含税元）：使用合同金额
            if contract_amount:
                if not self._fill_field_safely(
                    '单价（含税元）',
                    contract_amount,
                    [
                        'shoubiaounitpricehastax',
                        'price',
                        'unitPrice',
                        'danjia',
                        'hanshuidanjia',
                        'priceWithTax',
                        'taxPrice',
                    ],
                ):
                    logger.warning(f'合同 {contract_no}: 单价（含税元）填写失败')
                    fill_success = False
            else:
                logger.warning(f'合同 {contract_no}: 合同金额为空，无法填写单价')
                fill_success = False

            # 4. 填写原产地：固定为 "四川省眉山市"
            if not self._fill_field_safely(
                '原产地',
                '四川省眉山市',
                ['yuanchandi', 'origin', 'originPlace', 'ycd', 'place'],
            ):
                logger.warning(f'合同 {contract_no}: 原产地填写失败')
                fill_success = False

            # 5. 选择产品平台：固定为 "设备管理"
            try:
                # 先查找产品平台的combobox容器
                platform_combobox = self.driver.find_element(By.ID, 'productpingtai')

                if platform_combobox.is_displayed():
                    # 点击触发按钮打开下拉列表
                    platform_combobox.click()
                    sleep(1)  # 等待下拉菜单出现

                    # 等待下拉列表出现
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located(
                            (By.CSS_SELECTOR, '.mini-popup .mini-listbox')
                        )
                    )

                    # 查找"设备管理"选项
                    platform_selected = False
                    try:
                        # 首先查找铁路货车选项（不管是否可见）
                        railway_option = None

                        # 尝试多种方式找到铁路货车选项
                        try:
                            railway_option = self.driver.find_element(
                                By.CSS_SELECTOR, '.mini-listbox-item[title="设备管理"]'
                            )
                        except:
                            try:
                                # 通过文本内容查找
                                options = self.driver.find_elements(
                                    By.CSS_SELECTOR, '.mini-listbox-item'
                                )
                                for option in options:
                                    if '设备管理' in option.text:
                                        railway_option = option
                                        break
                            except:
                                pass

                        if railway_option:
                            # 滚动到该选项使其可见
                            self.driver.execute_script(
                                "arguments[0].scrollIntoView({block: 'center'});",
                                railway_option,
                            )
                            sleep(0.5)  # 等待滚动完成

                            # 确保选项现在可见后再点击
                            if railway_option.is_displayed():
                                self.driver.execute_script(
                                    'arguments[0].click();', railway_option
                                )
                                platform_selected = True
                                logger.info(
                                    f"合同 {contract_no}: 成功选择产品平台 '设备管理'"
                                )
                            else:
                                # 如果滚动后仍不可见，尝试直接点击
                                self.driver.execute_script(
                                    'arguments[0].click();', railway_option
                                )
                                platform_selected = True
                                logger.info(
                                    f"合同 {contract_no}: 强制选择产品平台 '设备管理'"
                                )

                    except Exception as e:
                        logger.debug(f'合同 {contract_no}: 直接查找铁路货车失败: {e}')

                        # 如果直接查找失败，尝试滚动整个列表并遍历查找
                        try:
                            # 找到下拉列表容器
                            listbox_view = self.driver.find_element(
                                By.CSS_SELECTOR, '.mini-listbox-view'
                            )

                            # 先滚动到底部确保所有选项都加载
                            self.driver.execute_script(
                                'arguments[0].scrollTop = arguments[0].scrollHeight;',
                                listbox_view,
                            )
                            sleep(0.5)

                            # 再次查找所有选项
                            options = self.driver.find_elements(
                                By.CSS_SELECTOR, '.mini-listbox-item'
                            )

                            for option in options:
                                if '设备管理' in option.text:
                                    # 滚动到该选项
                                    self.driver.execute_script(
                                        "arguments[0].scrollIntoView({block: 'center'});",
                                        option,
                                    )
                                    sleep(0.3)

                                    self.driver.execute_script(
                                        'arguments[0].click();', option
                                    )
                                    platform_selected = True
                                    logger.info(
                                        f"合同 {contract_no}: 成功选择产品平台 '设备管理'"
                                    )
                                    break

                        except Exception as scroll_e:
                            logger.debug(
                                f'合同 {contract_no}: 滚动查找失败: {scroll_e}'
                            )

                    if not platform_selected:
                        logger.warning(
                            f"合同 {contract_no}: 未找到'设备管理'选项，尝试选择第一个可用选项"
                        )
                        # 如果没找到铁路货车，尝试点击第一个选项
                        try:
                            first_option = self.driver.find_element(
                                By.CSS_SELECTOR, '.mini-listbox-item'
                            )
                            if first_option.is_displayed():
                                self.driver.execute_script(
                                    'arguments[0].click();', first_option
                                )
                                logger.info(
                                    f'合同 {contract_no}: 已选择产品平台选项: {first_option.text}'
                                )
                            else:
                                logger.warning(
                                    f'合同 {contract_no}: 产品平台下拉列表为空'
                                )
                                fill_success = False
                        except Exception:
                            logger.warning(
                                f'合同 {contract_no}: 无法选择任何产品平台选项'
                            )
                            fill_success = False

                            sleep(0.5)  # 等待选择完成
                    else:
                        logger.warning(f'合同 {contract_no}: 产品平台输入框不可见')
                        fill_success = False

            except Exception as e:
                logger.warning(f'合同 {contract_no}: 产品平台选择失败 - {e}')
                fill_success = False

            try:
                # 查找业务类型的combobox
                business_type_combobox = self.driver.find_element(By.ID, 'yewuleixing')

                if business_type_combobox.is_displayed():
                    # 点击打开下拉列表
                    business_type_combobox.click()
                    sleep(1)  # 等待下拉菜单出现

                    # 等待mini-popup弹窗出现
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located(
                            (By.CSS_SELECTOR, '.mini-popup .mini-listbox-items')
                        )
                    )

                    # 查找并点击"新造"选项
                    business_type_selected = False
                    try:
                        # 查找包含"新造"文本的表格行，但不包含"新造+检修"
                        items = self.driver.find_elements(
                            By.CSS_SELECTOR, '.mini-listbox-item td'
                        )

                        for item in items:
                            item_text = item.text.strip()
                            if item_text == '新造':  # 精确匹配，避免选中"新造+检修"
                                # 滚动到选项可见位置
                                self.driver.execute_script(
                                    "arguments[0].scrollIntoView({block: 'center'});",
                                    item,
                                )
                                sleep(0.3)

                                # 点击选中
                                self.driver.execute_script(
                                    'arguments[0].click();', item
                                )
                                business_type_selected = True
                                logger.info(
                                    f"合同 {contract_no}: 成功选择业务类型 '新造'"
                                )
                                break

                    except Exception as select_e:
                        logger.debug(
                            f'合同 {contract_no}: 选择业务类型时出错: {select_e}'
                        )

                    if not business_type_selected:
                        logger.warning(f'合同 {contract_no}: 业务类型选择失败')
                        fill_success = False

                    sleep(0.5)  # 等待选择完成
                else:
                    logger.warning(f'合同 {contract_no}: 业务类型输入框不可见')
                    fill_success = False

            except Exception as e:
                logger.warning(f'合同 {contract_no}: 业务类型选择失败 - {e}')
                fill_success = False

            # 8. 填写品类名称（如果有对应的输入框）
            if category_name:  # and not category_success:
                if category_name.startswith('设备'):
                    category_name = '信息化软硬件及工装设备类-设备类' + category_name
                if category_name.startswith('运维'):
                    category_name = '工程类-工程基建类-' + category_name
                if not self._fill_field_safely(
                    '品类名称',
                    category_name,
                    [
                        'productcommodityguid',
                        'categoryName',
                        'category_name',
                        'pinlei_name',
                        'name',
                        'materialName',
                    ],
                ):
                    logger.debug(
                        f'合同 {contract_no}: 品类名称填写失败（可能是只读字段）'
                    )
            # 9. 填写品类编码（如果有对应的输入框）
            if category_code:
                input = self.driver.find_element(By.ID, 'productcommoditycode$text')
                input.clear()
                input.send_keys(category_code)
                input.send_keys(Keys.ENTER)

            # 等待填写完成
            sleep(2)

            # 尝试保存数据
            if self._save_material_data():
                logger.info(f'合同 {contract_no}: 新增物料数据保存成功')

                # 保存成功后，等待返回到合同编辑页面（框架18）
                if self._wait_for_return_to_contract_edit_page(contract_no):
                    logger.info(f'合同 {contract_no}: 已返回到合同编辑页面（框架18）')

                    # 在合同编辑页面进行后续操作
                    if self._handle_contract_edit_page_actions(contract_no, cursor):
                        logger.info(f'合同 {contract_no}: 合同编辑页面操作完成')
                        fill_success = True
                    else:
                        logger.warning(f'合同 {contract_no}: 合同编辑页面操作失败')
                        fill_success = False
                else:
                    logger.warning(f'合同 {contract_no}: 未能正确返回到合同编辑页面')
                    fill_success = False
            else:
                logger.warning(f'合同 {contract_no}: 新增物料数据保存失败')
                fill_success = False

            return fill_success

        except Exception as e:
            log_exception_location(
                '_fill_material_data', f'填写物料数据失败，合同号：{contract_no}'
            )
            logger.error(f'填写合同 {contract_no} 物料数据时发生异常: {e}')
            return False
        finally:
            # 不要在这里关闭iframe！合同编辑页面（iframe-18）需要保持打开状态
            # 以便后续在合同编辑页面进行其他操作
            # iframe的清理将在整个合同处理完成后进行
            try:
                logger.debug(f'合同 {contract_no}: 物料数据填写流程完成')
                # 确保回到默认内容，但不关闭任何iframe
                # self.driver.switch_to.default_content()
            except Exception as cleanup_e:
                logger.debug(f'合同 {contract_no}: 切换到默认内容时出错: {cleanup_e}')
                try:
                    self.driver.switch_to.default_content()
                except Exception:
                    pass

    def _wait_for_return_to_contract_edit_page(self, contract_no: str) -> bool:
        """
        等待从新增物料页面（框架20）返回到合同编辑页面（框架18）
        :param contract_no: 合同编号（用于日志）
        :return: 是否成功返回到合同编辑页面
        """
        try:
            logger.info(f'合同 {contract_no}: 等待返回到合同编辑页面（框架18）')

            # 等待一段时间让页面自动跳转
            sleep(3)

            # 切换到默认内容检查iframe状态
            self.driver.switch_to.default_content()

            max_wait_time = 10  # 最多等待10秒
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                try:
                    # 检查mini-iframe-20是否已经关闭
                    iframe_20_elements = self.driver.find_elements(
                        By.NAME, 'mini-iframe-20'
                    )
                    iframe_20_visible = any(
                        iframe.is_displayed() for iframe in iframe_20_elements if iframe
                    )

                    # 检查mini-iframe-18是否存在且可见
                    iframe_18_elements = self.driver.find_elements(
                        By.NAME, 'mini-iframe-18'
                    )
                    iframe_18_visible = any(
                        iframe.is_displayed() for iframe in iframe_18_elements if iframe
                    )

                    if not iframe_20_visible and iframe_18_visible:
                        logger.info(f'合同 {contract_no}: 检测到已从框架20返回到框架18')
                        return True
                    elif not iframe_20_visible and not iframe_18_visible:
                        # 两个iframe都不可见，可能已经完全关闭了编辑界面
                        logger.info(
                            f'合同 {contract_no}: 编辑界面已完全关闭，可能操作已完成'
                        )
                        return True

                    logger.debug(
                        f'合同 {contract_no}: 等待页面跳转... iframe-20可见: {iframe_20_visible}, iframe-18可见: {iframe_18_visible}'
                    )
                    sleep(0.5)

                except Exception as e:
                    logger.debug(f'合同 {contract_no}: 检查iframe状态时出错: {e}')
                    sleep(0.5)

            logger.warning(f'合同 {contract_no}: 等待返回合同编辑页面超时')
            return False

        except Exception as e:
            logger.error(f'合同 {contract_no}: 等待返回合同编辑页面时发生异常: {e}')
            return False

    def _handle_contract_edit_page_actions(self, contract_no: str, cursor) -> bool:
        """
        在合同编辑页面（框架18）执行后续操作
        :param contract_no: 合同编号
        :param cursor: 数据库游标
        :return: 是否成功完成操作
        """
        try:
            logger.info(f'合同 {contract_no}: 开始处理合同编辑页面的后续操作')

            # 检查是否有合同编辑页面iframe可以切换
            self.driver.switch_to.default_content()

            all_iframes_by_name = self.driver.find_elements(
                By.CSS_SELECTOR, "iframe[name^='mini-iframe-']"
            )
            for iframe in all_iframes_by_name:
                iframe_name = iframe.get_attribute('name')
                if iframe_name and iframe_name.startswith('mini-iframe-'):
                    self.driver.switch_to.frame(iframe)
                    success = self._perform_contract_edit_actions(contract_no, cursor)
                    return success
            else:
                logger.info(
                    f'合同 {contract_no}: 合同编辑页面iframe不可见，可能已自动关闭'
                )
                return True

        except Exception as e:
            logger.error(f'合同 {contract_no}: 处理合同编辑页面后续操作时发生异常: {e}')
            return False

    def _perform_contract_edit_actions(self, contract_no: str, cursor) -> bool:
        """
        在合同编辑页面执行具体的操作（如保存、提交等）
        :param contract_no: 合同编号
        :param cursor: 数据库游标
        :return: 是否成功完成操作
        """
        try:
            logger.info(f'合同 {contract_no}: 执行合同编辑页面的具体操作')

            # 等待页面稳定
            sleep(2)

            # 添加文件上传功能
            logger.info(f'合同 {contract_no}: 开始处理文件上传')
            #  检查是否已存在文件
            table = self.driver.find_element(By.XPATH, "//table[@id='file-list']")
            tr = table.find_elements(By.TAG_NAME, 'tr')[1]
            td = tr.find_elements(By.TAG_NAME, 'td')[1]

            self.driver.execute_script(
                'arguments[0].scrollIntoView({block: "end", behavior: "smooth"});',
                td,
            )
            sleep(1)  # 增加等待时间,确保滚动完成

            fiels = td.find_elements(By.CLASS_NAME, 'item.clearfix')
            if fiels[0].text != '无电子件':
                logger.info(f'合同 {contract_no}: 已存在文件，跳过上传')
            else:
                upload_success = self._handle_file_upload(contract_no, cursor)

                if upload_success:
                    logger.info(f'合同 {contract_no}: 文件上传处理完成')
                else:
                    logger.warning(f'合同 {contract_no}: 文件上传处理失败')

            # 查找并点击保存按钮（如果存在）
            self._try_save_contract_in_edit_page(contract_no)
            return True

        except Exception as e:
            logger.error(f'合同 {contract_no}: 执行合同编辑页面操作时发生异常: {e}')
            return False

    def _try_save_contract_in_edit_page(self, contract_no: str) -> bool:
        """
        尝试在合同编辑页面保存合同
        :param contract_no: 合同编号
        :return: 是否找到并点击了保存按钮
        """
        try:
            logger.debug(f'合同 {contract_no}: 查找合同编辑页面的保存按钮')

            # 查找可能的保存按钮
            save_button_selectors = [
                "//button[contains(text(), '保存')]",
                "//input[@value='保存']",
                "//span[contains(text(), '保存')]",
                "//a[contains(text(), '保存')]",
                '#saveBtn',
                '#btnSave',
                '.save-btn',
                "//button[contains(@class, 'save')]",
                "//input[@type='button' and contains(@value, '保存')]",
            ]

            for selector in save_button_selectors:
                try:
                    if selector.startswith('//'):
                        save_button = self.driver.find_element(By.XPATH, selector)
                    else:
                        save_button = self.driver.find_element(
                            By.CSS_SELECTOR, selector
                        )

                    if (
                        save_button
                        and save_button.is_displayed()
                        and save_button.is_enabled()
                    ):
                        logger.info(f'合同 {contract_no}: 找到保存按钮，准备点击')
                        save_button.click()
                        logger.info(f'合同 {contract_no}: 成功点击保存按钮')
                        sleep(2)  # 等待保存完成

                        # 处理保存成功的确认对话框
                        self._handle_save_success_dialog(contract_no)

                        return True

                except Exception:
                    continue

            logger.debug(f'合同 {contract_no}: 未找到保存按钮')
            return False

        except Exception as e:
            logger.debug(f'合同 {contract_no}: 查找保存按钮时出错: {e}')
            return False

    def _fill_field_safely(
        self, field_name: str, value: str, possible_ids: list
    ) -> bool:
        """
        安全地填写字段，尝试多个可能的ID
        :param field_name: 字段名称（用于日志）
        :param value: 要填写的值
        :param possible_ids: 可能的字段ID列表
        :return: 是否成功填写
        """
        if not value or not possible_ids:
            logger.debug(f'字段 {field_name}: 值为空或ID列表为空，跳过填写')
            return False

        try:
            for field_id in possible_ids:
                try:
                    # 尝试多种可能的元素查找方式
                    element = None
                    text_element = None
                    value_element = None

                    # 1. 尝试直接查找主ID
                    try:
                        element = self.driver.find_element(By.ID, field_id)
                        if element and element.is_displayed() and element.is_enabled():
                            logger.debug(f'字段 {field_name}: 找到主元素 {field_id}')
                    except:
                        pass

                    # 2. 尝试查找带$text后缀的文本输入框
                    try:
                        text_element = self.driver.find_element(
                            By.ID, f'{field_id}$text'
                        )
                        if (
                            text_element
                            and text_element.is_displayed()
                            and text_element.is_enabled()
                        ):
                            logger.debug(
                                f'字段 {field_name}: 找到文本输入框 {field_id}$text'
                            )
                    except:
                        pass

                    # 3. 尝试查找带$value后缀的隐藏值字段
                    try:
                        value_element = self.driver.find_element(
                            By.ID, f'{field_id}$value'
                        )
                        logger.debug(
                            f'字段 {field_name}: 找到隐藏值字段 {field_id}$value'
                        )
                    except:
                        pass

                    # 如果找到了可用的输入元素
                    if text_element or element:
                        target_element = text_element if text_element else element

                        # 清空并填写值
                        try:
                            # 使用JavaScript清空和设置值，更稳定
                            self.driver.execute_script(
                                "arguments[0].value = ''; arguments[0].value = arguments[1];",
                                target_element,
                                str(value),
                            )

                            # 触发输入事件
                            self.driver.execute_script(
                                """
                                var element = arguments[0];
                                var value = arguments[1];
                                element.value = value;
                                
                                // 触发各种事件
                                ['focus', 'input', 'change', 'blur', 'keyup'].forEach(function(eventType) {
                                    var event = new Event(eventType, { bubbles: true });
                                    element.dispatchEvent(event);
                                });
                            """,
                                target_element,
                                str(value),
                            )

                            sleep(0.1)  # 短暂等待

                            # 验证值是否设置成功
                            current_value = target_element.get_attribute('value')
                            if current_value == str(value):
                                logger.info(
                                    f"字段 {field_name}: 成功填写值 '{value}' 到 {target_element.get_attribute('id')}"
                                )

                                # 如果有隐藏值字段，也设置一下
                                if value_element:
                                    try:
                                        self.driver.execute_script(
                                            'arguments[0].value = arguments[1];',
                                            value_element,
                                            str(value),
                                        )
                                        logger.debug(
                                            f'字段 {field_name}: 同时设置隐藏值字段'
                                        )
                                    except Exception as e:
                                        logger.debug(
                                            f'字段 {field_name}: 设置隐藏值字段失败: {e}'
                                        )

                                return True
                            else:
                                logger.debug(
                                    f"字段 {field_name}: 值设置失败，期望: '{value}', 实际: '{current_value}'"
                                )

                        except Exception as e:
                            logger.debug(f'字段 {field_name}: 填写过程出错: {e}')
                            continue

                except Exception as e:
                    logger.debug(f'字段 {field_name}: 尝试ID {field_id} 失败: {e}')
                    continue

            # 尝试通过name属性查找
            for field_id in possible_ids:
                try:
                    elements = self.driver.find_elements(By.NAME, field_id)
                    for element in elements:
                        if element and element.is_displayed() and element.is_enabled():
                            # 获取元素类型
                            tag_name = element.tag_name.lower()
                            element_type = element.get_attribute('type')

                            if tag_name == 'input' and element_type in [
                                'text',
                                'hidden',
                            ]:
                                try:
                                    self.driver.execute_script(
                                        'arguments[0].value = arguments[1];',
                                        element,
                                        str(value),
                                    )

                                    # 触发change事件
                                    self.driver.execute_script(
                                        """
                                        var event = new Event('change', { bubbles: true });
                                        arguments[0].dispatchEvent(event);
                                    """,
                                        element,
                                    )

                                    logger.info(
                                        f"字段 {field_name}: 通过name属性成功填写值 '{value}'"
                                    )
                                    return True
                                except Exception as e:
                                    logger.debug(
                                        f'字段 {field_name}: 通过name属性填写失败: {e}'
                                    )
                                    continue
                except Exception as e:
                    logger.debug(f'字段 {field_name}: 通过name属性查找失败: {e}')
                    continue

            logger.warning(f"字段 {field_name}: 所有尝试都失败，无法填写值 '{value}'")
            return False

        except Exception as e:
            logger.error(f'字段 {field_name}: 填写过程发生异常: {e}')
            log_exception_location('_fill_field_safely')
            return False

    def _fill_miniui_combobox(
        self, field_name: str, target_text: str, possible_ids: list
    ) -> bool:
        """
        专门处理miniui combobox控件
        :param field_name: 字段名称（用于日志）
        :param target_text: 要选择的文本
        :param possible_ids: 可能的字段ID列表
        :return: 是否成功选择
        """
        try:
            for field_id in possible_ids:
                try:
                    # 1. 尝试找到主combobox容器
                    combobox = self.driver.find_element(By.ID, field_id)
                    if not (combobox and combobox.is_displayed()):
                        continue

                    logger.info(f'字段 {field_name}: 找到combobox控件 {field_id}')

                    # 2. 先尝试直接设置值到文本输入框和隐藏字段
                    try:
                        text_input = self.driver.find_element(By.ID, f'{field_id}$text')
                        value_input = self.driver.find_element(
                            By.ID, f'{field_id}$value'
                        )

                        # 直接设置值
                        self.driver.execute_script(
                            """
                            arguments[0].value = arguments[2];
                            arguments[1].value = arguments[2];
                            
                            // 触发各种事件
                            var events = ['focus', 'input', 'change', 'blur'];
                            events.forEach(function(eventType) {
                                var event = new Event(eventType, { bubbles: true });
                                arguments[0].dispatchEvent(event);
                            });
                        """,
                            text_input,
                            value_input,
                            target_text,
                        )

                        sleep(0.5)

                        # 验证是否设置成功
                        current_value = text_input.get_attribute('value')
                        if current_value == target_text:
                            logger.info(
                                f'字段 {field_name}: 直接设置值成功: {target_text}'
                            )
                            return True

                    except Exception as direct_e:
                        logger.debug(f'字段 {field_name}: 直接设置值失败: {direct_e}')

                    # 3. 如果直接设置失败，尝试点击触发按钮打开下拉框
                    try:
                        # 查找触发按钮
                        trigger_selectors = [
                            f"#{field_id} span[name='trigger']",
                            f'#{field_id} .mini-buttonedit-trigger',
                            f'#{field_id} .mini-buttonedit-button',
                        ]

                        trigger_button = None
                        for selector in trigger_selectors:
                            try:
                                trigger_button = self.driver.find_element(
                                    By.CSS_SELECTOR, selector
                                )
                                if trigger_button and trigger_button.is_displayed():
                                    break
                            except:
                                continue

                        if trigger_button:
                            logger.info(
                                f'字段 {field_name}: 找到触发按钮，准备点击打开下拉框'
                            )

                            # 点击触发按钮
                            self.driver.execute_script(
                                'arguments[0].click();', trigger_button
                            )
                            sleep(1)  # 等待下拉框打开

                            # 4. 在下拉框中查找目标选项
                            option_selectors = [
                                f"//li[contains(text(), '{target_text}')]",
                                f"//div[contains(@class, 'mini-listbox-item') and contains(text(), '{target_text}')]",
                                f"//tr[contains(@class, 'mini-listbox-item') and contains(text(), '{target_text}')]",
                                f"//span[contains(text(), '{target_text}')]",
                                f"//*[contains(@class, 'option') and contains(text(), '{target_text}')]",
                            ]

                            option_found = False
                            for option_selector in option_selectors:
                                try:
                                    # 等待选项出现
                                    option = WebDriverWait(self.driver, 3).until(
                                        EC.element_to_be_clickable(
                                            (By.XPATH, option_selector)
                                        )
                                    )

                                    if option:
                                        # 滚动到选项可见
                                        self.driver.execute_script(
                                            "arguments[0].scrollIntoView({block: 'center'});",
                                            option,
                                        )
                                        sleep(0.3)

                                        # 点击选项
                                        self.driver.execute_script(
                                            'arguments[0].click();', option
                                        )
                                        logger.info(
                                            f'字段 {field_name}: 成功点击选项: {target_text}'
                                        )

                                        # 等待下拉框关闭
                                        sleep(1)

                                        # 验证是否选择成功
                                        try:
                                            text_input = self.driver.find_element(
                                                By.ID, f'{field_id}$text'
                                            )
                                            current_value = text_input.get_attribute(
                                                'value'
                                            )
                                            if (
                                                target_text in current_value
                                                or current_value in target_text
                                            ):
                                                logger.info(
                                                    f'字段 {field_name}: 下拉选择成功: {current_value}'
                                                )
                                                option_found = True
                                                break
                                        except:
                                            option_found = True  # 假设成功
                                            break

                                except Exception as option_e:
                                    logger.debug(
                                        f'字段 {field_name}: 尝试选项 {option_selector} 失败: {option_e}'
                                    )
                                    continue

                            if option_found:
                                return True
                            else:
                                logger.warning(
                                    f'字段 {field_name}: 在下拉框中未找到选项: {target_text}'
                                )
                                # 尝试关闭下拉框
                                try:
                                    self.driver.execute_script(
                                        'arguments[0].click();', trigger_button
                                    )
                                except:
                                    pass
                        else:
                            logger.debug(f'字段 {field_name}: 未找到触发按钮')

                    except Exception as trigger_e:
                        logger.debug(f'字段 {field_name}: 触发下拉框失败: {trigger_e}')

                except Exception as field_e:
                    logger.debug(
                        f'字段 {field_name}: 处理字段 {field_id} 失败: {field_e}'
                    )
                    continue

            logger.warning(
                f'字段 {field_name}: 所有方式都失败，无法设置值: {target_text}'
            )
            return False

        except Exception as e:
            logger.error(f'字段 {field_name}: 处理miniui combobox时发生异常: {e}')
            log_exception_location('_fill_miniui_combobox')
            return False

    def _save_material_data(self) -> bool:
        """
        保存新增物料数据 - 点击"修改保存"按钮
        :return: 是否成功保存
        """
        try:
            logger.info("开始保存新增物料数据，查找'修改保存'按钮")

            # 等待页面加载完成
            sleep(1)

            # 根据提供的HTML结构，优先查找"修改保存"按钮
            save_button_selectors = [
                # 主要选择器 - 根据提供的HTML结构
                '#icon-save',  # 使用ID直接定位
                'a#icon-save',  # 更精确的选择器
                "a.mini-button[id='icon-save']",  # 最精确的选择器
                # 备用选择器 - 通过文本内容查找
                "//a[contains(@class, 'mini-button') and .//span[contains(text(), '修改保存')]]",
                "//a[@id='icon-save' and .//span[contains(text(), '修改保存')]]",
                # 通用保存按钮选择器
                "//input[@value='保存']",
                "//button[contains(text(), '保存')]",
                "//span[contains(text(), '保存')]",
                "//input[@value='确定']",
                "//button[contains(text(), '确定')]",
                "//span[contains(text(), '确定')]",
                '#saveBtn',
                '#confirmBtn',
                '.save-btn',
                '.confirm-btn',
            ]

            save_button = None
            used_selector = None

            for selector in save_button_selectors:
                try:
                    if selector.startswith('//'):
                        # XPath选择器
                        save_button = self.driver.find_element(By.XPATH, selector)
                    else:
                        # CSS选择器
                        save_button = self.driver.find_element(
                            By.CSS_SELECTOR, selector
                        )

                    if (
                        save_button
                        and save_button.is_displayed()
                        and save_button.is_enabled()
                    ):
                        used_selector = selector
                        logger.info(f"找到'修改保存'按钮，选择器: {selector}")
                        break

                except Exception:
                    continue

            if not save_button:
                logger.warning("未找到'修改保存'按钮")
                return False

            # 滚动到按钮可见位置
            self.driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center'});", save_button
            )
            sleep(0.5)

            # 尝试多种点击方法
            click_methods = [
                ('直接点击', lambda: save_button.click()),
                (
                    'JavaScript点击',
                    lambda: self.driver.execute_script(
                        'arguments[0].click();', save_button
                    ),
                ),
                (
                    'Action点击',
                    lambda: ActionChains(self.driver)
                    .move_to_element(save_button)
                    .click()
                    .perform(),
                ),
            ]

            click_success = False
            for method_name, click_func in click_methods:
                try:
                    logger.debug(f"尝试使用{method_name}点击'修改保存'按钮")
                    click_func()
                    logger.info(f"使用{method_name}成功点击'修改保存'按钮")
                    click_success = True
                    break
                except Exception as click_e:
                    logger.warning(f'{method_name}失败: {click_e}')
                    continue

            if not click_success:
                logger.error("所有点击方法都失败，无法点击'修改保存'按钮")
                return False

            # 等待保存操作完成
            sleep(2)

            # 检查是否有保存成功的提示或页面变化
            success_indicators = [
                '保存成功',
                '修改成功',
                '添加成功',
                '创建成功',
                '操作成功',
                'success',
                '完成',
            ]

            # 等待可能的提示信息出现
            sleep(1.5)

            page_source = self.driver.page_source.lower()
            for indicator in success_indicators:
                if indicator.lower() in page_source:
                    logger.info(f'检测到成功提示: {indicator}')

                    # 如果有成功提示弹窗，尝试关闭
                    try:
                        self._handle_success_popup()
                    except Exception as popup_e:
                        logger.debug(f'处理成功提示弹窗时出错: {popup_e}')

                    return True

            # 检查是否有错误提示
            error_indicators = [
                '保存失败',
                '修改失败',
                '添加失败',
                '创建失败',
                '操作失败',
                'error',
                '错误',
                '失败',
            ]

            for indicator in error_indicators:
                if indicator.lower() in page_source:
                    logger.warning(f'检测到错误提示: {indicator}')
                    return False

            # 检查是否已经回到上一级页面（mini-iframe-18）
            try:
                self.driver.switch_to.default_content()
                # 检查是否还在mini-iframe-20中
                current_iframe = self.driver.find_elements(By.NAME, 'mini-iframe-20')
                if not current_iframe or not current_iframe[0].is_displayed():
                    logger.info('页面已关闭，可能保存成功并返回上级页面')
                    return True
            except Exception:
                pass

            # 如果没有明确的提示，但按钮点击成功，认为保存成功
            logger.info('未检测到明确的保存结果提示，但按钮点击成功，假定保存成功')
            return True

        except Exception as e:
            log_exception_location('_save_material_data', '保存物料数据失败')
            logger.error(f'保存物料数据时发生异常: {e}')
            return False

    def _close_popup_iframes_only(self):
        """
        只关闭弹出的iframe，保留主表格iframe (tab-content-95130002)
        """
        try:
            logger.debug('开始关闭弹出iframe（保留主表格iframe）')
            self.driver.switch_to.default_content()

            # 首先强制移除所有遮罩层
            self._force_remove_all_masks()

            # 使用JavaScript关闭弹出iframe，但保留主表格iframe
            close_script = """
            // 查找所有iframe
            var iframes = document.querySelectorAll('iframe');
            var closedCount = 0;
            
            for (var i = 0; i < iframes.length; i++) {
                var iframe = iframes[i];
                var name = iframe.getAttribute('name') || '';
                var id = iframe.getAttribute('id') || '';
                
                // 只关闭弹出的iframe，保留主表格iframe
                if ((name.startsWith('mini-iframe-') && name !== 'tab-content-95130002') ||
                    (id.startsWith('mini-iframe-') && id !== 'tab-content-95130002') ||
                    (name.includes('modal') || id.includes('modal'))) {
                    try {
                        // 尝试多种方式关闭iframe
                        iframe.style.display = 'none';
                        iframe.style.visibility = 'hidden';
                        iframe.style.zIndex = '-1';
                        
                        // 尝试移除iframe
                        if (iframe.parentNode) {
                            iframe.parentNode.removeChild(iframe);
                            closedCount++;
                        }
                    } catch(e) {
                        console.log('关闭弹出iframe失败: ' + (name || id), e);
                    }
                }
            }
            
            // 关闭所有可能的弹窗
            var windows = document.querySelectorAll('.mini-window, .dialog, .modal, .popup');
            for (var j = 0; j < windows.length; j++) {
                try {
                    windows[j].style.display = 'none';
                    if (windows[j].parentNode) {
                        windows[j].parentNode.removeChild(windows[j]);
                    }
                } catch(e) {
                    console.log('关闭弹窗失败', e);
                }
            }
            
            return closedCount;
            """

            closed_count = self.driver.execute_script(close_script)
            logger.info(f'关闭了 {closed_count} 个弹出iframe（保留主表格iframe）')

            # 再次强制移除所有遮罩层
            self._force_remove_all_masks()

            # 等待关闭生效
            sleep(0.5)

        except Exception as e:
            logger.warning(f'关闭弹出iframe时发生异常: {e}')
            try:
                # 如果出错，至少确保回到默认内容
                self.driver.switch_to.default_content()
            except Exception:
                pass

    def _detect_edit_interface_type(self) -> dict:
        """
        检测编辑界面的类型（iframe或模态窗口）
        :return: 包含type和name的字典，如果未找到返回None
        """
        try:
            # 等待界面出现
            sleep(2)

            # 首先检查是否有预期的iframe (mini-iframe-18)
            try:
                iframe_18 = self.driver.find_element(By.NAME, 'mini-iframe-18')
                if iframe_18.is_displayed():
                    logger.debug('找到预期的iframe: mini-iframe-18')
                    return {'type': 'iframe', 'name': 'mini-iframe-18'}
            except Exception:
                pass

            # 检查是否有其他可能的编辑iframe
            try:
                all_iframes = self.driver.find_elements(
                    By.CSS_SELECTOR, "iframe[name^='mini-iframe-']"
                )
                for iframe in all_iframes:
                    name = iframe.get_attribute('name')
                    if (
                        name
                        and name != 'tab-content-95130002'
                        and iframe.is_displayed()
                    ):
                        logger.debug(f'找到其他编辑iframe: {name}')
                        return {'type': 'iframe', 'name': name}
            except Exception:
                pass

            # 检查是否有模态窗口
            try:
                modal_selectors = [
                    "[id^='__modalmini-']",  # 从日志看到的模态窗口
                    '.mini-modal',
                    '.mini-window',
                    "[class*='modal'][class*='mini']",
                ]

                for selector in modal_selectors:
                    modals = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for modal in modals:
                        if modal.is_displayed():
                            modal_id = modal.get_attribute('id') or modal.get_attribute(
                                'class'
                            )
                            logger.debug(f'找到模态窗口: {modal_id}')
                            return {'type': 'modal', 'name': modal_id}
            except Exception:
                pass

            logger.warning('未检测到任何编辑界面')
            return None

        except Exception as e:
            logger.error(f'检测编辑界面类型时出错: {e}')
            return None

    def _find_add_material_button(self) -> object:
        """
        查找"新增物料"按钮
        :return: 按钮元素，如果未找到返回None
        """
        try:
            # 等待按钮加载
            sleep(5)

            # 多种可能的按钮选择器
            button_selectors = [
                # 通过文本查找
                "//span[contains(@class, 'mini-button-text') and normalize-space(text())='新增物料']",
                "//button[contains(text(), '新增物料')]",
                "//input[@value='新增物料']",
                "//a[contains(text(), '新增物料')]",
                # 通过class或id查找
                "//span[contains(@class, 'mini-button-text') and contains(text(), '新增')]",
                "//span[contains(@class, 'mini-button-text') and contains(text(), '物料')]",
                # 更宽泛的查找
                "//*[contains(text(), '新增物料')]",
                "//*[contains(@title, '新增物料')]",
            ]

            for selector in button_selectors:
                try:
                    button = self.driver.find_element(By.XPATH, selector)
                    if button and button.is_displayed():
                        logger.debug(f"找到'新增物料'按钮，选择器: {selector}")
                        return button
                except Exception:
                    continue

            # 如果找不到"新增物料"，尝试查找其他可能的新增按钮
            fallback_selectors = [
                "//span[contains(@class, 'mini-button-text') and contains(text(), '新增')]",
                "//button[contains(text(), '新增')]",
                "//input[@value*='新增']",
            ]

            for selector in fallback_selectors:
                try:
                    button = self.driver.find_element(By.XPATH, selector)
                    if button and button.is_displayed():
                        logger.warning(f"未找到'新增物料'，使用备用按钮: {selector}")
                        return button
                except Exception:
                    continue

            logger.error("未找到任何'新增物料'或'新增'按钮")
            return None

        except Exception as e:
            logger.error(f"查找'新增物料'按钮时出错: {e}")
            return None

    def _handle_success_popup(self) -> bool:
        """
        处理保存成功后可能出现的弹窗
        :return: 是否成功处理弹窗
        """
        try:
            # 查找可能的确定按钮或关闭按钮
            popup_button_selectors = [
                "//button[contains(text(), '确定')]",
                "//button[contains(text(), '关闭')]",
                "//input[@value='确定']",
                "//input[@value='关闭']",
                "//span[contains(text(), '确定')]",
                "//span[contains(text(), '关闭')]",
                '.mini-messagebox-button',
                '.popup-close',
                '.dialog-close',
            ]

            for selector in popup_button_selectors:
                try:
                    if selector.startswith('//'):
                        popup_button = self.driver.find_element(By.XPATH, selector)
                    else:
                        popup_button = self.driver.find_element(
                            By.CSS_SELECTOR, selector
                        )

                    if (
                        popup_button
                        and popup_button.is_displayed()
                        and popup_button.is_enabled()
                    ):
                        popup_button.click()
                        logger.info(f'成功点击弹窗按钮: {selector}')
                        sleep(0.5)
                        return True

                except Exception:
                    continue

            return False

        except Exception as e:
            logger.debug(f'处理成功提示弹窗时出错: {e}')
            return False

    def _ensure_main_iframe(self) -> bool:
        """
        确保当前在主表格iframe中（不关闭任何iframe）
        :return: 是否成功切换到主表格iframe
        """
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.debug(f'第{attempt + 1}次尝试切换到主表格iframe')

                # 切换到默认内容
                self.driver.switch_to.default_content()

                # 等待主iframe存在
                iframe = self.wait.until(
                    EC.presence_of_element_located((By.ID, 'tab-content-95130002'))
                )

                # 确保iframe可见且可用
                if not iframe.is_displayed():
                    logger.warning('主iframe不可见')
                    if attempt < max_retries - 1:
                        sleep(1)
                        continue
                    return False

                # 切换到iframe
                self.driver.switch_to.frame(iframe)

                # 验证是否成功切换（检查是否能找到表格元素）
                try:
                    table_element = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located(
                            (By.CSS_SELECTOR, 'div.mini-grid-rows-view')
                        )
                    )
                    if table_element:
                        logger.debug('成功切换到主表格iframe')
                        return True
                except Exception:
                    logger.warning('切换到iframe后未找到表格元素')
                    if attempt < max_retries - 1:
                        sleep(1)
                        continue
                    return False

            except Exception as e:
                logger.warning(f'第{attempt + 1}次切换主iframe失败: {e}')
                if attempt < max_retries - 1:
                    sleep(1)
                else:
                    return False

        return False

    def _select_dropdown_option(
        self, field_name: str, target_text: str, possible_ids: list
    ) -> bool:
        """
        安全地选择下拉菜单选项
        :param field_name: 字段名称（用于日志）
        :param target_text: 要选择的文本
        :param possible_ids: 可能的下拉菜单ID列表
        :return: 是否成功选择
        """
        try:
            for field_id in possible_ids:
                try:
                    # 尝试通过ID查找下拉元素
                    dropdown = self.driver.find_element(By.ID, field_id)
                    if dropdown and dropdown.is_displayed():
                        # 尝试点击下拉框激活
                        dropdown.click()
                        sleep(0.5)

                        # 查找下拉选项的多种方式
                        option_selectors = [
                            f"//option[contains(text(), '{target_text}')]",
                            f"//li[contains(text(), '{target_text}')]",
                            f"//div[contains(@class, 'option') and contains(text(), '{target_text}')]",
                            f"//span[contains(text(), '{target_text}')]",
                            f"//a[contains(text(), '{target_text}')]",
                        ]

                        option_found = False
                        for selector in option_selectors:
                            try:
                                option = self.driver.find_element(By.XPATH, selector)
                                if option and option.is_displayed():
                                    # 滚动到选项可见
                                    self.driver.execute_script(
                                        "arguments[0].scrollIntoView({block: 'center'});",
                                        option,
                                    )
                                    sleep(0.3)

                                    # 点击选项
                                    option.click()
                                    logger.info(
                                        f'{field_name} 选择成功: {target_text} (使用ID: {field_id})'
                                    )
                                    option_found = True
                                    break
                            except Exception:
                                continue

                        if option_found:
                            return True

                except Exception:
                    continue

            # 如果通过ID找不到，尝试通过class或其他属性
            try:
                # 查找可能的下拉框
                dropdown_selectors = [
                    f"//select[following-sibling::label[contains(text(), '{field_name}')]]",
                    f"//select[preceding-sibling::label[contains(text(), '{field_name}')]]",
                    f"//div[contains(@class, 'dropdown')]//input[following-sibling::label[contains(text(), '{field_name}')]]",
                    f"//div[contains(@class, 'select')]//input[following-sibling::label[contains(text(), '{field_name}')]]",
                ]

                for selector in dropdown_selectors:
                    try:
                        dropdown = self.driver.find_element(By.XPATH, selector)
                        if dropdown and dropdown.is_displayed():
                            dropdown.click()
                            sleep(0.5)

                            # 查找并点击选项
                            option = self.driver.find_element(
                                By.XPATH, f"//option[contains(text(), '{target_text}')]"
                            )
                            if option:
                                option.click()
                                logger.info(
                                    f'{field_name} 选择成功: {target_text} (使用XPath)'
                                )
                                return True

                    except Exception:
                        continue

            except Exception:
                pass

            logger.debug(f'{field_name} 下拉选择失败，未找到匹配选项: {target_text}')
            return False

        except Exception as e:
            logger.warning(f'选择下拉菜单 {field_name} 时发生异常: {e}')
            return False

    def _close_all_pop_iframes(self):
        """
        强力关闭所有非主表格iframe，确保主表格可见
        """
        try:
            logger.debug('开始强力关闭所有弹出iframe')
            self.driver.switch_to.default_content()

            # 首先强制移除所有遮罩层
            self._force_remove_all_masks()

            # 使用JavaScript强制关闭所有弹出iframe
            close_script = """
            // 查找所有iframe
            var iframes = document.querySelectorAll('iframe');
            var closedCount = 0;
            
            for (var i = 0; i < iframes.length; i++) {
                var iframe = iframes[i];
                var name = iframe.getAttribute('name') || '';
                var id = iframe.getAttribute('id') || '';
                
                // 关闭所有mini-iframe开头的iframe（除了主表格）
                if (name.startsWith('mini-iframe-') && name !== 'tab-content-95130002') {
                    try {
                        // 尝试多种方式关闭iframe
                        iframe.style.display = 'none';
                        iframe.style.visibility = 'hidden';
                        iframe.style.zIndex = '-1';
                        
                        // 尝试移除iframe
                        if (iframe.parentNode) {
                            iframe.parentNode.removeChild(iframe);
                            closedCount++;
                        }
                    } catch(e) {
                        console.log('关闭iframe失败: ' + name, e);
                    }
                }
            }
            
            // 关闭所有可能的弹窗
            var windows = document.querySelectorAll('.mini-window, .dialog, .modal, .popup');
            for (var j = 0; j < windows.length; j++) {
                try {
                    windows[j].style.display = 'none';
                    if (windows[j].parentNode) {
                        windows[j].parentNode.removeChild(windows[j]);
                    }
                } catch(e) {
                    console.log('关闭弹窗失败', e);
                }
            }
            
            return closedCount;
            """

            closed_count = self.driver.execute_script(close_script)
            logger.info(f'强制关闭了 {closed_count} 个弹出iframe')

            # 再次强制移除所有遮罩层（在关闭iframe后可能还有残留）
            self._force_remove_all_masks()

            # 等待关闭生效
            sleep(1)

            # 确保切换回主表格iframe
            try:
                iframe = self.wait.until(
                    EC.presence_of_element_located((By.ID, 'tab-content-95130002'))
                )
                self.driver.switch_to.frame(iframe)
                logger.debug('已切换回主表格iframe')

                # 在主表格iframe内也清除遮罩层
                self._force_remove_all_masks_in_iframe()

            except Exception as e:
                logger.warning(f'切换回主表格iframe失败: {e}')

        except Exception as e:
            logger.warning(f'强力关闭弹出iframe时发生异常: {e}')
            try:
                # 如果出错，至少确保回到默认内容
                self.driver.switch_to.default_content()
            except Exception:
                pass

    def _force_remove_all_masks(self):
        """
        强制移除所有遮罩层 - 在默认内容中执行
        """
        try:
            logger.debug('开始强制移除所有遮罩层（默认内容）')

            remove_masks_script = """
            // 移除所有遮罩层的函数
            function removeMasks() {
                var removed = 0;
                
                // 查找所有可能的遮罩元素
                var maskSelectors = [
                    '.mini-mask-background',
                    '.mini-mask',
                    '.mask',
                    '.overlay',
                    '.loading-mask',
                    '[class*="mask"]',
                    '[style*="z-index"][style*="position: absolute"]',
                    '[style*="z-index"][style*="position: fixed"]'
                ];
                
                for (var i = 0; i < maskSelectors.length; i++) {
                    var elements = document.querySelectorAll(maskSelectors[i]);
                    for (var j = 0; j < elements.length; j++) {
                        var elem = elements[j];
                        try {
                            // 隐藏元素
                            elem.style.display = 'none';
                            elem.style.visibility = 'hidden';
                            elem.style.opacity = '0';
                            elem.style.zIndex = '-999999';
                            elem.style.pointerEvents = 'none';
                            
                            // 尝试移除元素
                            if (elem.parentNode) {
                                elem.parentNode.removeChild(elem);
                                removed++;
                            }
                        } catch(e) {
                            console.log('移除遮罩失败', e);
                        }
                    }
                }
                
                // 移除所有高z-index的div元素（可能是动态创建的遮罩）
                var allDivs = document.querySelectorAll('div');
                for (var k = 0; k < allDivs.length; k++) {
                    var div = allDivs[k];
                    var style = window.getComputedStyle(div);
                    var zIndex = parseInt(style.zIndex);
                    
                    if (zIndex > 1000 && (style.position === 'absolute' || style.position === 'fixed')) {
                        // 检查是否是遮罩层（通常有背景色或透明度）
                        if (style.backgroundColor !== 'rgba(0, 0, 0, 0)' || 
                            style.backgroundColor !== 'transparent' ||
                            parseFloat(style.opacity) < 1) {
                            try {
                                div.style.display = 'none';
                                div.style.zIndex = '-999999';
                                if (div.parentNode) {
                                    div.parentNode.removeChild(div);
                                    removed++;
                                }
                            } catch(e) {
                                console.log('移除高z-index元素失败', e);
                            }
                        }
                    }
                }
                
                return removed;
            }
            
            return removeMasks();
            """

            removed_count = self.driver.execute_script(remove_masks_script)
            logger.info(f'在默认内容中移除了 {removed_count} 个遮罩元素')

            # 等待移除生效
            sleep(0.5)

        except Exception as e:
            logger.warning(f'强制移除遮罩层时出错: {e}')

    def _force_remove_all_masks_in_iframe(self):
        """
        强制移除iframe内的所有遮罩层
        """
        try:
            logger.debug('开始强制移除iframe内的所有遮罩层')

            remove_masks_script = """
            // 移除iframe内遮罩层
            function removeIframeMasks() {
                var removed = 0;
                
                var maskSelectors = [
                    '.mini-mask-background',
                    '.mini-mask',
                    '.mask',
                    '.overlay',
                    '.loading-mask',
                    '[class*="mask"]'
                ];
                
                for (var i = 0; i < maskSelectors.length; i++) {
                    var elements = document.querySelectorAll(maskSelectors[i]);
                    for (var j = 0; j < elements.length; j++) {
                        var elem = elements[j];
                        try {
                            elem.style.display = 'none !important';
                            elem.style.visibility = 'hidden !important';
                            elem.style.opacity = '0 !important';
                            elem.style.zIndex = '-999999 !important';
                            elem.style.pointerEvents = 'none !important';
                            
                            if (elem.parentNode) {
                                elem.parentNode.removeChild(elem);
                                removed++;
                            }
                        } catch(e) {
                            console.log('移除iframe遮罩失败', e);
                        }
                    }
                }
                
                return removed;
            }
            
            return removeIframeMasks();
            """

            removed_count = self.driver.execute_script(remove_masks_script)
            logger.debug(f'在iframe内移除了 {removed_count} 个遮罩元素')

            sleep(0.3)

        except Exception as e:
            logger.debug(f'强制移除iframe内遮罩层时出错: {e}')

    def _safe_click_button_with_mask_handling(
        self, button_element, button_name: str
    ) -> bool:
        """
        安全点击按钮，包含遮罩层处理
        :param button_element: 按钮元素
        :param button_name: 按钮名称（用于日志）
        :return: 是否成功点击
        """
        try:
            # 多种点击方法
            click_methods = [
                ('直接点击', lambda: button_element.click()),
                (
                    'JavaScript点击',
                    lambda: self.driver.execute_script(
                        'arguments[0].click();', button_element
                    ),
                ),
                (
                    '强制JavaScript点击',
                    lambda: self.driver.execute_script(
                        """
                    var elem = arguments[0];
                    var event = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    elem.dispatchEvent(event);
                """,
                        button_element,
                    ),
                ),
                (
                    'Action点击',
                    lambda: ActionChains(self.driver)
                    .move_to_element(button_element)
                    .click()
                    .perform(),
                ),
                (
                    '坐标点击',
                    lambda: self._click_element_at_coordinates(button_element),
                ),
            ]

            for method_name, click_func in click_methods:
                try:
                    logger.debug(f'尝试使用{method_name}点击{button_name}按钮')

                    # 每次点击前都清除遮罩
                    self._force_remove_all_masks_in_iframe()
                    sleep(0.2)

                    # 确保元素仍然存在且可见
                    if not button_element.is_displayed():
                        logger.warning(f'{button_name}按钮不可见，跳过{method_name}')
                        continue

                    # 执行点击
                    click_func()
                    logger.info(f'使用{method_name}成功点击{button_name}按钮')
                    return True

                except Exception as click_e:
                    error_msg = str(click_e)
                    if 'click intercepted' in error_msg:
                        logger.warning(f'{method_name}被遮罩拦截: {error_msg}')
                        # 如果被遮罩拦截，强制清除更多遮罩后重试
                        self._aggressive_mask_removal()
                        sleep(0.5)
                    else:
                        logger.warning(f'{method_name}失败: {click_e}')
                    continue

            logger.error(f'所有点击方法都失败，无法点击{button_name}按钮')
            return False

        except Exception as e:
            logger.error(f'安全点击{button_name}按钮时发生异常: {e}')
            return False

    def _click_element_at_coordinates(self, element):
        """
        通过坐标点击元素
        """
        try:
            # 获取元素位置和大小
            location = element.location
            size = element.size

            # 计算元素中心坐标
            center_x = location['x'] + size['width'] // 2
            center_y = location['y'] + size['height'] // 2

            # 使用ActionChains在指定坐标点击
            actions = ActionChains(self.driver)
            actions.move_by_offset(center_x, center_y)
            actions.click()
            actions.perform()

        except Exception as e:
            logger.warning(f'坐标点击失败: {e}')
            raise

    def _aggressive_mask_removal(self):
        """
        激进的遮罩层移除 - 移除所有可能阻挡的元素
        """
        try:
            logger.debug('执行激进的遮罩层移除')

            aggressive_script = """
            // 激进移除所有可能的阻挡元素
            function aggressiveRemoveMasks() {
                var removed = 0;
                
                // 移除所有绝对定位且z-index高的元素
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var elem = allElements[i];
                    var style = window.getComputedStyle(elem);
                    
                    // 检查是否是可能的遮罩元素
                    if ((style.position === 'absolute' || style.position === 'fixed') &&
                        (parseInt(style.zIndex) > 100 || style.zIndex === 'auto')) {
                        
                        var className = elem.className || '';
                        var tagName = elem.tagName.toLowerCase();
                        
                        // 如果包含mask关键词或是可疑的遮罩div
                        if (className.includes('mask') || 
                            className.includes('overlay') ||
                            className.includes('loading') ||
                            (tagName === 'div' && elem.offsetWidth > 100 && elem.offsetHeight > 100)) {
                            try {
                                elem.style.display = 'none';
                                elem.style.visibility = 'hidden';
                                elem.style.pointerEvents = 'none';
                                elem.style.zIndex = '-999999';
                                removed++;
                            } catch(e) {
                                // 忽略错误
                            }
                        }
                    }
                }
                
                return removed;
            }
            
            return aggressiveRemoveMasks();
            """

            removed_count = self.driver.execute_script(aggressive_script)
            logger.info(f'激进移除了 {removed_count} 个可能的阻挡元素')

        except Exception as e:
            logger.debug(f'激进遮罩移除时出错: {e}')

    def _get_current_page_row_infos(self) -> list:
        """
        获取当前页面所有行的基本信息，避免陈旧的元素引用
        :return: 包含行信息的列表，每个元素包含 row_id, contract_no, audit_status
        """
        try:
            row_infos = []

            # 获取表格数据区
            rows_view = self.driver.find_element(
                By.CSS_SELECTOR, 'div.mini-grid-rows-view'
            )
            table = rows_view.find_element(
                By.CSS_SELECTOR, 'table.mini-grid-table.mini-grid-rowstable'
            )

            # 先滚动到表格顶部，然后到底部，确保所有行都被渲染
            logger.info('滚动表格以确保所有行可见...')
            self.driver.execute_script('arguments[0].scrollTop = 0;', rows_view)
            sleep(0.5)
            self.driver.execute_script(
                'arguments[0].scrollTop = arguments[0].scrollHeight;', rows_view
            )
            sleep(0.5)
            # 再回到顶部开始处理
            self.driver.execute_script('arguments[0].scrollTop = 0;', rows_view)
            sleep(0.5)

            # 先获取行数，避免在遍历过程中元素失效
            rows = table.find_elements(
                By.CSS_SELECTOR, 'tr.mini-grid-row, tr.mini-grid-row-alt'
            )
            row_count = len(rows)
            logger.info(f'当前页面找到 {row_count} 行数据')

            # 逐行处理，每次重新获取元素引用以避免stale element reference
            for i in range(row_count):
                try:
                    # 重新获取表格和行元素，避免stale element reference
                    rows_view = self.driver.find_element(
                        By.CSS_SELECTOR, 'div.mini-grid-rows-view'
                    )
                    table = rows_view.find_element(
                        By.CSS_SELECTOR, 'table.mini-grid-table.mini-grid-rowstable'
                    )
                    current_rows = table.find_elements(
                        By.CSS_SELECTOR, 'tr.mini-grid-row, tr.mini-grid-row-alt'
                    )

                    # 检查行数是否发生变化
                    if i >= len(current_rows):
                        logger.warning(
                            f'行索引{i}超出范围，当前行数: {len(current_rows)}'
                        )
                        break

                    row = current_rows[i]

                    # 滚动到当前行，确保行可见
                    self.driver.execute_script(
                        "arguments[0].scrollIntoView({block: 'center'});", row
                    )
                    sleep(0.1)

                    # 获取行ID
                    row_id = row.get_attribute('id')
                    if not row_id:
                        logger.debug(f'第{i + 1}行没有ID，跳过')
                        continue

                    # 获取所有单元格
                    cells = row.find_elements(By.CSS_SELECTOR, 'td.mini-grid-cell')
                    if len(cells) < 15:  # 确保有足够的列
                        logger.debug(f'行 {row_id} 列数不足: {len(cells)}')
                        continue

                    # 根据HTML结构，合同号在第4列（索引3），审核状态在第15列（索引14）
                    # 根据用户提供的HTML结构，审核状态应该在第15列
                    contract_no = ''
                    audit_status = ''

                    # 查找合同号（第4列，索引3）
                    if len(cells) > 3:
                        contract_no = cells[3].text.strip()

                    # 查找审核状态 - 根据HTML结构，应该在第15列（索引14）
                    # 但也要兼容可能的其他索引位置
                    # for idx in [14, 13, 15]:  # 优先尝试第15列，然后第14列，最后第16列
                    #     if len(cells) > idx:
                    #         cell_text = cells[idx].text.strip()
                    #         if cell_text in [
                    #             '编辑中',
                    #             '已提交',
                    #             '已审核',
                    #             '已驳回',
                    #             '草稿',
                    #         ]:
                    #             audit_status = cell_text
                    #             logger.debug(
                    #                 f'在第{idx + 1}列找到审核状态: {audit_status}'
                    #             )
                    #             break
                    audit_status = cells[14].text.strip()
                    # 如果还没找到审核状态，遍历所有列查找
                    # if not audit_status:
                    #     for idx, cell in enumerate(cells):
                    #         cell_text = cell.text.strip()
                    #         if cell_text in [
                    #             '编辑中',
                    #             '已提交',
                    #             '已审核',
                    #             '已驳回',
                    #             '草稿',
                    #         ]:
                    #             audit_status = cell_text
                    #             logger.debug(
                    #                 f'在第{idx + 1}列找到审核状态: {audit_status}'
                    #             )
                    #             break

                    if contract_no:  # 只有合同号不为空的行才记录
                        row_info = {
                            'row_id': row_id,
                            'contract_no': contract_no,
                            'audit_status': audit_status,
                        }
                        row_infos.append(row_info)
                        logger.debug(
                            f'记录行信息: 行号={i + 1}, 行ID={row_id}, 合同号={contract_no}, 状态={audit_status}'
                        )
                    else:
                        logger.debug(f'第{i + 1}行合同号为空，跳过')

                except Exception as e:
                    logger.warning(f'解析第{i + 1}行数据时出错: {e}')
                    continue

            logger.info(f'成功解析 {len(row_infos)} 行有效数据')
            return row_infos

        except Exception as e:
            log_exception_location('_get_current_page_row_infos', '获取页面行信息失败')
            logger.error(f'获取页面行信息失败: {e}')
            return []

    def _get_current_contract_edit_iframe_name(self) -> str:
        """
        动态获取当前合同编辑页面的iframe名称（格式：mini-iframe-XX，XX从18开始每次递增2）
        :return: iframe名称，如果未找到返回None
        """
        try:
            self.driver.switch_to.default_content()

            # 查找所有mini-iframe开头的iframe
            all_iframes = self.driver.find_elements(
                By.CSS_SELECTOR, "iframe[name^='mini-iframe-']"
            )

            # 提取数字并找到最大的偶数编号（合同编辑页面的iframe）
            max_even_number = 0
            target_iframe_name = None

            for iframe in all_iframes:
                iframe_name = iframe.get_attribute('name')
                if iframe_name and iframe_name.startswith('mini-iframe-'):
                    try:
                        # 提取数字部分
                        number_str = iframe_name.replace('mini-iframe-', '')
                        number = int(number_str)

                        # 只考虑偶数编号且大于等于18的iframe（合同编辑页面）
                        if number >= 18 and number % 2 == 0 and iframe.is_displayed():
                            if number > max_even_number:
                                max_even_number = number
                                target_iframe_name = iframe_name

                    except ValueError:
                        continue

            if target_iframe_name:
                logger.info(f'检测到当前合同编辑iframe: {target_iframe_name}')
                return target_iframe_name
            else:
                logger.warning('未找到合同编辑iframe')
                return None

        except Exception as e:
            logger.error(f'获取合同编辑iframe名称时发生异常: {e}')
            return None

    def _get_current_material_add_iframe_name(
        self, contract_edit_iframe_name: str
    ) -> str:
        """
        根据合同编辑iframe名称推算新增物料iframe名称（编号+2）
        :param contract_edit_iframe_name: 合同编辑iframe名称
        :return: 新增物料iframe名称
        """
        try:
            if not contract_edit_iframe_name:
                return None

            # 提取数字部分
            number_str = contract_edit_iframe_name.replace('mini-iframe-', '')
            number = int(number_str)

            # 新增物料iframe编号是合同编辑iframe编号+2
            material_iframe_number = number + 2
            material_iframe_name = f'mini-iframe-{material_iframe_number}'

            logger.info(f'推算新增物料iframe名称: {material_iframe_name}')
            return material_iframe_name

        except Exception as e:
            logger.error(f'推算新增物料iframe名称时发生异常: {e}')
            return None

    def _wait_for_contract_edit_iframe_ready(self) -> str:
        """
        等待合同编辑iframe出现并且可用
        :return: iframe名称如果成功，否则返回None
        """
        try:
            logger.debug('等待合同编辑iframe出现...')

            # 确保在默认内容中
            self.driver.switch_to.default_content()

            # 等待合同编辑iframe出现，最多等待30秒
            for attempt in range(60):  # 30秒，每500ms检查一次
                try:
                    # 动态获取当前合同编辑iframe名称
                    contract_edit_iframe_name = (
                        self._get_current_contract_edit_iframe_name()
                    )

                    if contract_edit_iframe_name:
                        iframe_element = self.driver.find_element(
                            By.NAME, contract_edit_iframe_name
                        )
                        if iframe_element.is_displayed():
                            logger.info(f'{contract_edit_iframe_name}已出现且可见')

                            # 额外验证iframe是否可用（尝试切换进去）
                            try:
                                self.driver.switch_to.frame(iframe_element)
                                # 检查iframe内是否有内容
                                WebDriverWait(self.driver, 5).until(
                                    EC.presence_of_element_located(
                                        (By.TAG_NAME, 'body')
                                    )
                                )
                                logger.info(f'{contract_edit_iframe_name}内容已加载')
                                # 切回默认内容
                                self.driver.switch_to.default_content()
                                return contract_edit_iframe_name
                            except Exception as switch_e:
                                logger.debug(
                                    f'{contract_edit_iframe_name}切换测试失败: {switch_e}'
                                )
                                self.driver.switch_to.default_content()

                except Exception as e:
                    logger.debug(f'检测合同编辑iframe时出错: {e}')

                sleep(0.5)

            logger.warning('等待合同编辑iframe就绪超时')
            return None

        except Exception as e:
            logger.error(f'等待合同编辑iframe就绪时发生异常: {e}')
            return None

    def _jump_to_page(self, page_number: int) -> bool:
        """
        直接跳转到指定页码
        :param page_number: 目标页码
        :return: 是否成功跳转
        """
        try:
            # 检查页码参数是否有效
            if page_number < 1:
                logger.warning(f'无效的页码: {page_number}，页码必须大于0')
                return False

            # 查找页码输入框
            # page_input_selectors = [
            #     'input.pagination-number',
            #     '.pagination-number',
            #     "input[class*='pagination-number']",
            #     '.mini-pagination input',
            # ]

            # page_input = None
            # for selector in page_input_selectors:
            #     try:
            #         page_input = self.driver.find_element(By.CSS_SELECTOR, selector)
            #         if page_input and page_input.is_displayed():
            #             break
            #     except Exception:
            #         continue

            page_input = self.driver.find_element(
                By.XPATH, '//*[@id="mini-42"]/span[2]/input'
            )
            page_input.clear()
            page_input.send_keys(str(page_number))
            page_input.send_keys(Keys.ENTER)
            if not page_input:
                logger.warning('未找到页码输入框，使用传统翻页方式')
                return False

            try:
                # 获取当前页码，用于验证是否需要跳转
                current_page_text = page_input.get_attribute('value') or '1'
                logger.debug(f'当前页码显示为: {current_page_text}')

                # 如果已经在目标页，直接返回成功
                try:
                    current_page_num = int(current_page_text.strip())
                    if current_page_num == page_number:
                        logger.info(f'已经在第{page_number}页，无需跳转')
                        return True
                except ValueError:
                    logger.debug(f"无法解析当前页码'{current_page_text}'，继续执行跳转")

                # 清空输入框并输入目标页码
                page_input.clear()
                sleep(0.2)  # 短暂等待确保清空完成
                page_input.send_keys(str(page_number))
                logger.info(f'已输入目标页码: {page_number}')

                # 按回车键确认
                page_input.send_keys(Keys.ENTER)
                logger.info(f'已按回车键跳转到第{page_number}页')

                # 等待页面跳转
                sleep(2)

                # 验证跳转结果 - 优先通过页码输入框验证
                success = False
                try:
                    # 再次检查页码输入框的值
                    new_page_text = page_input.get_attribute('value') or ''
                    if new_page_text.strip():
                        new_page_num = int(new_page_text.strip())

                        if new_page_num == page_number:
                            success = True
                            logger.info(f'页码验证成功: 当前在第{new_page_num}页')
                        else:
                            logger.warning(
                                f'页码验证失败: 期望第{page_number}页, 实际第{new_page_num}页'
                            )

                            # 如果页码不匹配，可能是跳转到了最后一页
                            if new_page_num > 0 and new_page_num < page_number:
                                logger.info(
                                    f'可能已到最后一页(第{new_page_num}页)，目标页{page_number}超出范围'
                                )
                                return False  # 明确返回失败，表示超出页码范围
                    else:
                        logger.debug('页码输入框为空，尝试通过页面变化验证')

                except (ValueError, AttributeError) as ve:
                    logger.debug(f'无法验证页码: {ve}，尝试通过页面变化验证')

                # 如果页码验证失败，通过页面变化来判断
                if not success:
                    if self._verify_page_changed():
                        success = True
                        logger.info('通过页面变化验证跳转成功')

                if success:
                    logger.info(f'成功跳转到第{page_number}页')
                    return True
                else:
                    logger.warning(f'跳转到第{page_number}页失败或验证失败')
                    return False

            except Exception as input_e:
                logger.warning(f'页码输入操作失败: {input_e}')
                return False

        except Exception as e:
            logger.error(f'跳转到第{page_number}页时发生异常: {e}')
            return False

    def _safe_click_next_page(self) -> bool:
        """
        安全地点击下一页按钮
        :return: 是否成功点击（False表示已到最后一页）
        """
        try:
            # 查找下一页按钮
            next_btn_selectors = [
                'a.pagination-button.pagination-next',
                "a[action='next']",
                '.pagination-next',
                'a.mini-pager-next',
            ]

            next_btn = None
            for selector in next_btn_selectors:
                try:
                    next_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if next_btn and next_btn.is_displayed():
                        break
                except Exception:
                    continue

            if not next_btn:
                logger.info('未找到下一页按钮')
                return False

            # 检查按钮是否被禁用
            btn_classes = next_btn.get_attribute('class') or ''
            if 'disabled' in btn_classes.lower():
                logger.info('下一页按钮已禁用，已到最后一页')
                return False

            # 检查按钮是否可点击
            if not next_btn.is_enabled():
                logger.info('下一页按钮不可用，已到最后一页')
                return False

            # 尝试多种点击方法
            click_methods = [
                ('直接点击', lambda: next_btn.click()),
                (
                    'JavaScript点击',
                    lambda: self.driver.execute_script(
                        'arguments[0].click();', next_btn
                    ),
                ),
                ('Action点击', lambda: self._action_click(next_btn)),
                ('坐标点击', lambda: self._coordinate_click(next_btn)),
            ]

            for method_name, click_func in click_methods:
                try:
                    logger.debug(f'尝试使用{method_name}点击下一页按钮')
                    click_func()
                    sleep(0.5)  # 等待点击生效

                    # 验证是否成功翻页（检查页面是否有变化）
                    if self._verify_page_changed():
                        logger.info(f'使用{method_name}成功点击下一页按钮')
                        return True
                    else:
                        logger.warning(f'{method_name}点击后页面未发生变化')

                except Exception as click_e:
                    logger.warning(f'{method_name}失败: {click_e}')
                    continue

            logger.error('所有点击方法都失败了')
            return False

        except Exception as e:
            log_exception_location(
                '_safe_click_next_page', '安全点击下一页按钮时发生异常'
            )
            logger.error(f'安全点击下一页按钮时发生异常: {e}')
            return False

    def _handle_file_upload(self, contract_no: str, cursor) -> bool:
        """
        处理合同文件上传功能
        :param contract_no: 合同号
        :param cursor: 数据库游标
        :return: 是否成功处理文件上传
        """
        try:
            logger.info(f'合同 {contract_no}: 开始处理文件上传功能')

            # 先移除可能的遮罩层
            self._force_remove_all_masks()

            # 从数据库查询合同文件名
            cursor.execute(
                'SELECT 合同文件名 FROM 品类及合同文件 WHERE 合同编号=?',
                (contract_no,),
            )
            result = cursor.fetchone()

            if not result or not result[0] or str(result[0]).strip() == '':
                logger.warning(f'合同 {contract_no}: 数据库中没有找到合同文件名')
                return False

            contract_filename = str(result[0]).strip()

            # 构造完整的文件路径
            file_path = os.path.join(r'd:\user\合同汇总', contract_filename)

            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f'合同 {contract_no}: 文件不存在 - {file_path}')
                return False

            logger.info(f'合同 {contract_no}: 找到合同文件 - {file_path}')

            # 查找文件上传区域
            upload_area_selectors = [
                '.uc-ywscanfilelist',
                '#ywscanfilelist1',
                '#ywscanfilelist2',
                '#ywscanfilelist3',
                '.mini-webuploader',
                '.file-wrap',
                '.upload-area',
                '.webuploader-container',
            ]

            upload_area = None
            for selector in upload_area_selectors:
                try:
                    upload_area = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if upload_area and upload_area.is_displayed():
                        logger.info(f'合同 {contract_no}: 找到文件上传区域')
                        break
                except Exception:
                    continue

            # 扩展文件输入元素选择器
            file_input_selectors = [
                "#rowuploader_HeTongCompany_001 input[type='file']",
                "#rowuploader_HeTongCompany_002 input[type='file']",
                "input[type='file'][name='file']",
                '.webuploader-element-invisible',
                "input[type='file']",
                "input[accept*='pdf']",
                "input[accept*='doc']",
                '#fileInput',
                '#upload',
                '.file-input',
                "[data-role='file-input']",
            ]

            file_input = None
            for selector in file_input_selectors:
                try:
                    file_inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for input_elem in file_inputs:
                        # 检查元素是否可用
                        if (
                            input_elem.is_displayed()
                            or input_elem.get_attribute('type') == 'file'
                        ):
                            # 检查是否是合同相关的文件输入
                            parent_html = input_elem.get_attribute('outerHTML')

                            # 向上查找包含合同关键词的父级元素
                            current = input_elem
                            for _ in range(10):  # 最多向上查找10层
                                try:
                                    current = current.find_element(By.XPATH, '..')
                                    parent_id = current.get_attribute('id') or ''
                                    parent_class = current.get_attribute('class') or ''

                                    if any(
                                        keyword in parent_id.lower()
                                        or keyword in parent_class.lower()
                                        for keyword in [
                                            'hetong',
                                            'contract',
                                            '合同',
                                            'upload',
                                        ]
                                    ):
                                        file_input = input_elem
                                        logger.info(
                                            f'合同 {contract_no}: 找到合同相关的文件输入元素'
                                        )
                                        break
                                except Exception:
                                    break

                            if file_input:
                                break
                    if file_input:
                        break
                except Exception:
                    continue

            # 如果没找到特定的合同文件输入，使用备用策略
            if not file_input:
                try:
                    all_file_inputs = self.driver.find_elements(
                        By.CSS_SELECTOR, "input[type='file']"
                    )

                    # 过滤掉隐藏的或不可用的输入元素
                    visible_inputs = []
                    for inp in all_file_inputs:
                        try:
                            if (
                                inp.is_displayed()
                                or inp.get_attribute('style')
                                and 'display:none' not in inp.get_attribute('style')
                                or 'webuploader' in inp.get_attribute('class', '')
                            ):
                                visible_inputs.append(inp)
                        except:
                            continue

                    if len(visible_inputs) >= 2:
                        file_input = visible_inputs[1]  # 使用第二个文件输入
                        logger.info(f'合同 {contract_no}: 使用第二个可见文件输入元素')
                    elif len(visible_inputs) >= 1:
                        file_input = visible_inputs[0]  # 如果只有一个，就用这个
                        logger.info(f'合同 {contract_no}: 使用唯一的可见文件输入元素')
                    elif len(all_file_inputs) >= 1:
                        file_input = all_file_inputs[0]  # 兜底方案
                        logger.info(f'合同 {contract_no}: 使用兜底文件输入元素')

                except Exception as e:
                    logger.error(f'合同 {contract_no}: 查找文件输入元素失败: {e}')

            if not file_input:
                logger.error(f'合同 {contract_no}: 未找到可用的文件输入元素')

                # 尝试通过JavaScript创建文件输入
                try:
                    self.driver.execute_script("""
                        var fileInput = document.createElement('input');
                        fileInput.type = 'file';
                        fileInput.id = 'dynamic_file_input';
                        fileInput.style.position = 'absolute';
                        fileInput.style.left = '-9999px';
                        document.body.appendChild(fileInput);
                    """)

                    file_input = self.driver.find_element(By.ID, 'dynamic_file_input')
                    logger.info(f'合同 {contract_no}: 动态创建文件输入元素')

                except Exception as create_e:
                    logger.error(
                        f'合同 {contract_no}: 动态创建文件输入失败: {create_e}'
                    )
                    return False

            # 使用Selenium直接发送文件路径到文件输入元素
            try:
                logger.info(f'合同 {contract_no}: 开始上传文件 - {file_path}')

                # 先移除遮罩层，确保操作不被阻拦
                self._force_remove_all_masks()
                sleep(0.5)

                # 直接向文件输入元素发送文件路径
                file_input.send_keys(file_path)

                logger.info(f'合同 {contract_no}: 文件路径已发送到输入元素')

                # 等待文件上传处理
                sleep(3)  # 增加等待时间

                # 检查是否有上传进度或成功提示
                upload_success_indicators = [
                    '.upload-success',
                    '.file-success',
                    '.upload-complete',
                    "//span[contains(text(), '上传成功')]",
                    "//div[contains(text(), '上传完成')]",
                    '.mini-uploader-list .file-item',
                    '.webuploader-pick',
                    '.file-name',
                    "[data-status='success']",
                    '.progress-bar-success',
                ]

                upload_detected = False
                for indicator in upload_success_indicators:
                    try:
                        if indicator.startswith('//'):
                            elements = self.driver.find_elements(By.XPATH, indicator)
                        else:
                            elements = self.driver.find_elements(
                                By.CSS_SELECTOR, indicator
                            )

                        if elements:
                            for elem in elements:
                                if elem.is_displayed():
                                    logger.info(
                                        f'合同 {contract_no}: 检测到上传成功指示器: {indicator}'
                                    )
                                    upload_detected = True
                                    break
                        if upload_detected:
                            break
                    except Exception:
                        continue

                if upload_detected:
                    logger.info(
                        f'合同 {contract_no}: 文件上传成功 - {contract_filename}'
                    )
                    return True
                else:
                    # 即使没有明确的成功指示器，文件路径发送成功也认为上传完成
                    logger.info(
                        f'合同 {contract_no}: 文件已发送，假设上传成功 - {contract_filename}'
                    )
                    return True

            except Exception as upload_e:
                logger.error(f'合同 {contract_no}: 文件上传过程中发生异常: {upload_e}')
                return False

        except Exception as e:
            log_exception_location(
                '_handle_file_upload', f'处理文件上传时发生异常 - 合同号: {contract_no}'
            )
            logger.error(f'合同 {contract_no}: 处理文件上传时发生异常: {e}')
            return False

    def _handle_contract_submit(self, contract_no: str, cursor) -> bool:
        """
        处理合同提报功能
        :param contract_no: 合同号
        :param cursor: 数据库游标
        :return: 是否成功处理提报
        """
        try:
            logger.info(f'合同 {contract_no}: 开始提报流程')

            # 先移除可能的遮罩层
            self._force_remove_all_masks()
            sleep(1)

            # 查找提报按钮 - 扩展选择器
            submit_button_selectors = [
                "//a[contains(text(), '提报')]",
                "//button[contains(text(), '提报')]",
                "//span[contains(text(), '提报')]",
                "//input[@value='提报']",
                ".mini-btn[title*='提报']",
                "input[value*='提报']",
                '#btn_4b5ebb68-e00f-405c-a8b9-3225ddcfb68d',  # 具体的提报按钮ID
                "a[onclick*='submit']",
                "button[onclick*='submit']",
                '.submit-button',
                '.btn-submit',
                "[data-action='submit']",
                "//a[contains(@href, 'submit')]",
                "//button[contains(@onclick, '提报')]",
                "//span[contains(@onclick, '提报')]",
                ".mini-button[title*='提报']",
                ".mini-button:contains('提报')",
            ]

            submit_button = None
            found_selector = None

            for selector in submit_button_selectors:
                try:
                    if selector.startswith('//'):
                        buttons = self.driver.find_elements(By.XPATH, selector)
                    elif (
                        selector.startswith('.')
                        or selector.startswith('#')
                        or selector.startswith('[')
                    ):
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    else:
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for button in buttons:
                        try:
                            if button and button.is_displayed() and button.is_enabled():
                                # 额外检查按钮文本内容
                                button_text = (
                                    button.text
                                    or button.get_attribute('value')
                                    or button.get_attribute('title')
                                    or ''
                                ).strip()

                                if (
                                    '提报' in button_text
                                    or 'submit' in button_text.lower()
                                ):
                                    submit_button = button
                                    found_selector = selector
                                    logger.info(
                                        f'合同 {contract_no}: 找到提报按钮 - 选择器: {selector}, 文本: {button_text}'
                                    )
                                    break
                        except Exception:
                            continue

                    if submit_button:
                        break

                except Exception:
                    continue

            if not submit_button:
                # 尝试通过JavaScript查找提报按钮
                try:
                    submit_button_js = self.driver.execute_script("""
                        var buttons = [];
                        
                        // 查找所有可能的提报按钮
                        var selectors = [
                            'a', 'button', 'input[type="button"]', 'input[type="submit"]', 
                            '.mini-button', '[role="button"]'
                        ];
                        
                        for (var i = 0; i < selectors.length; i++) {
                            var elements = document.querySelectorAll(selectors[i]);
                            for (var j = 0; j < elements.length; j++) {
                                var elem = elements[j];
                                var text = (elem.textContent || elem.innerText || elem.value || elem.title || '').trim();
                                
                                if (text.includes('提报') || text.toLowerCase().includes('submit')) {
                                    if (elem.offsetWidth > 0 && elem.offsetHeight > 0) {
                                        buttons.push({
                                            element: elem,
                                            text: text,
                                            id: elem.id,
                                            className: elem.className
                                        });
                                    }
                                }
                            }
                        }
                        
                        return buttons.length > 0 ? buttons[0].element : null;
                    """)

                    if submit_button_js:
                        submit_button = submit_button_js
                        logger.info(f'合同 {contract_no}: 通过JavaScript找到提报按钮')

                except Exception as js_e:
                    logger.warning(
                        f'合同 {contract_no}: JavaScript查找提报按钮失败: {js_e}'
                    )

            if not submit_button:
                logger.warning(f'合同 {contract_no}: 未找到提报按钮')

                # 尝试查找所有按钮并记录，帮助调试
                try:
                    all_buttons = self.driver.find_elements(
                        By.CSS_SELECTOR,
                        "button, input[type='button'], input[type='submit'], a, .mini-button",
                    )
                    button_info = []
                    for btn in all_buttons[:10]:  # 只记录前10个，避免日志过长
                        try:
                            if btn.is_displayed():
                                text = (
                                    btn.text
                                    or btn.get_attribute('value')
                                    or btn.get_attribute('title')
                                    or ''
                                ).strip()
                                button_info.append(
                                    f"ID:{btn.get_attribute('id')}, 文本:'{text}', 类名:'{btn.get_attribute('class')}'"
                                )
                        except:
                            continue

                    if button_info:
                        logger.info(
                            f'合同 {contract_no}: 页面上的可见按钮: {button_info}'
                        )

                except Exception:
                    pass

                return False

            # 点击提报按钮
            try:
                # 再次移除遮罩层，确保点击不被阻拦
                self._force_remove_all_masks()
                sleep(0.5)

                # 尝试多种点击方式
                click_success = False

                # 方法1: 直接点击
                try:
                    submit_button.click()
                    click_success = True
                    logger.info(f'合同 {contract_no}: 直接点击提报按钮成功')
                except Exception as direct_e:
                    logger.warning(
                        f'合同 {contract_no}: 直接点击提报按钮失败: {direct_e}'
                    )

                # 方法2: JavaScript点击
                if not click_success:
                    try:
                        self.driver.execute_script(
                            'arguments[0].click();', submit_button
                        )
                        click_success = True
                        logger.info(f'合同 {contract_no}: JavaScript点击提报按钮成功')
                    except Exception as js_click_e:
                        logger.warning(
                            f'合同 {contract_no}: JavaScript点击提报按钮失败: {js_click_e}'
                        )

                # 方法3: 模拟鼠标点击
                if not click_success:
                    try:
                        from selenium.webdriver.common.action_chains import ActionChains

                        ActionChains(self.driver).move_to_element(
                            submit_button
                        ).click().perform()
                        click_success = True
                        logger.info(f'合同 {contract_no}: ActionChains点击提报按钮成功')
                    except Exception as action_e:
                        logger.warning(
                            f'合同 {contract_no}: ActionChains点击提报按钮失败: {action_e}'
                        )

                if not click_success:
                    logger.error(f'合同 {contract_no}: 所有点击方法都失败')
                    return False

                logger.info(f'合同 {contract_no}: 已点击提报按钮')
                sleep(1)  # 等待弹窗加载

                # # 自动点击弹窗"确定"按钮
                # try:
                #     ok_span = self.driver.find_element(
                #         By.XPATH,
                #         "//span[contains(@class, 'mini-button-text') and text()='确定']",
                #     )
                #     ok_button = ok_span.find_element(
                #         By.XPATH, "./ancestor::*[self::a or self::button][1]"
                #     )
                #     if ok_button:
                #         ok_button.click()
                #         logger.info('已点击弹窗的"确定"按钮')
                #         self._close_popup_iframes_only()  # 只关闭弹出iframe，不关闭主表格iframe
                #         self._ensure_main_iframe()
                #         return True
                #     else:
                #         # 处理提报后的审批页面
                #         return self._handle_approval_dialog(contract_no)
                # except Exception:
                #     # logger.warning(f'查找或点击弹窗"确定"按钮失败: {e}')
                #     return False
                return True
            except Exception as click_e:
                logger.warning(f'合同 {contract_no}: 点击提报按钮失败: {click_e}')
                return False

        except Exception as e:
            logger.error(f'合同 {contract_no}: 提报处理异常: {e}')
            return False

    def _handle_approval_dialog(self, contract_no: str) -> bool:
        """
        处理审批对话框
        :param contract_no: 合同号
        :return: 是否成功处理审批
        """
        try:
            logger.info(f'合同 {contract_no}: 开始处理审批对话框')

            # 切换到主页面
            self.driver.switch_to.default_content()

            # 动态查找审批对话框iframe (支持mini-iframe-*)
            approval_iframe = None

            # 首先尝试动态查找所有以mini-iframe-开头的iframe
            try:
                # 查找所有可能的mini-iframe (通过name属性)
                all_iframes_by_name = self.driver.find_elements(
                    By.CSS_SELECTOR, "iframe[name^='mini-iframe-']"
                )

                for iframe in all_iframes_by_name:
                    iframe_name = iframe.get_attribute('name')
                    if (
                        iframe_name
                        and iframe_name.startswith('mini-iframe-')
                        and iframe.is_displayed()
                    ):
                        logger.info(
                            f'合同 {contract_no}: 找到审批对话框iframe: {iframe_name}'
                        )
                        approval_iframe = iframe
                        break

                # 如果通过name属性找不到，尝试通过id属性查找
                if not approval_iframe:
                    all_iframes_by_id = self.driver.find_elements(
                        By.CSS_SELECTOR, "iframe[id^='mini-iframe-']"
                    )

                    for iframe in all_iframes_by_id:
                        iframe_id = iframe.get_attribute('id')
                        if (
                            iframe_id
                            and iframe_id.startswith('mini-iframe-')
                            and iframe.is_displayed()
                        ):
                            logger.info(
                                f'合同 {contract_no}: 通过ID找到审批对话框iframe: {iframe_id}'
                            )
                            approval_iframe = iframe
                            break

            except Exception as e:
                logger.warning(f'合同 {contract_no}: 动态查找审批iframe时出错: {e}')

            # 如果动态查找失败，回退到固定的选择器模式（向后兼容）
            if not approval_iframe:
                logger.info(f'合同 {contract_no}: 动态查找失败，使用固定选择器查找')
            iframe_selectors = [
                "iframe[name='mini-iframe-22']",  # 保留原有选择器作为备选
                "iframe[id*='mini-iframe-22']",
                "iframe[name*='mini-iframe']",  # 更宽泛的查找
                "iframe[id*='mini-iframe']",
            ]

            for selector in iframe_selectors:
                try:
                    approval_iframe = self.driver.find_element(
                        By.CSS_SELECTOR, selector
                    )
                    if approval_iframe and approval_iframe.is_displayed():
                        logger.info(
                            f'合同 {contract_no}: 通过固定选择器找到审批对话框iframe'
                        )
                        break
                except Exception:
                    continue

            if not approval_iframe:
                logger.warning(f'合同 {contract_no}: 未找到审批对话框iframe')
                return False

            # 切换到审批iframe
            self.driver.switch_to.frame(approval_iframe)
            sleep(2)

            # 查找内嵌的iframe (动态查找mini-iframe-*)
            inner_iframe = None

            # 首先尝试查找所有以mini-iframe-开头的iframe
            try:
                # 查找所有可能的mini-iframe
                all_iframes = self.driver.find_elements(
                    By.CSS_SELECTOR, "iframe[name^='mini-iframe-']"
                )

                for iframe in all_iframes:
                    iframe_name = iframe.get_attribute('name')
                    if (
                        iframe_name
                        and iframe_name.startswith('mini-iframe-')
                        and iframe.is_displayed()
                    ):
                        logger.info(
                            f'合同 {contract_no}: 找到内嵌审批iframe: {iframe_name}'
                        )
                        inner_iframe = iframe
                        break

                # 如果上述方法失败，尝试通过id属性查找
                if not inner_iframe:
                    all_iframes_by_id = self.driver.find_elements(
                        By.CSS_SELECTOR, "iframe[id^='mini-iframe-']"
                    )
                    for iframe in all_iframes_by_id:
                        iframe_id = iframe.get_attribute('id')
                        if (
                            iframe_id
                            and iframe_id.startswith('mini-iframe-')
                            and iframe.is_displayed()
                        ):
                            logger.info(
                                f'合同 {contract_no}: 通过ID找到内嵌审批iframe: {iframe_id}'
                            )
                            inner_iframe = iframe
                            break

            except Exception as e:
                logger.warning(f'合同 {contract_no}: 动态查找iframe时出错: {e}')

            # 如果动态查找失败，回退到原有的固定查找方式
            if not inner_iframe:
                inner_iframe_selectors = [
                    "iframe[name='mini-iframe-188']",
                    "iframe[id*='mini-iframe-188']",
                    "iframe[name*='mini-iframe']",
                    "iframe[id*='mini-iframe']",
                    "iframe[name*='mini-iframeiframe']",  # 新增支持mini-iframeiframe-模式
                    "iframe[id*='mini-iframeiframe']",
                ]

                for selector in inner_iframe_selectors:
                    try:
                        inner_iframe = self.driver.find_element(
                            By.CSS_SELECTOR, selector
                        )
                        if inner_iframe and inner_iframe.is_displayed():
                            logger.info(
                                f'合同 {contract_no}: 通过固定选择器找到内嵌审批iframe'
                            )
                            break
                    except Exception:
                        continue

            if not inner_iframe:
                logger.warning(f'合同 {contract_no}: 未找到内嵌审批iframe')
                return False

            # 切换到内嵌iframe
            self.driver.switch_to.frame(inner_iframe)
            sleep(2)

            # 填写签署意见"同意"
            opinion_filled = self._fill_approval_opinion(contract_no)

            if opinion_filled:
                self.driver.switch_to.default_content()
                all_iframes_by_name = self.driver.find_elements(
                    By.CSS_SELECTOR, "iframe[name^='mini-iframe-']"
                )
                for iframe in all_iframes_by_name:
                    iframe_name = iframe.get_attribute('name')
                    if iframe_name and iframe_name.startswith('mini-iframe-'):
                        self.driver.switch_to.frame(iframe)
                        break

                element = self.wait.until(
                    EC.visibility_of_element_located(
                        (
                            By.XPATH,
                            "//a[.//span[@class='mini-button-text' and text()='确定']]",
                        )
                    )
                )
                element.click()
                sleep(0.5)
                return True
            else:
                return False
        except Exception as e:
            logger.error(f'合同 {contract_no}: 处理审批对话框异常: {e}')
            return False

    def _fill_approval_opinion(self, contract_no: str) -> bool:
        """
        填写签署意见
        :param contract_no: 合同号
        :return: 是否成功填写
        """
        try:
            div = self.driver.find_element(By.CSS_SELECTOR, "div[class='opinion-text']")
            self.driver.execute_script(
                'arguments[0].innerText = arguments[1];', div, '同意'
            )
            sleep(0.5)
            sure_button = self.driver.find_element(By.ID, 'btnsubmit')
            sure_button.click()
            sleep(0.5)
            return True

        except Exception as e:
            logger.error(f'合同 {contract_no}: 填写签署意见异常: {e}')
            return False

    def _handle_save_success_dialog(self, contract_no: str) -> bool:
        """
        处理保存成功后的确认对话框
        :param contract_no: 合同编号
        :return: 是否成功处理确认对话框
        """
        try:
            logger.info(f'合同 {contract_no}: 查找保存成功确认对话框')

            # 等待对话框出现
            sleep(1)

            # 查找可能的确定按钮
            confirm_button_selectors = [
                "//button[contains(text(), '确定')]",
                "//a[contains(text(), '确定')]",
                "//input[@value='确定']",
                "//span[contains(text(), '确定')]",
                '.mini-messagebox-button.ok',
                '.mini-button.ok',
                '.confirm-btn',
                'button.mini-messagebox-button',
                'a.mini-messagebox-button',
            ]

            for selector in confirm_button_selectors:
                try:
                    if selector.startswith('//'):
                        confirm_button = self.driver.find_element(By.XPATH, selector)
                    else:
                        confirm_button = self.driver.find_element(
                            By.CSS_SELECTOR, selector
                        )

                    if (
                        confirm_button
                        and confirm_button.is_displayed()
                        and confirm_button.is_enabled()
                    ):
                        confirm_button.click()
                        logger.info(f'合同 {contract_no}: 已点击保存成功确认按钮')
                        sleep(1)  # 等待对话框关闭
                        return True

                except Exception:
                    continue

            logger.debug(f'合同 {contract_no}: 未找到保存成功确认按钮，可能自动关闭')
            return True  # 即使没找到也认为是正常的

        except Exception as e:
            logger.warning(f'合同 {contract_no}: 处理保存成功确认对话框时出错: {e}')
            return True  # 出错也认为是正常的


def main():
    log_path = os.path.join(os.path.dirname(__file__), 'crawler.log')
    with open(log_path, 'w', encoding='utf-8'):
        pass
    config = Config()
    crawler = ContractCrawler(config)
    try:
        start_time = time.time()
        crawler.crawl_data()
        duration = (time.time() - start_time) / 60
        logger.info('任务完成，运行时间：%.2f 分钟', duration)
    except Exception as e:
        log_exception_location('main', '程序主函数执行失败')
        logger.error('程序执行失败: %s', e)
        sys.exit(1)


if __name__ == '__main__':
    main()
