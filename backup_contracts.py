# 作者: KingFreeDom
# 创建时间         : 2025-04-25 14:11:50
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-04-28 10:30:58
# 文件相对于项目的路径   : \Crawl_AI\zcg.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
#!/usr/bin/env python
# -*- coding:utf-8 -*-

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
import sys
from typing import Optional
from dataclasses import dataclass
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    StaleElementReferenceException,
)
from time import sleep
import sqlite3
from datetime import datetime
from selenium.webdriver.common.keys import Keys
from dateutil.relativedelta import relativedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),  # 文件使用UTF-8编码
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


@dataclass
class Config:
    """配置类"""

    CHROME_DRIVER_PATH: str = (
        r'C:\Program Files\Google\Chrome\Application\chromedriver.exe'
    )
    LOGIN_URL: str = 'https://sso.crrcgo.cc/login?v=1&client=cangqiong&returnUrl=https%3A%2F%2Fportal.crrcgo.cc%2F%2F%3F&isPortalMobile=false'
    USERNAME: str = '010800006291'
    PASSWORD: str = 'Z6h2en91@'
    MAX_RETRIES: int = 3
    WAIT_TIMEOUT: int = 20  # 增加等待时间
    PAGE_LOAD_TIMEOUT: int = 30  # 页面加载超时时间
    RETRY_INTERVAL: int = 2  # 重试间隔时间


def robust_execute(cursor, sql, params=(), max_retries=5, retry_interval=1):
    for attempt in range(max_retries):
        try:
            cursor.execute(sql, params)
            return
        except sqlite3.OperationalError as e:
            if 'database is locked' in str(e):
                if attempt < max_retries - 1:
                    time.sleep(retry_interval)
                    continue
            raise


class ContractCrawler:
    def __init__(self, config: Config):
        self.config = config
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        try:
            from ddddocr import DdddOcr

            self.ocr = DdddOcr(show_ad=False)
        except ImportError as e:
            logger.error('OCR初始化失败，请确保已安装 ddddocr: %s', e)
            sys.exit("缺少 ddddocr 库，请运行 'pip install ddddocr'")

    def setup_driver(self):
        if self.driver and self.wait:
            return
        options = webdriver.ChromeOptions()
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('log-level=3')
        options.add_experimental_option('excludeSwitches', ['enable-logging'])
        options.page_load_strategy = 'normal'
        service = Service(self.config.CHROME_DRIVER_PATH)
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.maximize_window()
        self.driver.set_page_load_timeout(self.config.PAGE_LOAD_TIMEOUT)
        self.wait = WebDriverWait(self.driver, self.config.WAIT_TIMEOUT)
        logger.info('浏览器初始化完成')

    def handle_captcha(self) -> str:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        try:
            captcha_img = self.wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//img[@src='/getCode' and contains(@class, 'validImg')]",
                    )
                )
            )
            time.sleep(1.5)  # 增加等待时间，确保验证码图片完全加载
            img_bytes = captcha_img.screenshot_as_png
            if not img_bytes:
                raise Exception('无法获取验证码截图')

            # 给OCR识别过程更多时间
            time.sleep(0.5)
            code = self.ocr.classification(img_bytes)
            if isinstance(code, dict) and 'result' in code:
                code = code['result']
            logger.info('验证码识别结果: %s', code)
            if not code or not isinstance(code, str) or len(code) != 4:
                logger.warning('验证码识别结果异常: %s', code)
                refresh_button = self.driver.find_element(
                    By.XPATH, "//img[@src='/getCode']/following-sibling::span"
                )
                refresh_button.click()
                time.sleep(1)
                return self.handle_captcha()
            return code

        except (TimeoutException, NoSuchElementException) as e:
            logger.error('未找到验证码图片元素: %s', e)
            raise
        except Exception as e:
            logger.error('验证码处理失败: %s', e)
            raise

    def fetch_page_data(self, page_num):
        self.setup_driver()
        if not self.driver:
            raise Exception('Driver 未初始化')

        page_data = []
        try:
            self.driver.get(f'{self.config.LOGIN_URL}?page={page_num}')
            table = self.wait.until(
                EC.presence_of_element_located((By.XPATH, '//table'))
            )
            rows = table.find_elements(By.TAG_NAME, 'tr')
            for row in rows:
                cols = row.find_elements(By.TAG_NAME, 'td')
                if cols:
                    page_data.append([col.text for col in cols])
            return page_data

        except Exception as e:
            logger.error(f'获取第 {page_num} 页数据失败: {e}')
            raise

    def login(self) -> bool:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        try:
            try:
                self.driver.get(self.config.LOGIN_URL)
            except TimeoutException:
                logger.warning('页面加载超时，尝试继续操作')
                self.driver.execute_script('window.stop();')

            username_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, 'username'))
            )
            password_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, 'password'))
            )

            username_input.send_keys(self.config.USERNAME)
            password_input.send_keys(self.config.PASSWORD)

            for attempt in range(self.config.MAX_RETRIES):
                try:
                    code = self.handle_captcha()
                    validcode_input = self.wait.until(
                        EC.presence_of_element_located((By.NAME, 'validCode'))
                    )
                    validcode_input.clear()
                    validcode_input.send_keys(code)

                    login_button = self.wait.until(
                        EC.element_to_be_clickable((By.CLASS_NAME, 'loginBtn'))
                    )
                    login_button.click()

                    try:
                        e_procurement_button = self.wait.until(
                            EC.element_to_be_clickable(
                                (
                                    By.XPATH,
                                    "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]",
                                )
                            )
                        )
                        logger.info('找到电子采购按钮')
                        e_procurement_button.click()

                        try:
                            self.wait.until(EC.number_of_windows_to_be(2))
                            self.driver.switch_to.window(self.driver.window_handles[-1])
                            self.wait.until(
                                EC.presence_of_element_located((By.TAG_NAME, 'body'))
                            )
                            logger.info('电子采购页面已加载')
                            sleep(5)
                            return True
                        except TimeoutException:
                            logger.warning('等待新窗口超时，尝试直接继续')
                            if len(self.driver.window_handles) > 1:
                                self.driver.switch_to.window(
                                    self.driver.window_handles[-1]
                                )
                                return True
                            raise Exception('无法切换到新窗口')
                    except TimeoutException:
                        error_message = self.driver.find_elements(
                            By.CLASS_NAME, 'el-message__content'
                        )
                        if error_message and '验证码错误' in error_message[0].text:
                            if attempt < self.config.MAX_RETRIES - 1:
                                logger.warning('验证码错误，重试中...')
                                continue
                        raise Exception('登录失败')

                except Exception as e:
                    logger.warning('登录尝试 %d 失败: %s', attempt + 1, e)
                    if attempt == self.config.MAX_RETRIES - 1:
                        raise

            return False

        except Exception as e:
            logger.error('登录过程发生错误: %s', e)
            raise

    def navigate_to_contracts(self) -> None:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        max_retries = 3
        for attempt in range(max_retries):
            try:
                if len(self.driver.window_handles) > 1:
                    self.driver.switch_to.window(self.driver.window_handles[-1])

                def retry_click(xpath, max_retries=3):
                    for i in range(max_retries):
                        try:
                            element = self.wait.until(
                                EC.element_to_be_clickable((By.XPATH, xpath))
                            )
                            element.click()
                            return True
                        except (TimeoutException, StaleElementReferenceException) as e:
                            if i == max_retries - 1:
                                raise
                            logger.warning(f'点击元素失败，重试中: {e}')
                            time.sleep(self.config.RETRY_INTERVAL)
                    return False

                # 尝试点击电子合同管理菜单
                try:
                    retry_click('//*[@id="theme-side"]/div[1]/div[11]')
                    logger.info('已点击电子合同管理菜单')

                    retry_click('//*[@id="theme-side"]/div[1]/div[11]/div/div[2]/a')
                    logger.info('已点击采购合同备案菜单')

                    sleep(5)

                    # 验证是否成功到达目标页面
                    try:
                        self.driver.switch_to.default_content()
                        iframe = self.wait.until(
                            EC.presence_of_element_located(
                                (By.ID, 'tab-content-95130002')
                            )
                        )
                        logger.info('成功导航到合同备案页面')
                        return  # 成功则直接返回
                    except TimeoutException:
                        logger.warning(
                            f'第{attempt + 1}次导航后未找到目标iframe，可能页面无响应'
                        )
                        raise Exception('页面无响应')

                except Exception as e:
                    logger.warning(f'第{attempt + 1}次导航尝试失败: {e}')
                    if attempt < max_retries - 1:
                        logger.info('页面可能无响应，尝试刷新页面并重试...')
                        self._refresh_and_wait()
                        continue
                    else:
                        raise

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f'导航失败，第{attempt + 1}次重试: {e}')
                    self._refresh_and_wait()
                else:
                    logger.error('导航到合同备案页面失败: %s', e)
                    raise

    def _refresh_and_wait(self):
        """刷新页面并等待加载完成"""
        try:
            logger.info('正在刷新页面...')
            self.driver.refresh()
            sleep(3)  # 等待页面开始加载

            # 等待页面加载完成
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, 'body')))
            sleep(2)  # 额外等待确保页面完全加载

            logger.info('页面刷新完成')
        except Exception as e:
            logger.warning(f'页面刷新时出现异常: {e}')

    def _navigate_to_procurement_from_login(self) -> bool:
        """
        从登录后页面重新导航到采购页面
        :return: 是否成功导航
        """
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f'第{attempt + 1}次尝试从登录后页面重新导航到采购页面')

                # 确保在主窗口
                if len(self.driver.window_handles) > 1:
                    # 关闭其他窗口，只保留主窗口
                    main_window = self.driver.window_handles[0]
                    for handle in self.driver.window_handles[1:]:
                        self.driver.switch_to.window(handle)
                        self.driver.close()
                    self.driver.switch_to.window(main_window)
                    logger.info('已关闭其他窗口，回到主窗口')

                # 等待页面加载
                sleep(2)

                # 查找并点击电子采购按钮
                try:
                    e_procurement_button = self.wait.until(
                        EC.element_to_be_clickable(
                            (
                                By.XPATH,
                                "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]",
                            )
                        )
                    )
                    logger.info('找到电子采购按钮')
                    e_procurement_button.click()

                    # 等待新窗口打开
                    try:
                        self.wait.until(EC.number_of_windows_to_be(2))
                        self.driver.switch_to.window(self.driver.window_handles[-1])
                        self.wait.until(
                            EC.presence_of_element_located((By.TAG_NAME, 'body'))
                        )
                        logger.info('电子采购页面已加载')
                        sleep(3)

                        # 导航到合同备案页面
                        self.navigate_to_contracts()
                        logger.info('成功重新导航到采购页面')
                        return True

                    except TimeoutException:
                        logger.warning('等待新窗口超时')
                        if len(self.driver.window_handles) > 1:
                            self.driver.switch_to.window(self.driver.window_handles[-1])
                            self.navigate_to_contracts()
                            return True
                        raise Exception('无法打开电子采购页面')

                except TimeoutException:
                    logger.error('未找到电子采购按钮')
                    raise Exception('未找到电子采购按钮')

            except Exception as e:
                logger.warning(f'第{attempt + 1}次重新导航失败: {e}')
                if attempt < max_retries - 1:
                    logger.info('页面可能无响应，尝试刷新页面并重试...')
                    self._refresh_and_wait()
                else:
                    logger.error(f'重新导航到采购页面失败: {e}')
                    return False

        return False

    def _check_if_in_procurement_page(self) -> bool:
        """
        检查当前是否在采购页面，如果检查失败尝试刷新
        :return: 是否在采购页面
        """
        max_retries = 2
        for attempt in range(max_retries):
            try:
                # 检查是否有多个窗口（采购页面应该有两个窗口）
                if len(self.driver.window_handles) < 2:
                    logger.info('窗口数量不足，不在采购页面')
                    return False

                # 切换到最后一个窗口（采购页面窗口）
                self.driver.switch_to.window(self.driver.window_handles[-1])

                # 检查页面特征
                check_script = """
                // 检查是否有采购页面的特征元素
                var hasElectronicContract = document.querySelector('#theme-side');
                var hasContractMenu = document.querySelector('a[href*="contractproduction"]');
                var hasAddButton = false;
                
                // 尝试切换到iframe检查新增按钮
                try {
                    var iframe = document.getElementById('tab-content-95130002');
                    if (iframe) {
                        // 如果能找到iframe，说明在正确的页面结构中
                        return {
                            inProcurementPage: true,
                            hasElectronicContract: !!hasElectronicContract,
                            hasContractMenu: !!hasContractMenu,
                            hasIframe: true
                        };
                    }
                } catch(e) {
                    console.log('检查iframe失败:', e.message);
                }
                
                return {
                    inProcurementPage: false,
                    hasElectronicContract: !!hasElectronicContract,
                    hasContractMenu: !!hasContractMenu,
                    hasIframe: false
                };
                """

                result = self.driver.execute_script(check_script)
                logger.info(f'采购页面检查结果: {result}')

                if result.get('inProcurementPage', False):
                    return True

                # 如果第一次检查失败且还有重试机会，尝试刷新
                if attempt < max_retries - 1:
                    logger.warning('页面检查失败，可能页面无响应，尝试刷新...')
                    self._refresh_and_wait()
                    continue

                return False

            except Exception as e:
                logger.warning(f'检查采购页面失败: {e}')
                if attempt < max_retries - 1:
                    logger.info('检查失败，尝试刷新页面...')
                    self._refresh_and_wait()
                else:
                    return False

        return False

    def crawl_data(self) -> None:
        try:
            self.setup_driver()
            if not self.login():
                raise Exception('登录失败')
            self.navigate_to_contracts()
            self.write_to_tables()

        except Exception as e:
            logger.error('数据抓取过程发生错误: %s', e)
            raise
        finally:
            if self.driver:
                try:
                    self.driver.quit()
                    logger.info('浏览器已关闭')
                except Exception as e:
                    logger.warning(f'关闭浏览器时发生错误: {e}')

    def set_dropdown_safely(
        self, dropdown_id: str, preferred_text: str, fallback_value: str = ''
    ) -> bool:
        """
        安全地设置下拉菜单选项
        :param dropdown_id: 下拉菜单的ID
        :param preferred_text: 首选的文本值
        :param fallback_value: 如果找不到首选文本时的备用值
        :return: 是否设置成功
        """
        try:
            # 先检查下拉菜单是否存在
            check_script = f"""
            var combo = mini.get("{dropdown_id}");
            return combo ? true : false;
            """

            exists = self.driver.execute_script(check_script)
            if not exists:
                logger.warning(f'下拉菜单 {dropdown_id} 不存在')
                return False

            # 获取下拉菜单的所有选项
            script = f"""
            var combo = mini.get("{dropdown_id}");
            if (combo) {{
                try {{
                    var data = combo.getData();
                    var options = [];
                    if (data && data.length) {{
                        for (var i = 0; i < data.length; i++) {{
                            var item = data[i];
                            options.push({{
                                value: item.value !== undefined ? item.value : (item.id !== undefined ? item.id : i),
                                text: item.text || item.name || item.label || String(item.value || item.id || i)
                            }});
                        }}
                    }}
                    return options;
                }} catch(e) {{
                    console.log("获取下拉选项时出错: " + e.message);
                    return [];
                }}
            }}
            return [];
            """

            options = self.driver.execute_script(script)
            logger.info(f'下拉菜单 {dropdown_id} 的可选项: {options}')

            if not options:
                logger.warning(f'下拉菜单 {dropdown_id} 没有可选项')
                return False

            # 查找匹配的选项
            selected_option = None

            # 首先尝试精确匹配文本
            for option in options:
                if str(option.get('text', '')).strip() == preferred_text.strip():
                    selected_option = option
                    break

            # 如果没有精确匹配，尝试包含匹配
            if not selected_option:
                for option in options:
                    option_text = str(option.get('text', ''))
                    if (
                        preferred_text.strip() in option_text
                        or option_text in preferred_text.strip()
                    ):
                        selected_option = option
                        break

            # 如果还是没有找到，使用备用值或第一个选项
            if not selected_option:
                if fallback_value:
                    for option in options:
                        if (
                            str(option.get('value', '')).strip()
                            == str(fallback_value).strip()
                        ):
                            selected_option = option
                            break

                if not selected_option:
                    selected_option = options[0]  # 使用第一个选项
                    logger.warning(
                        f"未找到匹配项 '{preferred_text}'，使用第一个选项: {selected_option}"
                    )

            if selected_option:
                # JavaScript字符串转义函数
                def escape_js_string(s):
                    """转义JavaScript字符串中的特殊字符"""
                    if s is None:
                        return ''
                    s = str(s)
                    # 转义反斜杠
                    s = s.replace('\\', '\\\\')
                    # 转义单引号
                    s = s.replace("'", "\\'")
                    # 转义双引号
                    s = s.replace('"', '\\"')
                    # 转义换行符
                    s = s.replace('\n', '\\n')
                    s = s.replace('\r', '\\r')
                    # 转义制表符
                    s = s.replace('\t', '\\t')
                    return s

                # 转义值和文本
                escaped_value = escape_js_string(selected_option['value'])
                escaped_text = escape_js_string(selected_option['text'])

                # 使用更安全的设置方法
                set_script = f"""
                var combo = mini.get("{dropdown_id}");
                if (combo) {{
                    try {{
                        combo.setValue("{escaped_value}");
                        combo.setText("{escaped_text}");
                        if (combo.doValueChanged) {{
                            combo.doValueChanged();
                        }}
                        if (combo.fire) {{
                            combo.fire("valuechanged");
                        }}
                        return true;
                    }} catch(e) {{
                        console.log("设置下拉值时出错: " + e.message);
                        return false;
                    }}
                }}
                return false;
                """

                result = self.driver.execute_script(set_script)
                if result:
                    logger.info(
                        f'成功设置下拉菜单 {dropdown_id} 为: {selected_option["text"]} (值: {selected_option["value"]})'
                    )
                else:
                    logger.error(f'设置下拉菜单 {dropdown_id} 失败')
                return result
            else:
                logger.warning(f'未找到下拉菜单 {dropdown_id} 的任何合适选项')
                return False

        except Exception as e:
            logger.error(f'设置下拉菜单 {dropdown_id} 时发生异常: {e}')
            return False

    def set_text_input_safely(
        self, element_id: str, text_value: str, field_name: str = ''
    ) -> bool:
        """
        安全地设置文本输入框的值
        :param element_id: 输入框的ID
        :param text_value: 要输入的文本值
        :param field_name: 字段名称（用于日志）
        :return: 是否设置成功
        """
        try:
            text_input = self.wait.until(
                EC.element_to_be_clickable((By.ID, element_id))
            )

            # 清空输入框
            text_input.clear()
            sleep(0.2)  # 短暂等待确保清空完成

            # 输入文本
            text_input.send_keys(str(text_value))

            # 触发change事件
            self.driver.execute_script(
                "arguments[0].dispatchEvent(new Event('change', {bubbles:true}));",
                text_input,
            )

            logger.info(f'{field_name}填写成功: {text_value}')
            return True

        except Exception as e:
            logger.error(f'填写{field_name}失败: {e}')
            return False

    def set_radio_button_safely(self, radio_group_id: str, target_text: str) -> bool:
        """
        安全地设置单选按钮组选项 - 恢复原来正确的逻辑
        :param radio_group_id: 单选按钮组的基础ID（如"mini-28"）
        :param target_text: 目标选项的文本（如"否"）
        :return: 是否设置成功
        """
        try:
            # 特殊处理：is_sp_upload_file 实际对应 mini-28
            actual_group_id = radio_group_id
            if radio_group_id == 'is_sp_upload_file':
                actual_group_id = 'mini-28'

            # 查找所有相关的单选按钮
            find_buttons_script = f"""
            var buttons = [];
            var elements = document.querySelectorAll('[id^="{actual_group_id}$ck$"]');
            for (var i = 0; i < elements.length; i++) {{
                var input = elements[i];
                var label = document.querySelector('label[for="' + input.id + '"]');
                if (label) {{
                    buttons.push({{
                        id: input.id,
                        value: input.value,
                        text: (label.textContent || label.innerText).trim(),
                        checked: input.checked
                    }});
                }}
            }}
            return buttons;
            """

            buttons = self.driver.execute_script(find_buttons_script)
            logger.info(f'单选按钮组 {radio_group_id} 的选项: {buttons}')

            if not buttons:
                logger.warning(f'未找到单选按钮组 {radio_group_id}')
                return False

            # 查找目标选项
            target_button = None
            for button in buttons:
                if button['text'] == target_text.strip():
                    target_button = button
                    break

            if not target_button:
                logger.error(
                    f"未找到文本为 '{target_text}' 的选项，可用选项: {[b['text'] for b in buttons]}"
                )
                return False

            # 使用正确的方法：同时设置CSS类和隐藏字段
            set_radio_script = f"""
            try {{
                // 1. 先移除所有同组选项的selected类
                var allItems = document.querySelectorAll('[id^="{actual_group_id}$"][class*="mini-radiobuttonlist-item"]');
                for (var i = 0; i < allItems.length; i++) {{
                    allItems[i].classList.remove('mini-radiobuttonlist-item-selected');
                }}
                
                // 2. 找到目标input和它的容器
                var targetInput = document.getElementById("{target_button['id']}");
                if (!targetInput) {{
                    return {{success: false, error: "未找到目标input"}};
                }}
                
                var targetContainer = targetInput.closest('.mini-radiobuttonlist-item');
                if (!targetContainer) {{
                    return {{success: false, error: "未找到目标容器"}};
                }}
                
                // 3. 设置radio的checked状态
                targetInput.checked = true;
                targetInput.dispatchEvent(new Event('change', {{bubbles:true}}));
                
                // 4. 添加selected类到目标容器
                targetContainer.classList.add('mini-radiobuttonlist-item-selected');
                
                // 5. 设置对应的隐藏字段值 - 特殊处理
                var hiddenFieldId = "{radio_group_id}$value";
                if ("{radio_group_id}" === "is_sp_upload_file") {{
                    hiddenFieldId = "is_sp_upload_file$value";
                }} else if ("{radio_group_id}" === "mini-46") {{
                    hiddenFieldId = "zhibaojin$value";
                }}
                
                var hiddenField = document.getElementById(hiddenFieldId);
                if (hiddenField) {{
                    hiddenField.setAttribute('value', "{target_button['value']}");
                    hiddenField.value = "{target_button['value']}";
                }}
                
                // 6. 尝试通过Mini UI API设置（如果可用）
                try {{
                    var miniComponent = mini.get("{radio_group_id}");
                    if (miniComponent && miniComponent.setValue) {{
                        miniComponent.setValue("{target_button['value']}");
                        if (miniComponent.doValueChanged) {{
                            miniComponent.doValueChanged();
                        }}
                        // 触发其他可能的事件
                        if (miniComponent.fire) {{
                            miniComponent.fire("valuechanged");
                            miniComponent.fire("change");
                        }}
                    }}
                }} catch(miniError) {{
                    console.log("Mini UI API调用失败: " + miniError.message);
                }}
                
                // 7. 额外触发容器点击事件（模拟真实用户点击）
                try {{
                    var clickEvent = new MouseEvent('click', {{
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }});
                    targetContainer.dispatchEvent(clickEvent);
                    
                    // 也尝试触发span的点击（有些UI可能监听这个）
                    var iconSpan = targetContainer.querySelector('.mini-list-icon');
                    if (iconSpan) {{
                        iconSpan.dispatchEvent(clickEvent);
                    }}
                }} catch(clickError) {{
                    console.log("触发点击事件失败: " + clickError.message);
                }}
                
                // 8. 验证最终状态
                var finalValue = hiddenField ? hiddenField.value : 'no hidden field';
                var hasSelectedClass = targetContainer.classList.contains('mini-radiobuttonlist-item-selected');
                var isChecked = targetInput.checked;
                
                return {{
                    success: true,
                    id: "{target_button['id']}",
                    value: "{target_button['value']}",
                    hiddenFieldValue: finalValue,
                    hasSelectedClass: hasSelectedClass,
                    isChecked: isChecked
                }};
                
            }} catch(e) {{
                return {{success: false, error: e.message}};
            }}
            """

            result = self.driver.execute_script(set_radio_script)

            if result.get('success'):
                logger.info(f'成功设置单选按钮: {target_text}')
                logger.info(f'  - ID: {result.get("id")}')
                logger.info(f'  - 值: {result.get("value")}')
                logger.info(f'  - 隐藏字段值: {result.get("hiddenFieldValue")}')
                logger.info(f'  - CSS类状态: {result.get("hasSelectedClass")}')
                logger.info(f'  - checked状态: {result.get("isChecked")}')
                return True
            else:
                logger.error(f'设置单选按钮失败: {result.get("error")}')
                return False

        except Exception as e:
            logger.error(f'设置单选按钮组 {radio_group_id} 时发生异常: {e}')
            return False

    def save_contract_safely(self) -> bool:
        """
        安全地保存合同，包含错误检查
        :return: 是否保存成功
        """
        try:
            # 首先检查保存按钮是否存在和可用
            check_button_script = """
            var saveButton = document.getElementById('btn_5b8e7420-ef55-41db-8ef6-b04ea5c1c8eb');
            if (saveButton) {
                return {
                    exists: true,
                    visible: saveButton.offsetWidth > 0 && saveButton.offsetHeight > 0,
                    enabled: !saveButton.disabled && !saveButton.classList.contains('disabled'),
                    text: saveButton.textContent || saveButton.innerText || ''
                };
            }
            return {exists: false};
            """

            button_info = self.driver.execute_script(check_button_script)

            if not button_info.get('exists'):
                logger.error('保存按钮不存在')
                return False

            if not button_info.get('visible'):
                logger.error('保存按钮不可见')
                return False

            if not button_info.get('enabled'):
                logger.error('保存按钮不可点击')
                return False

            logger.info(f'保存按钮状态正常: {button_info.get("text", "").strip()}')

            # 点击保存修改按钮
            save_button = self.wait.until(
                EC.element_to_be_clickable(
                    (
                        By.ID,
                        'btn_5b8e7420-ef55-41db-8ef6-b04ea5c1c8eb',
                    )
                )
            )
            save_button.click()
            logger.info('已点击保存修改按钮')

            # 等待保存操作开始
            sleep(0.5)

            # 检查是否有加载指示器出现（表明正在保存）
            loading_check_script = """
            var button = document.getElementById('btn_5b8e7420-ef55-41db-8ef6-b04ea5c1c8eb');
            if (button) {
                var loadingIcon = button.querySelector('.mini-button-loading-icon');
                var isLoading = loadingIcon && loadingIcon.style.display !== 'none';
                return {hasLoading: isLoading};
            }
            return {hasLoading: false};
            """

            loading_info = self.driver.execute_script(loading_check_script)
            if loading_info.get('hasLoading'):
                logger.info('检测到加载状态，等待保存完成...')
                sleep(1)

            # 等待足够的时间确保保存操作完成
            sleep(1.5)

            # 检查是否有成功保存的确认弹窗
            success_popup_script = """
            var popup = document.querySelector('.mini-messagebox');
            if (popup && popup.offsetWidth > 0 && popup.offsetHeight > 0) {
                var message = popup.querySelector('.mini-messagebox-msg');
                var okButton = popup.querySelector('.mini-messagebox-button.ok');
                
                if (message) {
                    var messageText = message.textContent || message.innerText;
                    if (messageText.includes('保存成功') || messageText.includes('成功')) {
                        return {
                            isSuccessPopup: true,
                            message: messageText,
                            hasOkButton: okButton !== null,
                            okButtonId: okButton ? okButton.id : null
                        };
                    }
                }
            }
            return {isSuccessPopup: false};
            """

            success_popup_info = self.driver.execute_script(success_popup_script)

            if success_popup_info.get('isSuccessPopup'):
                logger.info(f'检测到保存成功弹窗: {success_popup_info.get("message")}')

                if success_popup_info.get('hasOkButton'):
                    ok_button_id = success_popup_info.get('okButtonId')
                    try:
                        ok_button = self.wait.until(
                            EC.element_to_be_clickable((By.ID, ok_button_id))
                        )
                        ok_button.click()
                        logger.info(f'已点击保存成功弹窗确定按钮 (ID: {ok_button_id})')

                        # 等待弹窗消失
                        sleep(0.5)

                    except Exception as e:
                        logger.error(f'点击保存成功弹窗确定按钮失败: {e}')
                        return False

            # 检查是否有错误信息出现
            error_check_script = """
            var errorInfo = {
                hasErrors: false,
                errorMessages: [],
                hasErrorPopup: false,
                popupInfo: null
            };
            
            // 检查是否有错误弹窗
            var messagebox = document.querySelector('.mini-messagebox');
            if (messagebox && messagebox.offsetWidth > 0 && messagebox.offsetHeight > 0) {
                var messageDiv = messagebox.querySelector('.mini-messagebox-msg');
                if (messageDiv) {
                    var messageText = (messageDiv.textContent || messageDiv.innerText || '').trim();
                    // 判断是否是错误信息（包含错误、失败等关键词，且不包含成功）
                    if (messageText && !messageText.includes('成功') && 
                        (messageText.includes('错误') || messageText.includes('失败') || 
                         messageText.includes('不能') || messageText.includes('无法') ||
                         messageText.includes('请') || messageText.includes('必须'))) {
                        
                        errorInfo.hasErrorPopup = true;
                        errorInfo.hasErrors = true;
                        errorInfo.errorMessages.push(messageText);
                        
                        // 查找确定按钮
                        var okButton = messagebox.querySelector('.mini-messagebox-button');
                        if (!okButton) {
                            okButton = messagebox.querySelector('input[type="button"]');
                        }
                        if (!okButton) {
                            okButton = messagebox.querySelector('button');
                        }
                        
                        errorInfo.popupInfo = {
                            message: messageText,
                            hasOkButton: okButton !== null,
                            okButtonText: okButton ? (okButton.value || okButton.textContent || okButton.innerText) : null,
                            okButtonId: okButton ? okButton.id : null
                        };
                    }
                }
            }
            
            // 检查其他错误元素
            var errorSelectors = [
                '.mini-errorIcon:not([style*="display: none"])',
                '.validation-error',
                '.error-message',
                '.mini-tooltip-error',
                '[class*="error"]:not([style*="display: none"])'
            ];
            
            for (var i = 0; i < errorSelectors.length; i++) {
                var elements = document.querySelectorAll(errorSelectors[i]);
                for (var j = 0; j < elements.length; j++) {
                    var elem = elements[j];
                    if (elem.offsetWidth > 0 && elem.offsetHeight > 0) {
                        var text = (elem.textContent || elem.innerText || '').trim();
                        if (text && text !== '') {
                            errorInfo.hasErrors = true;
                            errorInfo.errorMessages.push(text);
                        }
                    }
                }
            }
            
            return errorInfo;
            """

            error_result = self.driver.execute_script(error_check_script)

            if error_result.get('hasErrors'):
                error_messages = error_result.get('errorMessages', [])
                logger.error(f'保存时出现错误: {error_messages}')

                # 如果有错误弹窗，点击确定按钮关闭
                if error_result.get('hasErrorPopup'):
                    popup_info = error_result.get('popupInfo', {})
                    logger.info(f'检测到错误弹窗: {popup_info.get("message")}')

                    if popup_info.get('hasOkButton'):
                        try:
                            if popup_info.get('okButtonId'):
                                ok_button = self.driver.find_element(
                                    By.ID, popup_info['okButtonId']
                                )
                            else:
                                ok_button = self.driver.find_element(
                                    By.CSS_SELECTOR,
                                    '.mini-messagebox .mini-messagebox-button, .mini-messagebox input[type="button"], .mini-messagebox button',
                                )
                            ok_button.click()
                            logger.info('已点击错误弹窗的确定按钮')
                            sleep(0.5)
                        except Exception as e:
                            logger.warning(f'点击错误弹窗确定按钮失败: {e}')

                return False

            # 检查按钮是否恢复到非加载状态
            final_button_check = self.driver.execute_script(loading_check_script)
            if not final_button_check.get('hasLoading'):
                logger.info('保存操作完成，按钮已恢复正常状态')

            logger.info('[成功] 合同保存成功')
            return True

        except Exception as e:
            logger.error(f'保存合同时发生异常: {e}')
            return False

    def submit_contract_safely(self) -> bool:
        """
        安全地提报合同，包含错误检查
        :return: 是否提报成功
        """
        try:
            # 首先检查提报按钮是否存在和可用
            check_button_script = """
            var submitButton = document.getElementById('btn_4b5ebb68-e00f-405c-a8b9-3225ddcfb68d');
            if (submitButton) {
                return {
                    exists: true,
                    visible: submitButton.offsetWidth > 0 && submitButton.offsetHeight > 0,
                    enabled: !submitButton.disabled && !submitButton.classList.contains('disabled'),
                    text: submitButton.textContent || submitButton.innerText || ''
                };
            }
            return {exists: false};
            """

            button_info = self.driver.execute_script(check_button_script)

            if not button_info.get('exists'):
                logger.error('提报按钮不存在')
                return False

            if not button_info.get('visible'):
                logger.error('提报按钮不可见')
                return False

            if not button_info.get('enabled'):
                logger.error('提报按钮不可点击')
                return False

            logger.info(f'提报按钮状态正常: {button_info.get("text", "").strip()}')

            # 点击提报按钮
            submit_button = self.wait.until(
                EC.element_to_be_clickable(
                    (
                        By.ID,
                        'btn_4b5ebb68-e00f-405c-a8b9-3225ddcfb68d',
                    )
                )
            )
            submit_button.click()
            logger.info('已点击提报按钮')

            # 等待提报操作开始
            sleep(0.5)

            # 检查是否有加载指示器出现（表明正在提报）
            loading_check_script = """
            var button = document.getElementById('btn_4b5ebb68-e00f-405c-a8b9-3225ddcfb68d');
            if (button) {
                var loadingIcon = button.querySelector('.mini-button-loading-icon');
                var isLoading = loadingIcon && loadingIcon.style.display !== 'none';
                return {hasLoading: isLoading};
            }
            return {hasLoading: false};
            """

            loading_info = self.driver.execute_script(loading_check_script)
            if loading_info.get('hasLoading'):
                logger.info('检测到加载状态，等待提报完成...')
                sleep(1)

            # 等待足够的时间确保提报操作完成
            sleep(1.5)

            # 检查是否有成功提报的确认弹窗
            success_popup_script = """
            var popup = document.querySelector('.mini-messagebox');
            if (popup && popup.offsetWidth > 0 && popup.offsetHeight > 0) {
                var message = popup.querySelector('.mini-messagebox-msg');
                var okButton = popup.querySelector('.mini-messagebox-button.ok');
                
                if (message) {
                    var messageText = message.textContent || message.innerText;
                    if (messageText.includes('提报成功') || messageText.includes('提交成功') || 
                        messageText.includes('成功') || messageText.includes('完成')) {
                        return {
                            isSuccessPopup: true,
                            message: messageText,
                            hasOkButton: okButton !== null,
                            okButtonId: okButton ? okButton.id : null
                        };
                    }
                }
            }
            return {isSuccessPopup: false};
            """

            success_popup_info = self.driver.execute_script(success_popup_script)

            if success_popup_info.get('isSuccessPopup'):
                logger.info(f'检测到提报成功弹窗: {success_popup_info.get("message")}')

                if success_popup_info.get('hasOkButton'):
                    ok_button_id = success_popup_info.get('okButtonId')
                    try:
                        ok_button = self.wait.until(
                            EC.element_to_be_clickable((By.ID, ok_button_id))
                        )
                        ok_button.click()
                        logger.info(f'已点击提报成功弹窗确定按钮 (ID: {ok_button_id})')

                        # 等待弹窗消失
                        sleep(0.5)

                    except Exception as e:
                        logger.error(f'点击提报成功弹窗确定按钮失败: {e}')
                        return False

            # 检查是否有错误信息出现
            error_check_script = """
            var errorInfo = {
                hasErrors: false,
                errorMessages: [],
                hasErrorPopup: false,
                popupInfo: null
            };
            
            // 检查是否有错误弹窗
            var messagebox = document.querySelector('.mini-messagebox');
            if (messagebox && messagebox.offsetWidth > 0 && messagebox.offsetHeight > 0) {
                var messageDiv = messagebox.querySelector('.mini-messagebox-msg');
                if (messageDiv) {
                    var messageText = (messageDiv.textContent || messageDiv.innerText || '').trim();
                    // 判断是否是错误信息（包含错误、失败等关键词，且不包含成功）
                    if (messageText && !messageText.includes('成功') && 
                        (messageText.includes('错误') || messageText.includes('失败') || 
                         messageText.includes('不能') || messageText.includes('无法') ||
                         messageText.includes('请') || messageText.includes('必须'))) {
                        
                        errorInfo.hasErrorPopup = true;
                        errorInfo.hasErrors = true;
                        errorInfo.errorMessages.push(messageText);
                        
                        // 查找确定按钮
                        var okButton = messagebox.querySelector('.mini-messagebox-button');
                        if (!okButton) {
                            okButton = messagebox.querySelector('input[type="button"]');
                        }
                        if (!okButton) {
                            okButton = messagebox.querySelector('button');
                        }
                        
                        errorInfo.popupInfo = {
                            message: messageText,
                            hasOkButton: okButton !== null,
                            okButtonText: okButton ? (okButton.value || okButton.textContent || okButton.innerText) : null,
                            okButtonId: okButton ? okButton.id : null
                        };
                    }
                }
            }
            
            // 检查其他错误元素
            var errorSelectors = [
                '.mini-errorIcon:not([style*="display: none"])',
                '.validation-error',
                '.error-message',
                '.mini-tooltip-error',
                '[class*="error"]:not([style*="display: none"])'
            ];
            
            for (var i = 0; i < errorSelectors.length; i++) {
                var elements = document.querySelectorAll(errorSelectors[i]);
                for (var j = 0; j < elements.length; j++) {
                    var elem = elements[j];
                    if (elem.offsetWidth > 0 && elem.offsetHeight > 0) {
                        var text = (elem.textContent || elem.innerText || '').trim();
                        if (text && text !== '') {
                            errorInfo.hasErrors = true;
                            errorInfo.errorMessages.push(text);
                        }
                    }
                }
            }
            
            return errorInfo;
            """

            error_result = self.driver.execute_script(error_check_script)

            if error_result.get('hasErrors'):
                error_messages = error_result.get('errorMessages', [])
                logger.error(f'提报时出现错误: {error_messages}')

                # 如果有错误弹窗，点击确定按钮关闭
                if error_result.get('hasErrorPopup'):
                    popup_info = error_result.get('popupInfo', {})
                    logger.info(f'检测到错误弹窗: {popup_info.get("message")}')

                    if popup_info.get('hasOkButton'):
                        try:
                            if popup_info.get('okButtonId'):
                                ok_button = self.driver.find_element(
                                    By.ID, popup_info['okButtonId']
                                )
                            else:
                                ok_button = self.driver.find_element(
                                    By.CSS_SELECTOR,
                                    '.mini-messagebox .mini-messagebox-button, .mini-messagebox input[type="button"], .mini-messagebox button',
                                )
                            ok_button.click()
                            logger.info('已点击错误弹窗的确定按钮')
                            sleep(0.5)
                        except Exception as e:
                            logger.warning(f'点击错误弹窗确定按钮失败: {e}')

                return False

            # 检查按钮是否恢复到非加载状态
            final_button_check = self.driver.execute_script(loading_check_script)
            if not final_button_check.get('hasLoading'):
                logger.info('提报操作完成，按钮已恢复正常状态')

            logger.info('[成功] 合同提报成功')
            return True

        except Exception as e:
            logger.error(f'提报合同时发生异常: {e}')
            return False

    def handle_submit_popup(self) -> bool:
        """
        暴力关闭创建合同页面，回到登录后的主页面
        :return: 是否成功处理
        """
        try:
            logger.info('[提交] 开始暴力关闭创建合同页面')

            # 等待提交完成
            sleep(2)

            # 采用暴力关闭策略
            page_closed = self._force_close_and_return_to_main()

            if page_closed:
                logger.info('[成功] 已暴力关闭页面并回到登录后主页面')
                return True
            else:
                logger.warning('[警告] 暴力关闭可能未完全成功，但继续执行')
                return True  # 即使部分失败也继续，避免程序中断

        except Exception as e:
            logger.error(f'暴力关闭页面失败: {e}')
            # 即使失败也返回True，避免程序中断
            return True

    def _force_close_and_return_to_main(self) -> bool:
        """
        暴力关闭当前页面并回到登录后的主页面
        :return: 是否成功关闭并返回
        """
        try:
            logger.info('[暴力关闭] 开始暴力关闭当前页面，回到登录后主页面')

            # 第一步：关闭所有新窗口，只保留主窗口
            all_handles = self.driver.window_handles
            main_handle = all_handles[0]  # 主窗口句柄（登录后的页面）

            logger.info(f'当前窗口总数: {len(all_handles)}')
            logger.info(f'主窗口句柄: {main_handle}')

            # 关闭除主窗口外的所有窗口
            if len(all_handles) > 1:
                for handle in all_handles[1:]:
                    try:
                        logger.info(f'正在关闭窗口: {handle}')
                        self.driver.switch_to.window(handle)
                        self.driver.close()
                        logger.info(f'成功关闭窗口: {handle}')
                    except Exception as e:
                        logger.warning(f'关闭窗口 {handle} 时出错: {e}')
                        continue

            # 第二步：切换回主窗口
            try:
                self.driver.switch_to.window(main_handle)
                logger.info('已切换回主窗口')
                sleep(1)
            except Exception as e:
                logger.error(f'切换回主窗口失败: {e}')
                return False

            # 第三步：检查是否回到登录后的主页面
            is_main_page = self._check_main_page_elements()

            if is_main_page:
                logger.info('[成功] 已回到登录后的主页面')
                return True
            else:
                # 第四步：如果不在主页面，尝试重新导航到电子采购
                logger.info('[尝试恢复] 不在主页面，尝试重新导航到电子采购')
                recovery_success = self._recover_to_procurement_page()

                if recovery_success:
                    logger.info('[恢复成功] 已重新导航到电子采购页面')
                    return True
                else:
                    logger.warning('[恢复失败] 无法重新导航到电子采购页面')
                    return False

        except Exception as e:
            logger.error(f'暴力关闭页面时发生异常: {e}')
            return False

    def _check_main_page_elements(self) -> bool:
        """
        检查是否在登录后的主页面
        :return: 是否在主页面
        """
        try:
            logger.info('[检查] 检查是否在登录后的主页面')

            # 检查是否能找到电子采购按钮
            check_script = """
            // 检查是否有电子采购按钮
            var eProcurementButton = document.querySelector('a[class*="kd-cq-btn"] span[class*="_1RGaSniK"]');
            if (eProcurementButton) {
                var buttonText = (eProcurementButton.textContent || eProcurementButton.innerText || '').trim();
                if (buttonText === '电子采购') {
                    return {
                        isMainPage: true,
                        hasEProcurementButton: true,
                        buttonText: buttonText
                    };
                }
            }
            
            // 检查其他可能的主页面特征
            var hasPortalElements = document.querySelector('[class*="portal"]') !== null;
            var hasKdElements = document.querySelector('[class*="kd-"]') !== null;
            
            return {
                isMainPage: false,
                hasEProcurementButton: false,
                hasPortalElements: hasPortalElements,
                hasKdElements: hasKdElements
            };
            """

            result = self.driver.execute_script(check_script)
            logger.info(f'主页面检查结果: {result}')

            return result.get('isMainPage', False)

        except Exception as e:
            logger.warning(f'检查主页面元素时出错: {e}')
            return False

    def _recover_to_procurement_page(self) -> bool:
        """
        尝试从当前位置恢复到电子采购页面
        :return: 是否成功恢复
        """
        try:
            logger.info('[恢复] 开始尝试恢复到电子采购页面')

            # 方法1：检查是否能直接点击电子采购按钮
            try:
                # 创建短超时的WebDriverWait
                short_wait = WebDriverWait(self.driver, 5)
                e_procurement_button = short_wait.until(
                    EC.element_to_be_clickable(
                        (
                            By.XPATH,
                            "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]",
                        )
                    )
                )
                logger.info('找到电子采购按钮，准备点击')
                e_procurement_button.click()

                # 等待新窗口打开
                try:
                    # 创建中等超时的WebDriverWait
                    medium_wait = WebDriverWait(self.driver, 10)
                    medium_wait.until(EC.number_of_windows_to_be(2))
                    self.driver.switch_to.window(self.driver.window_handles[-1])
                    medium_wait.until(
                        EC.presence_of_element_located((By.TAG_NAME, 'body'))
                    )
                    logger.info('电子采购页面已加载')
                    sleep(3)

                    # 导航到合同备案页面
                    self.navigate_to_contracts()
                    logger.info('[恢复成功] 已重新导航到合同备案页面')
                    return True

                except TimeoutException:
                    logger.warning('等待电子采购页面加载超时')
                    return False

            except TimeoutException:
                logger.warning('未找到电子采购按钮')

            # 方法2：尝试刷新页面后重试
            try:
                logger.info('[恢复] 尝试刷新页面后重试')
                self.driver.refresh()
                sleep(3)

                # 创建中等超时的WebDriverWait
                medium_wait = WebDriverWait(self.driver, 10)
                e_procurement_button = medium_wait.until(
                    EC.element_to_be_clickable(
                        (
                            By.XPATH,
                            "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]",
                        )
                    )
                )
                logger.info('刷新后找到电子采购按钮')
                e_procurement_button.click()

                # 等待并切换到新窗口
                medium_wait.until(EC.number_of_windows_to_be(2))
                self.driver.switch_to.window(self.driver.window_handles[-1])
                medium_wait.until(EC.presence_of_element_located((By.TAG_NAME, 'body')))
                sleep(3)

                # 导航到合同备案页面
                self.navigate_to_contracts()
                logger.info('[恢复成功] 刷新后重新导航到合同备案页面成功')
                return True

            except Exception as refresh_e:
                logger.error(f'刷新页面后重试失败: {refresh_e}')

            return False

        except Exception as e:
            logger.error(f'恢复到电子采购页面时发生异常: {e}')
            return False

    def _ensure_in_contract_page(self) -> bool:
        """
        确保当前在正确的合同备案页面iframe中
        :return: 是否成功确保在合同备案页面
        """
        try:
            logger.info('[检查] 确保在正确的合同备案页面')

            # 第一步：检查当前窗口状态
            current_handles = self.driver.window_handles
            logger.info(f'当前窗口数量: {len(current_handles)}')

            # 第二步：检查是否在合同备案页面
            is_in_contract_page = self._check_in_contract_page()

            if is_in_contract_page:
                logger.info('[成功] 已在正确的合同备案页面')
                return True

            # 第三步：如果不在合同备案页面，尝试导航
            logger.info('[导航] 当前不在合同备案页面，开始导航')

            # 检查窗口数量，决定导航策略
            if len(current_handles) < 2:
                # 只有一个窗口，说明在登录后主页面，需要重新打开电子采购
                logger.info('[导航] 只有一个窗口，从主页面重新导航到电子采购')
                if self._recover_to_procurement_page():
                    return self._check_in_contract_page()
                else:
                    logger.error('[失败] 无法从主页面导航到电子采购')
                    return False

            else:
                # 有多个窗口，切换到电子采购窗口并导航到合同备案
                logger.info('[导航] 多个窗口，切换到电子采购窗口')
                try:
                    # 切换到最后一个窗口（应该是电子采购窗口）
                    self.driver.switch_to.window(current_handles[-1])
                    sleep(1)

                    # 导航到合同备案页面
                    self.navigate_to_contracts()

                    # 验证是否成功
                    return self._check_in_contract_page()

                except Exception as e:
                    logger.warning(f'切换窗口导航失败: {e}')
                    # 如果失败，尝试重新导航
                    return (
                        self._recover_to_procurement_page()
                        and self._check_in_contract_page()
                    )

        except Exception as e:
            logger.error(f'确保在合同备案页面时发生异常: {e}')
            return False

    def _check_in_contract_page(self) -> bool:
        """
        检查是否在合同备案页面的iframe中
        :return: 是否在合同备案页面
        """
        try:
            # 首先检查当前页面是否有电子采购页面的特征
            current_url = self.driver.current_url
            logger.info(f'[验证] 当前页面URL: {current_url}')

            # 如果在登录后主页面，肯定不在合同备案页面
            if 'portal.crrcgo.cc' in current_url and 'epp.crrcgo.cc' not in current_url:
                logger.info('[验证] 当前在登录后主页面，不在合同备案页面')
                return False

            # 尝试切换到合同备案页面的iframe
            try:
                self.driver.switch_to.default_content()

                # 使用更短的超时时间检查iframe是否存在
                short_wait = WebDriverWait(self.driver, 2)
                iframe = short_wait.until(
                    EC.presence_of_element_located((By.ID, 'tab-content-95130002'))
                )
                self.driver.switch_to.frame(iframe)

                # 检查是否能找到新增按钮
                btnAdd = short_wait.until(
                    EC.presence_of_element_located((By.ID, 'btnAdd'))
                )
                if btnAdd and btnAdd.is_displayed():
                    logger.info('[验证] 在正确的合同备案页面，找到新增按钮')
                    return True
                else:
                    logger.warning('[验证] 找到iframe但新增按钮不可见')
                    return False

            except TimeoutException:
                logger.info('[验证] 未找到合同备案iframe，可能不在电子采购页面')
                return False
            except Exception as e:
                logger.warning(f'[验证] 无法切换到合同备案iframe或找到新增按钮: {e}')
                return False

        except Exception as e:
            logger.warning(f'检查合同备案页面时出错: {e}')
            return False

    def _click_popup_button(self, popup_info: dict) -> bool:
        """
        点击弹窗按钮的辅助方法 - 已废弃，使用暴力关闭策略
        """
        logger.info('此方法已废弃，使用暴力关闭策略')
        return True

    def _click_mini_messagebox_button(self) -> bool:
        """
        专门处理mini-messagebox弹窗的确定按钮点击 - 已废弃，使用暴力关闭策略
        """
        logger.info('此方法已废弃，使用暴力关闭策略')
        return True

    def _update_contract_status(self, cursor, contract_no: str, status: str) -> None:
        """
        更新已备案合同表的状态
        :param contract_no: 合同编号
        :param status: 合同状态
        """
        try:
            robust_execute(
                cursor,
                """
                UPDATE 已备案合同明细 SET 合同状态 = ? WHERE 合同编号 = ?
            """,
                (status, contract_no),
            )
            logger.info(f'[成功] 合同状态更新成功: {contract_no} ({status})')
        except sqlite3.Error as db_error:
            logger.error(f'数据库操作失败: {db_error}')
            raise
        except Exception as e:
            logger.error(f'更新合同状态时发生错误: {e}')
            raise

    def get_current_iframe_name(self) -> str:
        """
        获取当前iframe的name属性
        :return: iframe的name属性值
        """
        try:
            # 使用JavaScript获取当前iframe的name属性
            iframe_name = self.driver.execute_script(
                "return window.frameElement ? window.frameElement.getAttribute('name') : null;"
            )
            return iframe_name if iframe_name else '未知'
        except Exception as e:
            logger.warning(f'获取iframe name失败: {e}')
            return '获取失败'

    def select_date_range(self, start_date: str, end_date: str) -> bool:
        """
        选择日期范围 - 增强版本，包含面板导航
        :param start_date: 开始日期，格式：YYYY-MM-DD
        :param end_date: 结束日期，格式：YYYY-MM-DD
        :return: 是否成功选择日期范围
        """
        self.setup_driver()
        if not self.driver or not self.wait:
            logger.error('Driver或Wait未正确初始化')
            return False

        try:
            date_range_str = f'{start_date} ~ {end_date}'
            logger.info(f'尝试设置日期范围: {date_range_str}')

            # 解析日期
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError as e:
                logger.error(f'日期格式错误: {e}')
                return False

            # 点击触发按钮激活面板
            trigger_button = self.wait.until(
                EC.element_to_be_clickable(
                    (
                        By.CSS_SELECTOR,
                        'span.mini-buttonedit-icon.mini-icon.mini-iconfont',
                    )
                )
            )
            trigger_button.click()
            sleep(1)

            # 等待面板出现
            laydate_panel = self.wait.until(
                EC.presence_of_element_located((By.ID, 'layui-laydate1'))
            )
            logger.info('日期选择面板已激活')

            def safe_click(driver, element):
                try:
                    driver.execute_script(
                        "arguments[0].scrollIntoView({block: 'center'});", element
                    )
                    if element.is_displayed() and element.is_enabled():
                        try:
                            element.click()
                        except Exception:
                            driver.execute_script('arguments[0].click();', element)
                        return True
                    else:
                        driver.execute_script('arguments[0].click();', element)
                        return True
                except Exception as e:
                    logger.error(f'按钮点击失败: {e}')
                    return False

            def navigate_to_month(
                panel_index: int,
                target_year: int,
                target_month: int,
                max_attempts: int = 24,
            ):
                current_attempts = 0
                while current_attempts < max_attempts:
                    try:
                        # 获取当前面板显示的年月
                        year_span = laydate_panel.find_element(
                            By.CSS_SELECTOR,
                            f".laydate-main-list-{panel_index} .laydate-set-ym span[lay-type='year']",
                        )
                        month_span = laydate_panel.find_element(
                            By.CSS_SELECTOR,
                            f".laydate-main-list-{panel_index} .laydate-set-ym span[lay-type='month']",
                        )
                        current_year_text = year_span.text.replace('年', '')
                        current_month_text = month_span.text.replace('月', '')
                        current_year = int(current_year_text)
                        current_month = int(current_month_text)
                        logger.info(
                            f'面板{panel_index}当前显示: {current_year}年{current_month}月, 目标: {target_year}年{target_month}月'
                        )
                        # 1. 先调整年份
                        if current_year != target_year:
                            if current_year < target_year:
                                next_year_btn = laydate_panel.find_element(
                                    By.CSS_SELECTOR,
                                    f'.laydate-main-list-{panel_index} .laydate-next-y',
                                )
                                safe_click(self.driver, next_year_btn)
                                logger.info(f'面板{panel_index}点击向后一年')
                            else:
                                prev_year_btn = laydate_panel.find_element(
                                    By.CSS_SELECTOR,
                                    f'.laydate-main-list-{panel_index} .laydate-prev-y',
                                )
                                safe_click(self.driver, prev_year_btn)
                                logger.info(f'面板{panel_index}点击向前一年')
                            sleep(0.5)
                            current_attempts += 1
                            continue  # 年份没对齐，继续循环
                        # 2. 年份对齐后，调整月份
                        if current_month != target_month:
                            if current_month < target_month:
                                next_month_btn = laydate_panel.find_element(
                                    By.CSS_SELECTOR,
                                    f'.laydate-main-list-{panel_index} .laydate-next-m',
                                )
                                safe_click(self.driver, next_month_btn)
                                logger.info(f'面板{panel_index}点击向后一个月')
                            else:
                                prev_month_btn = laydate_panel.find_element(
                                    By.CSS_SELECTOR,
                                    f'.laydate-main-list-{panel_index} .laydate-prev-m',
                                )
                                safe_click(self.driver, prev_month_btn)
                                logger.info(f'面板{panel_index}点击向前一个月')
                            sleep(0.5)
                            current_attempts += 1
                            continue
                        # 年月都对齐，退出
                        logger.info(
                            f'面板{panel_index}已导航到目标年月: {target_year}年{target_month}月'
                        )
                        return True
                    except Exception as e:
                        logger.error(f'导航面板{panel_index}时出错: {e}')
                        current_attempts += 1
                        sleep(0.5)
                logger.error(
                    f'无法导航面板{panel_index}到目标年月，已尝试{max_attempts}次'
                )
                return False

            # 导航第一个面板到开始日期的年月
            start_success = navigate_to_month(0, start_dt.year, start_dt.month)
            if not start_success:
                logger.error('无法导航到开始日期的年月')
                return False

            # 导航第二个面板到结束日期的年月
            end_success = navigate_to_month(1, end_dt.year, end_dt.month)
            if not end_success:
                logger.error('无法导航到结束日期的年月')
                return False

            # 转换日期格式，去掉前导零以匹配lay-ymd格式
            def format_date_for_layui(date_str):
                year, month, day = date_str.split('-')
                # lay-ymd格式是去掉前导零的，例如: 2024-1-9
                return f'{year}-{int(month)}-{int(day)}'

            start_ymd = format_date_for_layui(start_date)
            end_ymd = format_date_for_layui(end_date)

            logger.info(f'查找并点击日期: 开始={start_ymd}, 结束={end_ymd}')

            # 等待导航完成后的渲染
            sleep(1)

            # 点击开始日期（在第一个日历中查找）
            def click_date_with_retry(
                panel_index, target_ymd, date_name, max_retries=3
            ):
                """
                重试点击日期，包含多种格式和查找方式
                """
                for attempt in range(max_retries):
                    try:
                        logger.info(
                            f'第{attempt + 1}次尝试点击{date_name}: {target_ymd}'
                        )

                        # 等待面板渲染完成
                        sleep(1.5)

                        # 尝试多种日期格式和查找方式
                        date_selectors = [
                            f"//div[contains(@class,'laydate-main-list-{panel_index}')]//td[@lay-ymd='{target_ymd}']",
                            f"//div[contains(@class,'laydate-main-list-{panel_index}')]//td[@lay-ymd='{target_ymd}' and not(contains(@class,'laydate-day-prev')) and not(contains(@class,'laydate-day-next'))]",
                            f".laydate-main-list-{panel_index} td[lay-ymd='{target_ymd}']:not(.laydate-day-prev):not(.laydate-day-next)",
                        ]

                        date_element = None
                        for selector in date_selectors:
                            try:
                                if selector.startswith('//'):
                                    # XPath selector
                                    date_element = self.driver.find_element(
                                        By.XPATH, selector
                                    )
                                else:
                                    # CSS selector
                                    date_element = self.driver.find_element(
                                        By.CSS_SELECTOR, selector
                                    )

                                if date_element and date_element.is_displayed():
                                    logger.info(
                                        f'找到{date_name}元素，选择器: {selector}'
                                    )
                                    break
                            except:
                                continue

                        if not date_element:
                            # 尝试查找所有可用日期，然后匹配
                            try:
                                all_dates = self.driver.find_elements(
                                    By.CSS_SELECTOR,
                                    f'.laydate-main-list-{panel_index} td[lay-ymd]:not(.laydate-day-prev):not(.laydate-day-next)',
                                )
                                logger.info(
                                    f'面板{panel_index}中找到{len(all_dates)}个可用日期'
                                )

                                for date_elem in all_dates:
                                    lay_ymd = date_elem.get_attribute('lay-ymd')
                                    logger.info(f'检查日期: {lay_ymd}')
                                    if lay_ymd == target_ymd:
                                        date_element = date_elem
                                        logger.info(
                                            f'通过遍历找到{date_name}: {lay_ymd}'
                                        )
                                        break
                            except Exception as find_e:
                                logger.warning(f'遍历查找日期失败: {find_e}')

                        if date_element:
                            try:
                                # 滚动到元素可见
                                self.driver.execute_script(
                                    "arguments[0].scrollIntoView({block: 'center'});",
                                    date_element,
                                )
                                sleep(0.5)

                                # 尝试直接点击
                                date_element.click()
                                logger.info(f'成功点击{date_name}: {target_ymd}')
                                return True

                            except Exception as click_e:
                                logger.warning(
                                    f'直接点击失败，尝试JavaScript点击: {click_e}'
                                )
                                try:
                                    # 使用JavaScript强制点击
                                    self.driver.execute_script(
                                        'arguments[0].click();', date_element
                                    )
                                    logger.info(
                                        f'JavaScript点击{date_name}成功: {target_ymd}'
                                    )
                                    return True
                                except Exception as js_e:
                                    logger.warning(f'JavaScript点击也失败: {js_e}')

                        if attempt < max_retries - 1:
                            logger.warning(
                                f'第{attempt + 1}次点击{date_name}失败，等待后重试...'
                            )
                            sleep(2)
                        else:
                            logger.error(
                                f'所有尝试都失败了，无法点击{date_name}: {target_ymd}'
                            )

                    except Exception as e:
                        logger.error(f'第{attempt + 1}次点击{date_name}时出现异常: {e}')
                        if attempt < max_retries - 1:
                            sleep(2)

                return False

            # 点击开始日期
            start_success = click_date_with_retry(0, start_ymd, '开始日期')
            if not start_success:
                logger.warning('开始日期点击失败，尝试使用第一个可用日期作为fallback')
                try:
                    first_available = laydate_panel.find_element(
                        By.CSS_SELECTOR,
                        '.laydate-main-list-0 td[lay-ymd]:not(.laydate-day-prev):not(.laydate-day-next)',
                    )
                    first_available.click()
                    actual_start_ymd = first_available.get_attribute('lay-ymd')
                    logger.info(f'使用fallback点击了开始日期: {actual_start_ymd}')
                except:
                    logger.error('无法找到任何可点击的开始日期')

            # 点击结束日期
            end_success = click_date_with_retry(1, end_ymd, '结束日期')
            if not end_success:
                logger.warning('结束日期点击失败，尝试使用最后一个可用日期作为fallback')
                try:
                    available_dates = laydate_panel.find_elements(
                        By.CSS_SELECTOR,
                        '.laydate-main-list-1 td[lay-ymd]:not(.laydate-day-prev):not(.laydate-day-next)',
                    )
                    if available_dates:
                        last_available = available_dates[
                            -1
                        ]  # 使用最后一个日期作为结束日期
                        last_available.click()
                        actual_end_ymd = last_available.get_attribute('lay-ymd')
                        logger.info(f'使用fallback点击了结束日期: {actual_end_ymd}')
                except:
                    logger.error('无法找到任何可点击的结束日期')

            sleep(0.5)

            # 检查确定按钮状态并点击
            try:
                confirm_btn = self.wait.until(
                    EC.presence_of_element_located(
                        (By.CSS_SELECTOR, "span[lay-type='confirm']")
                    )
                )

                # 检查按钮是否被禁用
                btn_classes = confirm_btn.get_attribute('class')
                if 'laydate-disabled' in btn_classes:
                    # 移除disabled类
                    self.driver.execute_script(
                        "arguments[0].className = arguments[0].className.replace('laydate-disabled', '').trim();",
                        confirm_btn,
                    )
                    logger.info('已移除确定按钮的disabled状态')

                # 点击确定按钮
                self.driver.execute_script('arguments[0].click();', confirm_btn)
                logger.info('已点击确定按钮')
                sleep(0.5)

                # 验证结果
                datepicker_input = self.driver.find_element(
                    By.ID, 'daterangepicker$text'
                )
                current_value = datepicker_input.get_attribute('value')
                logger.info(f'设置完成，当前值: {current_value}')

                # 检查是否包含我们设置的日期
                if current_value and (
                    start_date in current_value or end_date in current_value
                ):
                    logger.info('日期范围设置成功')
                    return True
                else:
                    logger.warning('日期范围设置未能正确反映到输入框')
                    return True  # 仍然返回True，因为可能是显示格式问题

            except Exception as e:
                logger.error(f'点击确定按钮失败: {e}')

        except Exception as e:
            logger.error(f'设置日期范围时发生错误: {str(e)}')
            return False

        return False

    def write_to_tables(self) -> None:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        try:
            with sqlite3.connect(
                r'd:\user\PythonProject\ZConline\app.db', timeout=60
            ) as conn:
                conn.execute('PRAGMA journal_mode=WAL;')
                cursor = conn.cursor()

                # 查询已备案合同
                robust_execute(cursor, 'SELECT 合同编号 FROM 已备案合同明细')
                backended_contract_IDs = cursor.fetchall()
                backended_contract_IDs = [a[0] for a in backended_contract_IDs]

                # 查询2024年合同
                robust_execute(
                    cursor,
                    "SELECT * FROM contract WHERE strftime('%Y', registrationtime) = '2024'",
                )
                contract_rows = cursor.fetchall()
                contract_columns = [desc[0] for desc in cursor.description]

                # 注意：不在这里直接切换到合同备案页面，而是在每个合同循环中动态检查和导航
                logger.info(
                    '开始处理合同列表，每个合同都会自动确保在正确的合同备案页面'
                )

                for contract_row in contract_rows:
                    contract = dict(zip(contract_columns, contract_row))
                    contract_no = contract.get('conid')
                    contract_name = contract.get('conname')
                    registrationtime = contract.get('registrationtime')
                    provider_name = contract.get('provider')
                    conmoney = contract.get('conmoney')
                    taxrate = contract.get('taxrate')

                    # 检查是否已经备案
                    if contract_no in backended_contract_IDs:
                        logger.info(f'合同 {contract_no} 已备案，跳过')
                        continue

                    logger.info(f'开始处理合同: {contract_name} ({contract_no})')

                    try:
                        # 查询供应商信息
                        robust_execute(
                            cursor,
                            'SELECT * FROM provider WHERE providername=?',
                            (provider_name,),
                        )
                        provider_row = cursor.fetchone()

                        if not provider_row:
                            logger.warning(
                                f'未找到供应商信息: {provider_name}，跳过合同 {contract_no}'
                            )
                            self._record_failed_contract(
                                cursor, contract_no, '未找到供应商信息'
                            )
                            continue

                        provider_columns = [desc[0] for desc in cursor.description]
                        provider = dict(zip(provider_columns, provider_row))
                        organization_code = provider.get('Organization_Code')
                        tax_identification_number = provider.get(
                            'Tax_Identification_Number'
                        )

                        # 确保在正确的合同备案页面
                        if not self._ensure_in_contract_page():
                            logger.error(
                                f'无法确保在合同备案页面，跳过合同: {contract_no}'
                            )
                            self._record_failed_contract(
                                cursor, contract_no, '无法导航到合同备案页面'
                            )
                            continue

                        # 点击新增按钮
                        btnAdd = self.wait.until(
                            EC.element_to_be_clickable((By.ID, 'btnAdd'))
                        )
                        btnAdd.click()
                        sleep(2)

                        # 切换到新窗口
                        all_handles = self.driver.window_handles
                        new_window_handle = all_handles[-1]
                        self.driver.switch_to.window(new_window_handle)
                        sleep(3)

                        # 进入表单iframe
                        iframe = self.wait.until(
                            EC.presence_of_element_located(
                                (
                                    By.CSS_SELECTOR,
                                    "iframe[name^='mini-iframe-']",
                                )
                            )
                        )
                        # 获取iframe的name属性
                        iframe_name = iframe.get_attribute('name')
                        iframe_id = int(iframe_name.split('-')[-1])
                        logger.info(f'找到iframe，name属性为: {iframe_name}')

                        self.driver.switch_to.frame(iframe)

                        # 验证切换后也可以获取iframe name
                        current_iframe_name = self.get_current_iframe_name()
                        logger.info(f'切换后确认iframe name: {current_iframe_name}')

                        # 买方公司选择逻辑
                        e = self.wait.until(
                            EC.presence_of_element_located(
                                (
                                    By.XPATH,
                                    '//*[@id="mini-5$body$1"]/div/div[1]/div[2]/div/div/div[1]/a[1]/span',
                                )
                            )
                        )
                        e.click()
                        sleep(2)

                        self.driver.switch_to.default_content()

                        iframe = self.wait.until(
                            EC.presence_of_element_located(
                                (
                                    By.CSS_SELECTOR,
                                    f"iframe[name^='mini-iframe-{iframe_id + 2}']",
                                )
                            )
                        )
                        self.driver.switch_to.frame(iframe)
                        input_element = self.wait.until(
                            EC.presence_of_element_located(
                                (
                                    By.CSS_SELECTOR,
                                    '#tree > div > div.mini-panel-viewport.mini-grid-viewport > div.mini-panel-toolbar > span > span > input',
                                )
                            )
                        )
                        input_element.clear()
                        input_element.send_keys('中车眉山车辆有限公司')
                        input_element.send_keys(Keys.RETURN)
                        sleep(2)
                        e = self.driver.find_element(
                            By.XPATH,
                            '//*[@id="3$cell$1"]/div/div/span[2]/span[2]/span',
                        )
                        e.click()
                        sleep(2)
                        e = self.driver.find_element(By.CSS_SELECTOR, '#addClose>span')
                        e.click()
                        sleep(2)

                        self.driver.switch_to.default_content()
                        iframe = self.wait.until(
                            EC.presence_of_element_located(
                                (
                                    By.CSS_SELECTOR,
                                    "iframe[name^='mini-iframe-']",
                                )
                            )
                        )
                        self.driver.switch_to.frame(iframe)
                        logger.info('成功切回主表单iframe')

                        # 填写表单字段
                        # 选择买方单位性质（境内企业）
                        if not self.set_radio_button_safely('mini-12', '境内企业'):
                            logger.warning('设置买方单位性质失败')

                        # 选择卖方单位性质
                        # if not self.set_radio_button_safely("mini-13", "民营企业"):
                        #     logger.warning("设置卖方单位性质失败")

                        # 填写卖方单位名称
                        if not self.set_text_input_safely(
                            'sellunitname$text', provider_name, '卖方单位名称'
                        ):
                            logger.warning('卖方单位名称填写失败，但继续执行')

                        # 填写卖方统一社会信用代码
                        if not self.set_text_input_safely(
                            'sellnsrsbh$text',
                            tax_identification_number,
                            '卖方统一社会信用代码',
                        ):
                            logger.warning('卖方统一社会信用代码填写失败，但继续执行')

                        # 填写合同编号
                        if not self.set_text_input_safely(
                            'htno$text', contract_no, '合同编号'
                        ):
                            logger.warning('合同编号填写失败，但继续执行')

                        # 填写合同名称
                        if not self.set_text_input_safely(
                            'hetongname$text', contract_name, '合同名称'
                        ):
                            logger.warning('合同名称填写失败，但继续执行')

                        # 框架合同选择（否）
                        if not self.set_radio_button_safely('mini-27', '否'):
                            logger.warning('设置框架合同选择失败')

                        # 是否需要审批后上传合同附件（否）
                        if not self.set_radio_button_safely('is_sp_upload_file', '否'):
                            logger.warning('设置审批后上传附件选择失败')

                        # 处理合同有效期
                        reg_str = ''
                        end_str = ''
                        if registrationtime:
                            try:
                                if isinstance(registrationtime, str):
                                    reg_date = datetime.fromisoformat(registrationtime)
                                else:
                                    reg_date = registrationtime
                                reg_str = reg_date.strftime('%Y-%m-%d')
                                end_date = reg_date + relativedelta(days=30)
                                end_str = end_date.strftime('%Y-%m-%d')
                            except Exception:
                                try:
                                    reg_str = registrationtime[:10]
                                    reg_date = datetime.strptime(reg_str, '%Y-%m-%d')
                                    end_date = reg_date + relativedelta(days=30)
                                    end_str = end_date.strftime('%Y-%m-%d')
                                except Exception:
                                    reg_str = '2025-01-01'
                                    end_str = '2025-03-01'
                        else:
                            reg_str = '2025-01-01'
                            end_str = '2025-03-01'

                        # 设置日期范围
                        if not self.select_date_range(reg_str, end_str):
                            logger.warning('设置合同有效期失败')

                        # 填写合同签订时间
                        if not self.set_text_input_safely(
                            'htqstime$text', reg_str, '合同签订时间'
                        ):
                            logger.warning('合同签订时间填写失败，但继续执行')

                        # 设置合同结算类型
                        if not self.set_dropdown_safely('billingtype', '付款'):
                            logger.warning('设置合同结算类型失败')

                        # 计算并填写不含税金额
                        notax_amount = 0
                        try:
                            if conmoney is not None and taxrate is not None:
                                notax_amount = round(
                                    float(conmoney) / (1 + float(taxrate) / 100), 6
                                )
                            else:
                                notax_amount = ''
                        except Exception:
                            notax_amount = ''

                        # 填写不含税金额
                        if not self.set_text_input_safely(
                            'notaxhtjine$text', str(notax_amount), '不含税金额'
                        ):
                            logger.warning('不含税金额填写失败，但继续执行')

                        # 填写含税金额
                        if not self.set_text_input_safely(
                            'htjine$text', str(conmoney), '含税金额'
                        ):
                            logger.warning('含税金额填写失败，但继续执行')

                        # 填写税率
                        if not self.set_text_input_safely(
                            'taxrate$text', str(taxrate), '税率'
                        ):
                            logger.warning('税率填写失败，但继续执行')

                        # 填写履约地点
                        if not self.set_text_input_safely(
                            'performanceaddress$text', '四川省眉山市', '履约地点'
                        ):
                            logger.warning('履约地点填写失败，但继续执行')

                        # 质保金选择（有）
                        if not self.set_radio_button_safely('mini-46', '有'):
                            logger.warning('设置质保金选择失败')

                        # 填写质保期（12个月）
                        if not self.set_text_input_safely(
                            'zhibaoqi$text', '12', '质保期'
                        ):
                            logger.warning('质保期填写失败，但继续执行')

                        # 填写质保条款
                        if not self.set_text_input_safely(
                            'zhibaotiaokuan$text', '按合同约定执行质保', '质保条款'
                        ):
                            logger.warning('质保条款填写失败，但继续执行')

                        # 合同变更选择（否）
                        if not self.set_radio_button_safely('mini-49', '否'):
                            logger.warning('设置合同变更选择失败')

                        # 补充协议选择（否）
                        if not self.set_radio_button_safely('mini-51', '否'):
                            logger.warning('设置补充协议选择失败')

                        # 填写结算条件
                        if not self.set_text_input_safely(
                            'jiesuanconditions$text', '按合同约定付款', '结算条件'
                        ):
                            logger.warning('结算条件填写失败，但继续执行')

                        # 设置采购相关字段
                        if not self.set_dropdown_safely('sourcingcgfs', '一般性谈判'):
                            logger.warning('设置采购方式失败')

                        if not self.set_radio_button_safely('mini-70', '邀请采购'):
                            logger.warning('设置采购方式类别失败')

                        if not self.set_dropdown_safely(
                            'dzcgplatform', '中车购"2.0"平台'
                        ):
                            logger.warning('设置采购平台名称失败')

                        if not self.set_dropdown_safely('caigoufenlei', '服务'):
                            logger.warning('设置采购对象分类失败')

                        if not self.set_dropdown_safely('dujiacaigou', '否'):
                            logger.warning('设置独家采购属性失败')

                        if not self.set_dropdown_safely('dailicaigou', '否'):
                            logger.warning('设置代理采购属性失败')

                        if not self.set_dropdown_safely('centralizecgtype', '企业自采'):
                            logger.warning('设置集采属性失败')

                        logger.info(f'表单填写完成: {contract_name}')

                        # 保存合同
                        save_result = self.save_contract_safely()

                        if save_result:
                            logger.info(f'合同保存成功: {contract_name}')

                            # 提报合同
                            submit_result = self.submit_contract_safely()
                            if submit_result:
                                logger.info(f'合同提报成功: {contract_name}')

                                # 记录成功的合同
                                self._record_successful_contract(
                                    cursor,
                                    contract,
                                    provider,
                                    reg_str,
                                    end_str,
                                    notax_amount,
                                )

                                # 更新合同状态
                                self._update_contract_status(
                                    cursor, contract_no, '已提报'
                                )
                            else:
                                logger.error(f'合同提报失败: {contract_name}')
                                self._record_failed_contract(
                                    cursor, contract_no, '提报失败'
                                )
                        else:
                            logger.error(f'合同保存失败: {contract_name}')
                            self._record_failed_contract(
                                cursor, contract_no, '保存失败'
                            )

                        # 处理提交后的弹窗并关闭页面
                        if self.handle_submit_popup():
                            logger.info(f'合同页面关闭完成: {contract_name}')
                        else:
                            logger.warning(f'合同页面关闭可能有问题: {contract_name}')

                    except Exception as e:
                        logger.error(
                            f'处理合同 {contract_name} ({contract_no}) 时出错: {e}'
                        )
                        self._record_failed_contract(cursor, contract_no, str(e))
                        continue

                # 提交所有更改
                conn.commit()
                logger.info('所有数据库更改已提交')

        except Exception as e:
            logger.error(f'write_to_tables执行失败: {e}')
            raise

    def _record_successful_contract(
        self, cursor, contract, provider, reg_str, end_str, notax_amount
    ) -> None:
        """记录成功处理的合同到数据库"""
        try:
            # 准备数据字典
            data = {
                '合同编号': contract.get('conid'),
                '合同名称': contract.get('conname'),
                '供应商': contract.get('provider'),
                '合同金额': contract.get('conmoney'),
                '税率': contract.get('taxrate'),
                '不含税金额': notax_amount,
                '组织机构代码': provider.get('Organization_Code'),
                '统一社会信用代码': provider.get('Tax_Identification_Number'),
                '合同开始日期': reg_str,
                '合同结束日期': end_str,
                '登记时间': contract.get('registrationtime'),
                '合同状态': '已保存',
                '处理时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }

            # 构建SQL语句
            fields = list(data.keys())
            values = list(data.values())
            fields_str = ', '.join([f'"{field}"' for field in fields])
            placeholders = ', '.join(['?' for _ in values])

            sql = f'INSERT OR REPLACE INTO 已备案合同明细 ({fields_str}) VALUES ({placeholders})'

            robust_execute(cursor, sql, values)
            logger.info(f'[成功] 合同记录已保存到数据库: {contract.get("conid")}')

        except Exception as e:
            logger.error(f'记录成功合同时发生错误: {e}')
            raise

    def _record_failed_contract(self, cursor, contract_no: str, error_msg: str) -> None:
        """记录失败的合同"""
        try:
            # 首先尝试添加必要的列（如果不存在）
            try:
                robust_execute(
                    cursor, 'ALTER TABLE 已备案合同明细 ADD COLUMN 错误信息 TEXT'
                )
                logger.info('已添加错误信息列到数据库表')
            except sqlite3.OperationalError as e:
                if 'duplicate column name' not in str(e).lower():
                    logger.warning(f'添加错误信息列时出现警告: {e}')

            try:
                robust_execute(
                    cursor, 'ALTER TABLE 已备案合同明细 ADD COLUMN 处理时间 TEXT'
                )
                logger.info('已添加处理时间列到数据库表')
            except sqlite3.OperationalError as e:
                if 'duplicate column name' not in str(e).lower():
                    logger.warning(f'添加处理时间列时出现警告: {e}')

            robust_execute(
                cursor,
                """
                INSERT OR REPLACE INTO 已备案合同明细 
                (合同编号, 合同状态, 错误信息, 处理时间) 
                VALUES (?, ?, ?, ?)
            """,
                (
                    contract_no,
                    '处理失败',
                    error_msg,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                ),
            )
            logger.info(f'[失败] 合同失败记录已保存: {contract_no} - {error_msg}')
        except Exception as e:
            logger.error(f'记录失败合同时发生错误: {e}')
            # 如果还是失败，尝试只记录基本信息
            try:
                robust_execute(
                    cursor,
                    """
                    INSERT OR REPLACE INTO 已备案合同明细 
                    (合同编号, 合同状态) 
                    VALUES (?, ?)
                """,
                    (contract_no, '处理失败'),
                )
                logger.info(f'[失败] 合同失败记录已保存（最简版本）: {contract_no}')
            except Exception as e2:
                logger.error(f'记录失败合同（最简版本）时发生错误: {e2}')
                # 不再抛出异常，避免程序中断


def main():
    config = Config()
    crawler = ContractCrawler(config)
    try:
        start_time = time.time()
        crawler.crawl_data()
        duration = (time.time() - start_time) / 60
        logger.info('任务完成，运行时间：%.2f 分钟', duration)
    except Exception as e:
        logger.error('程序执行失败: %s', e)
        sys.exit(1)


if __name__ == '__main__':
    main()
