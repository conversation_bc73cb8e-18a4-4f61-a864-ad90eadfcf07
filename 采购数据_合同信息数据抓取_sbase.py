# seleniumbase 改造版采购数据合同信息抓取脚本
import time
import logging
import sqlite3
from seleniumbase import BaseCase
from dataclasses import dataclass

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


@dataclass
class Config:
    CHROME_DRIVER_PATH: str = (
        r'C:\Program Files\Google\Chrome\Application\chromedriver.exe'
    )
    LOGIN_URL: str = 'https://sso.crrcgo.cc/login?v=1&client=cangqiong&returnUrl=https%3A%2F%2Fportal.crrcgo.cc%2F%2F%3F&isPortalMobile=false'
    USERNAME: str = '010800006291'
    PASSWORD: str = 'Z6h2en91@'
    DATABASE_PATH: str = r'd:\user\pythonproject\zconline\app.db'
    MAX_RETRIES: int = 3
    WAIT_TIMEOUT: int = 20
    PAGE_LOAD_TIMEOUT: int = 30
    RETRY_INTERVAL: int = 2


class ContractDataCrawlerSBase(BaseCase):
    config: Config = Config()

    def setUp(self):
        # 设置 seleniumbase 的配置参数
        self.maximize_window = True
        self.timeout = self.config.WAIT_TIMEOUT
        self.page_load_timeout = self.config.PAGE_LOAD_TIMEOUT
        super().setUp()

    def login(self):
        self.open(self.config.LOGIN_URL)
        self.type('input[name="username"]', self.config.USERNAME)
        self.type('input[name="password"]', self.config.PASSWORD)
        # 验证码处理略（可用OCR或人工输入）
        self.click('button.loginBtn')
        self.sleep(3)
        self.assert_element('span._1RGaSniK', timeout=10)
        logger.info('登录成功')

    def navigate_to_contracts(self):
        self.click('//*[@id="theme-side"]/div[1]/div[15]')
        self.sleep(1)
        self.click('//*[@id="theme-side"]/div[1]/div[15]/div/div/a')
        self.sleep(5)
        self.switch_to_frame('iframe#tab-content-95190001')
        logger.info('已进入合同信息页面')

    def extract_table_header(self):
        # 进入iframe后，提取表头
        header_cells = self.find_elements(
            '//*[@id="datagrid"]/div/div[2]/div[2]/div[2]/table//tr[1]/td[contains(@class, "mini-grid-headerCell")]'
        )
        headers = [
            cell.text.strip() or cell.get_attribute('title') or ''
            for cell in header_cells
        ]
        if headers and headers[-1] == '':
            headers = headers[:-1]
        logger.info(f'提取到表头: {headers}')
        return headers

    def extract_table_data(self):
        data = []
        rows = self.find_elements(
            '//*[@id="datagrid"]/div/div[2]/div[4]/div[2]/div/table//tr'
        )
        for row in rows:
            # seleniumbase 兼容写法
            cells = row.find_elements('td')
            row_data = [cell.text for cell in cells][1:]  # 跳过第一个无意义单元格
            if any(cell.strip() for cell in row_data):
                data.append(row_data)
        return data

    def get_total_pages(self):
        try:
            pages = int(self.get_text('//*[@id="mini-74"]/div[2]/a[9]'))
            logger.info(f'总页数: {pages}')
            return max(1, pages)
        except Exception as e:
            logger.warning(f'获取总页数失败: {e}')
            return 1

    def navigate_to_page(self, page: int):
        if page == 1:
            return True
        try:
            self.type('input.mini-pager-index', str(page))
            self.send_keys('input.mini-pager-index', '\ue007')  # 回车
            self.sleep(2)
            return True
        except Exception as e:
            logger.warning(f'跳转到第{page}页失败: {e}')
            return False

    def test_main(self):
        self.login()
        self.navigate_to_contracts()
        total_pages = self.get_total_pages()
        total_pages = min(total_pages, 2)  # 只抓2页
        headers = self.extract_table_header()
        all_data = []
        first_page_data = self.extract_table_data()
        if first_page_data:
            all_data.extend(first_page_data)
            logger.info(f'已完成第 1/{total_pages} 页数据抓取')
        for page in range(2, total_pages + 1):
            if not self.navigate_to_page(page):
                continue
            page_data = self.extract_table_data()
            if page_data:
                all_data.extend(page_data)
                logger.info(f'已完成第 {page}/{total_pages} 页数据抓取')
        # 保存为Excel
        if all_data and headers:
            import pandas as pd

            min_len = min(len(headers), len(all_data[0]))
            headers = headers[:min_len]
            all_data = [row[:min_len] for row in all_data]
            df = pd.DataFrame(all_data, columns=headers)
            df = df.dropna(how='all').dropna(axis=1, how='all')
            output_file = '采购数据_合同信息_sbase.xlsx'
            df.to_excel(output_file, index=False)
            logger.info(f'数据已保存到 {output_file}，共 {len(df)} 条记录')
        else:
            logger.warning('未获取到任何数据或表头，无法保存')


if __name__ == '__main__':
    import pytest

    pytest.main([__file__, '-s'])
