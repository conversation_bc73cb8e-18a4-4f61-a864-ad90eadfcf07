import re
import os
import pandas as pd

LOG_FILE = r'd:\user\PythonProject\Crawl_AI\crawler.log'
OUTPUT_FILE = r'd:\user\PythonProject\Crawl_AI\失败记录.xlsx'
START_LINE = 1

STATUS_KEYWORDS = [
    '发现编辑中的合同',
    '需要处理的合同',
    '审核通过',
    '非编辑状态',
    '无合同文件',
    '提报处理失败',
    '新增物料失败',
    '无对应文件',
    '找到审批对话框',
    '未找到审批对话框',
    '处理审批对话框异常',
]
files = []


def read_log_with_mixed_encoding(file_path, start_line):
    lines = []
    line_count = 0
    with open(file_path, 'rb') as f:
        for line in f:
            line_count += 1
            if line_count < start_line:
                continue
            decoded = False
            for encoding in ['utf-8', 'gb2312', 'gb18030']:
                try:
                    decoded_line = line.decode(encoding)
                    lines.append(decoded_line)
                    decoded = True
                    break
                except UnicodeDecodeError:
                    continue
            if not decoded:
                try:
                    decoded_line = line.decode('utf-8', errors='ignore')
                    lines.append(decoded_line)
                except Exception:
                    pass
    return lines


def parse_line(line):
    time_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})')
    contract_pattern = re.compile(
        r'(CRRC\d{2}[A-Z0-9]\d{3}[A-Z0-9]\d{7}|ZC\d{2}-A\d{3})'
    )
    time_match = time_pattern.search(line)
    contract_match = contract_pattern.search(line)
    timestamp = time_match.group(1) if time_match else None
    contract_no = contract_match.group(1) if contract_match else None
    return timestamp, contract_no


def check_type(line):
    # if any(keyword in line for keyword in STATUS_KEYWORDS):
    #     return '需要处理的合同'
    # if any(keyword in line for keyword in STATUS_KEYWORDS):
    #     return '审核通过'
    # return None
    for keyword in STATUS_KEYWORDS:
        if keyword in line:
            return keyword
    return None


def process_log_lines(lines):
    for line in lines:
        timestamp, contract_no = parse_line(line)
        if not (timestamp and contract_no):
            continue
        status_type = check_type(line)
        if status_type:
            # files[status_type].append({'合同号': contract_no, '错误描述': line.strip()})
            files.append({'合同号': contract_no, '状态': status_type})


def save_results():
    try:
        total_records = len(files)
        if total_records == 0:
            return
        df = pd.DataFrame(files)
        # 按状态分组，分别写入不同sheet，内容为所有该状态的合同号
        grouped = (
            df.drop_duplicates(subset=['合同号', '状态'])
            .sort_values('合同号')
            .groupby('状态')
        )
        with pd.ExcelWriter(OUTPUT_FILE) as writer:
            for status_type, group in grouped:
                group.reset_index(drop=True, inplace=True)
                # 确保sheet_name为str类型
                sheet_name = str(status_type)
                group.to_excel(writer, sheet_name=sheet_name, index=False)
    except Exception:
        pass


def main():
    if not os.path.exists(LOG_FILE):
        return
    if os.path.exists(OUTPUT_FILE):
        os.remove(OUTPUT_FILE)

    # for key_type in STATUS_KEYWORDS:
    #     files[key_type] = []
    try:
        lines = read_log_with_mixed_encoding(LOG_FILE, START_LINE)
        process_log_lines(lines)
        save_results()
    except Exception:
        pass


if __name__ == '__main__':
    main()
