#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-05-31 21:48:56
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-15 08:46:46
# 文件相对于项目的路径   : \Crawl_AI\下载平台合同.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#

import time
import logging
import traceback
import inspect
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.keys import Keys
import sys
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from selenium.common.exceptions import (
    NoSuchElementException,
)
from time import sleep
import os
import shutil
import send2trash  # 需要安装: pip install send2trash
import sqlite3  # 新增导入
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("crawler.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

# 在文件开头添加全局变量
Success_ids = set()  # 已成功下载的合同ID集合


def log_exception_location(func_name: str = None, additional_info: str = None):
    """记录异常发生的详细位置信息"""
    try:
        current_frame = inspect.currentframe()
        caller_frame = current_frame.f_back
        filename = caller_frame.f_code.co_filename
        line_number = caller_frame.f_lineno
        function_name = caller_frame.f_code.co_name
        if not func_name:
            func_name = function_name
        location_info = f"位置: {filename}:{line_number} 在函数 {func_name}()"
        if additional_info:
            location_info += f" - {additional_info}"
        logger.error(f"异常位置信息: {location_info}")
        logger.error("完整堆栈跟踪:")
        logger.error(traceback.format_exc())
    except Exception as e:
        logger.error(f"记录异常位置时发生错误: {e}")


@dataclass
class Config:
    """配置类"""

    CHROME_DRIVER_PATH: str = (
        r"C:\Program Files\Google\Chrome\Application\chromedriver.exe"
    )
    LOGIN_URL: str = "https://sso.crrcgo.cc/login?returnUrl=https%3A%2F%2Fwww.crrcgo.cc%2F%23%2FhomePage"
    USERNAME: str = "010800006291"
    PASSWORD: str = "Z6h2en91@"
    MAX_RETRIES: int = 3
    WAIT_TIMEOUT: int = 20
    PAGE_LOAD_TIMEOUT: int = 30
    RETRY_INTERVAL: int = 2
    DOWNLOAD_DIR: str = r"C:\Users\<USER>\Downloads"
    TARGET_DIR: str = r"d:\user\合同汇总"
    DB_PATH: str = r"D:/user/PythonProject/ZConline/app.db"


class ContractCrawler:
    def __init__(self, config: Config):
        self.config = config
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        self.eprocurement_window_handle = None

    def setup_driver(self):
        """初始化浏览器驱动"""
        if self.driver and self.wait:
            return
        options = webdriver.ChromeOptions()
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("log-level=3")
        options.add_experimental_option("excludeSwitches", ["enable-logging"])
        options.page_load_strategy = "normal"
        service = Service(self.config.CHROME_DRIVER_PATH)
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.maximize_window()
        self.driver.set_page_load_timeout(self.config.PAGE_LOAD_TIMEOUT)
        self.wait = WebDriverWait(self.driver, self.config.WAIT_TIMEOUT)
        logger.info("浏览器初始化完成")

    def login(self) -> bool:
        """执行登录操作"""
        try:
            logger.info("开始登录操作")
            self.driver.get(self.config.LOGIN_URL)
            logger.info("正在访问登录页面...")

            # 等待登录页面加载
            self.wait.until(EC.presence_of_element_located((By.NAME, "username")))
            self.wait.until(
                EC.presence_of_element_located((By.NAME, "password_visible"))
            )
            username_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            password_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, "password_visible"))
            )

            username_input.send_keys(self.config.USERNAME)
            password_input.send_keys(self.config.PASSWORD)
            self.driver.find_element(By.ID, "loginBtnCode").click()

            # 检查是否出现滑动验证
            try:
                # 先移除遮罩层
                try:
                    self.driver.execute_script(
                        "document.querySelectorAll('.mask').forEach(e => e.style.display = 'none');"
                    )
                except Exception as e:
                    logger.info("移除遮罩层失败: %s", e)

            #     # 等待滑块和轨道元素可见
            #     slider = self.wait.until(
            #         EC.visibility_of_element_located(
            #             (By.CLASS_NAME, "verify-move-block")
            #         )
            #     )
            #     track = self.wait.until(
            #         EC.visibility_of_element_located(
            #             (By.CLASS_NAME, "verify-img-panel")
            #         )
            #     )
            #     # 计算拖动距离（可多试几种偏移量）
            #     distance = track.size["width"] - slider.size["width"] - 8

            #     action = ActionChains(self.driver)
            #     action.click_and_hold(slider).perform()
            #     move_total = 0
            #     # 分步拖动，模拟加速-匀速-减速
            #     while move_total < distance:
            #         if move_total < distance * 0.3:
            #             move = random.randint(8, 12)  # 加速
            #         elif move_total > distance * 0.7:
            #             move = random.randint(2, 6)  # 减速
            #         else:
            #             move = random.randint(6, 10)  # 匀速
            #         if move_total + move > distance:
            #             move = distance - move_total
            #         action.move_by_offset(move, 0).perform()
            #         move_total += move
            #         sleep(random.uniform(0.01, 0.03))  # 一气呵成
            #     sleep(0.3)  # 到终点后短暂停留
            #     action.release().perform()
            #     logger.info("滑块拖动完成")
            #     sleep(1)  # 拖动完成后等1秒
            except Exception as e:
                logger.info("未检测到滑块验证码，或滑块拖动失败: %s", e)

            # sleep(0.5)
            return True
        except Exception as e:
            log_exception_location("login", "登录过程发生错误")
            logger.error("登录过程发生错误: %s", e)
            raise

    def delete_to_recycle_bin(
        self, directory: str, file_extension: str = ".pdf"
    ) -> None:
        """将指定目录中的所有指定扩展名文件移动到回收站"""
        try:
            files = self.find_files_with_extension(directory, file_extension)
            for file_path in files:
                try:
                    send2trash.send2trash(file_path)
                    logger.info(f"已将文件移动到回收站: {file_path}")
                except Exception as e:
                    logger.warning(f"移动文件到回收站失败: {file_path}, 错误: {e}")
        except Exception as e:
            log_exception_location("delete_to_recycle_bin", "清理文件时发生错误")
            logger.error(f"清理文件时发生错误: {e}")

    def navigate_to_contracts(self) -> bool:
        """导航到合同管理页面"""
        try:
            logger.info("开始导航到合同管理页面")
            sleep(3)

            # 点击电子采购按钮
            def click_eprocurement():
                eprocurement_btn = self.wait.until(
                    EC.element_to_be_clickable(
                        (
                            By.XPATH,
                            '//*[@id="app"]/div/div[1]/div[1]/div[2]/div[1]/div/div[3]/span/span[2]',
                        )
                    )
                )
                original_handles = self.driver.window_handles
                eprocurement_btn.click()
                logger.info("已点击电子采购按钮")
                # 等待新窗口出现
                WebDriverWait(self.driver, 10).until(
                    lambda d: len(d.window_handles) > len(original_handles)
                )
                new_handles = self.driver.window_handles
                # 找到新打开的窗口
                new_window = [h for h in new_handles if h not in original_handles][0]
                self.driver.switch_to.window(new_window)
                self.eprocurement_window_handle = new_window
                logger.info("已切换到电子采购新窗口")
                return True

            if not self._retry_operation(
                click_eprocurement, error_msg="点击电子采购按钮失败"
            ):
                return False
            sleep(2)
            # 切换到电子采购窗口
            self.driver.switch_to.window(self.eprocurement_window_handle)

            # 点击合同管理菜单
            def click_contract_menu():
                contract_menu = self.wait.until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//span[contains(text(), '合同管理')]")
                    )
                )
                contract_menu.click()
                logger.info("已点击合同管理菜单")
                return True

            if not self._retry_operation(
                click_contract_menu, error_msg="点击合同管理菜单失败"
            ):
                return False
            sleep(1)

            # 点击采购合同管理子菜单
            def click_purchase_contract_menu():
                purchase_contract_menu = self.wait.until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//span[contains(text(), '采购合同管理')]")
                    )
                )
                purchase_contract_menu.click()
                logger.info("已点击采购合同管理子菜单")
                return True

            if not self._retry_operation(
                click_purchase_contract_menu, error_msg="点击采购合同管理子菜单失败"
            ):
                return False
            sleep(3)
            # 切换到主iframe
            self.driver.switch_to.default_content()
            self.driver.switch_to.frame("tab-content-95040005")
            return True
        except Exception as e:
            log_exception_location(
                "navigate_to_contracts", "导航到合同管理页面时发生错误"
            )
            logger.error(f"导航到合同管理页面时发生错误: {e}")
            return False

    def find_files_with_extension(self, directory: str, extension: str) -> List[str]:
        """查找指定目录中所有扩展名为指定字符串的文件"""
        matched_files = []
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith(extension):
                        matched_files.append(os.path.join(root, file))
            return matched_files
        except Exception as e:
            logger.warning(f"查找文件时出错: {e}")
            return matched_files

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不合法字符"""
        # 替换Windows文件名中不允许的字符
        invalid_chars = ["\\", "/", ":", "*", "?", '"', "<", ">", "|"]
        for char in invalid_chars:
            filename = filename.replace(char, "_")
        # 限制文件名长度
        if len(filename) > 200:
            filename = filename[:197] + "..."  # 保留扩展名的空间
        return filename

    def _ensure_main_iframe(self) -> bool:
        """确保在主表格iframe中"""
        try:
            # 切换到默认内容
            self.driver.switch_to.default_content()

            # 查找并切换到主iframe
            def find_and_switch_to_main_iframe():
                try:
                    # 查找可能的主iframe
                    iframe_selectors = [
                        "iframe#main",
                        "iframe[name='main']",
                        "iframe[src*='contract']",
                        "iframe[src*='purchase']",
                        "iframe.main-iframe",
                        "iframe:not([style*='display: none'])",
                    ]

                    for selector in iframe_selectors:
                        try:
                            iframes = self.driver.find_elements(
                                By.CSS_SELECTOR, selector
                            )
                            if iframes and len(iframes) > 0:
                                self.driver.switch_to.frame(iframes[0])
                                logger.info(f"已切换到主iframe: {selector}")
                                return True
                        except:
                            continue

                    # 如果没有找到特定选择器的iframe，尝试切换到第一个可见的iframe
                    iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
                    if iframes and len(iframes) > 0:
                        self.driver.switch_to.frame(iframes[0])
                        logger.info("已切换到第一个可见的iframe")
                        return True

                    logger.warning("未找到主iframe")
                    return False
                except Exception as e:
                    logger.debug(f"查找并切换到主iframe失败: {e}")
                    return False

            # 使用重试机制查找并切换到主iframe
            if not self._retry_operation(
                find_and_switch_to_main_iframe,
                max_retries=3,
                retry_interval=1,
                error_msg="查找并切换到主iframe",
            ):
                logger.warning("无法切换到主iframe")
                return False

            # 处理可能存在的遮罩层
            self._handle_mask_layer()

            return True

        except Exception as e:
            logger.warning(f"确保在主表格iframe时发生错误: {e}")
            return False

    def _get_current_page_row_infos(self) -> List[Dict[str, str]]:
        """获取当前页面的所有行信息"""
        try:
            # 等待表格加载完成
            def wait_for_table():
                try:
                    # 等待表格行加载完成
                    rows = self.driver.find_elements(
                        By.CSS_SELECTOR, "tr.mini-grid-row"
                    )
                    if rows and len(rows) > 0:
                        return True
                    return False
                except Exception as e:
                    logger.debug(f"等待表格加载失败: {e}")
                    return False

            # 使用重试机制等待表格加载
            if not self._retry_operation(
                wait_for_table,
                max_retries=5,
                retry_interval=1,
                error_msg="等待表格加载",
            ):
                logger.warning("表格加载超时")
                return []

            # 获取所有行
            rows = self.driver.find_elements(By.CSS_SELECTOR, "tr.mini-grid-row")
            if not rows:
                logger.info("未找到数据行")
                return []

            row_infos = []
            for row in rows:
                try:
                    # 获取行ID
                    row_id = row.get_attribute("id")
                    if not row_id:
                        continue

                    # 获取所有单元格
                    cells = row.find_elements(By.CSS_SELECTOR, "td.mini-grid-cell")
                    if len(cells) < 5:
                        continue

                    # 获取合同编号、合同名称和审核状态
                    contract_no = cells[5].text.strip() if len(cells) > 2 else ""
                    contract_name = cells[6].text.strip() if len(cells) > 3 else ""
                    audit_status = cells[16].text.strip() if len(cells) > 4 else ""

                    row_infos.append(
                        {
                            "row_id": row_id,
                            "contract_no": contract_no,
                            "contract_name": contract_name,
                            "audit_status": audit_status,
                        }
                    )
                except Exception as e:
                    logger.debug(f"处理行数据时出错: {e}")
                    continue

            logger.info(f"成功获取 {len(row_infos)} 行数据")
            return row_infos

        except Exception as e:
            logger.warning(f"获取当前页面行信息时发生错误: {e}")
            return []

    def _retry_operation(
        self,
        operation_func,
        max_retries: int = None,
        retry_interval: int = None,
        error_msg: str = None,
    ) -> Any:
        """通用重试操作函数"""
        if max_retries is None:
            max_retries = self.config.MAX_RETRIES
        if retry_interval is None:
            retry_interval = self.config.RETRY_INTERVAL

        for attempt in range(max_retries):
            try:
                return operation_func()
            except Exception as e:
                if error_msg:
                    logger.warning(
                        f"{error_msg}: {e}，尝试 {attempt + 1}/{max_retries}"
                    )
                else:
                    logger.warning(f"操作失败: {e}，尝试 {attempt + 1}/{max_retries}")

                if attempt < max_retries - 1:
                    sleep(retry_interval)
                else:
                    logger.error(f"操作失败，已达到最大重试次数 {max_retries}")
                    raise
        return None

    def _click_edit_button_by_row_id(
        self, row_id: str, contract_no: str, edit_no: int
    ) -> bool:
        """根据行ID点击编辑按钮"""
        try:
            edit_button = self.driver.find_elements(By.ID, "searchBtn3")[edit_no]
            if not edit_button:
                logger.warning(f"未找到合同 {contract_no} 的行元素")
                return False
            edit_button.click()
            logger.info(f"已点击合同 {contract_no} 的编辑按钮")
            return True
        except Exception as e:
            logger.error(f"合同 {contract_no} 的编辑按钮点击失败: {e}")
            return False

    def _get_current_contract_edit_iframe_name(self) -> Optional[str]:
        """获取当前合同编辑页面的iframe名称"""
        try:
            # 切换到默认内容
            self.driver.switch_to.default_content()

            # 等待iframe加载
            def wait_for_iframe():
                try:
                    # 查找所有iframe
                    iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
                    if not iframes or len(iframes) == 0:
                        return None

                    # 查找合同编辑页面的iframe
                    for iframe in iframes:
                        iframe_name = iframe.get_attribute("name")
                        if iframe_name and ("mini-iframe" in iframe_name.lower()):
                            return iframe_name

                    # 如果没有找到特定名称的iframe，返回最后一个iframe的名称
                    last_iframe_name = iframes[-1].get_attribute("name")
                    if last_iframe_name:
                        return last_iframe_name

                    return None
                except Exception as e:
                    logger.debug(f"等待iframe加载失败: {e}")
                    return None

            # 使用重试机制等待iframe加载
            iframe_name = self._retry_operation(
                wait_for_iframe,
                max_retries=5,
                retry_interval=1,
                error_msg="等待合同编辑页面iframe加载",
            )

            if iframe_name:
                logger.info(f"找到合同编辑页面iframe: {iframe_name}")
                return iframe_name
            else:
                logger.warning("未找到合同编辑页面iframe")
                return None

        except Exception as e:
            logger.warning(f"获取合同编辑页面iframe名称时发生错误: {e}")
            return None

    def _handle_mask_layer(self) -> bool:
        """处理遮罩层"""
        try:
            # 使用JavaScript移除遮罩层
            def remove_mask_by_js():
                try:
                    # 尝试移除多种可能的遮罩层元素
                    mask_removal_scripts = [
                        "document.querySelectorAll('.el-loading-mask').forEach(e => e.remove());",
                        "document.querySelectorAll('.v-modal').forEach(e => e.remove());",
                        "document.querySelectorAll('.mask').forEach(e => e.remove());",
                        "document.querySelectorAll('[class*=mask]').forEach(e => e.remove());",
                        "document.querySelectorAll('[class*=loading]').forEach(e => e.remove());",
                    ]

                    for script in mask_removal_scripts:
                        self.driver.execute_script(script)

                    # 尝试设置遮罩层为不可见
                    self.driver.execute_script(
                        "document.querySelectorAll('.el-loading-mask').forEach(e => e.style.display = 'none');"
                    )
                    self.driver.execute_script(
                        "document.querySelectorAll('.v-modal').forEach(e => e.style.display = 'none');"
                    )

                    logger.info("已尝试移除遮罩层")
                    return True
                except Exception as e:
                    logger.warning(f"使用JavaScript移除遮罩层失败: {e}")
                    return False

            # 重试移除遮罩层
            return self._retry_operation(
                remove_mask_by_js, max_retries=3, error_msg="移除遮罩层失败"
            )

        except Exception as e:
            logger.warning(f"处理遮罩层时发生错误: {e}")
            return False

    def _jump_to_page(self, page_number: int) -> bool:
        """直接跳转到指定页码"""
        try:
            # 查找页码输入框
            page_input = self.driver.find_element(
                By.XPATH, '//*[@id="mini-21"]/span[2]/input'
            )
            if not page_input:
                logger.warning("未找到页码输入框")
                return False

            # 清空输入框并输入页码
            page_input.clear()
            page_input.send_keys(str(page_number))
            page_input.send_keys(Keys.ENTER)
            sleep(1)

            # 验证是否成功跳转
            current_page_text = self.driver.find_element(
                By.CLASS_NAME, "pagination-button.pagination-current"
            ).text
            if str(page_number) in current_page_text:
                logger.info(f"成功跳转到第{page_number}页")
                return True
            else:
                logger.warning(f"跳转到第{page_number}页失败")
                return False
        except Exception as e:
            logger.warning(f"跳转到第{page_number}页时发生错误: {e}")
            return False

    def _wait_and_process_download(
        self, contract_id: str, contract_no: str, contract_name: str
    ) -> bool:
        """等待下载完成并处理文件"""
        try:
            # 等待下载完成
            max_wait = 30  # 最大等待时间（秒）
            downloaded_file = None

            def wait_for_download():
                nonlocal downloaded_file
                pdf_files = self.find_files_with_extension(
                    self.config.DOWNLOAD_DIR, ".pdf"
                )
                if pdf_files:
                    # 获取最新的PDF文件（按修改时间排序）
                    pdf_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                    downloaded_file = pdf_files[0]
                    # 确认文件大小不为0
                    if os.path.getsize(downloaded_file) > 0:
                        return True
                return False

            # 使用重试机制等待下载完成
            download_success = self._retry_operation(
                wait_for_download,
                max_retries=max_wait,  # 每秒尝试一次，最多等待max_wait秒
                retry_interval=1,
                error_msg=f"等待合同 {contract_no} 文件下载",
            )

            if not download_success or not downloaded_file:
                logger.warning(f"合同 {contract_no}: 下载超时或文件为空")
                return False

            logger.info(f"合同 {contract_no}: 下载成功，文件名: {downloaded_file}")

            # 清理合同名称并重命名文件
            safe_contract_name = self._sanitize_filename(contract_name)
            new_name = os.path.join(
                self.config.DOWNLOAD_DIR,
                f"{contract_id}_{safe_contract_name}.pdf",
            )

            def process_file():
                try:
                    # 如果目标文件已存在，先删除
                    if os.path.exists(new_name):
                        os.remove(new_name)
                    os.rename(downloaded_file, new_name)

                    # 移动文件到目标目录
                    target_file = os.path.join(
                        self.config.TARGET_DIR,
                        os.path.basename(new_name),
                    )
                    # 如果目标目录中已存在同名文件，先删除
                    if os.path.exists(target_file):
                        os.remove(target_file)

                    shutil.move(new_name, self.config.TARGET_DIR)
                    logger.info(f"文件已成功移动到 {self.config.TARGET_DIR}")
                    logger.info(f"{target_file}")

                    db_path = self.config.DB_PATH  # 从配置获取数据库路径
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    # 自动建表（如果不存在）
                    # cursor.execute(
                    #     "CREATE TABLE IF NOT EXISTS 已下载合同号 (合同号 TEXT PRIMARY KEY)"
                    # )
                    # 插入合同号，忽略重复
                    cursor.execute(
                        "INSERT OR IGNORE INTO 已下载合同号 (合同号) VALUES (?)",
                        (str(contract_id),),
                    )
                    conn.commit()
                    cursor.close()
                    conn.close()
                    logger.info(f"合同号 {contract_id} 已写入数据库")

                    return True
                except Exception as e:
                    logger.error(f"处理文件时出错: {e}")
                    return False

            # # 使用重试机制处理文件
            # if not self._retry_operation(
            #     process_file,
            #     max_retries=3,
            #     retry_interval=1,
            #     error_msg=f"处理文件 {downloaded_file} 失败",
            # ):
            #     # 如果重试后仍然失败，尝试直接复制文件
            #     try:
            #         target_file = os.path.join(
            #             self.config.TARGET_DIR,
            #             f"{contract_id}_{safe_contract_name}.pdf",
            #         )
            #         shutil.copy2(downloaded_file, target_file)
            #         logger.info(f"已通过复制方式保存文件到 {target_file}")
            #         return True
            #     except Exception as copy_e:
            #         logger.error(f"复制文件也失败: {copy_e}")
            #         return False

            if process_file():
                # 新增：写入数据库（每次都新建连接和游标，用完即关）
                try:
                    db_path = self.config.DB_PATH  # 从配置获取数据库路径
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    # 自动建表（如果不存在）
                    cursor.execute(
                        "CREATE TABLE IF NOT EXISTS 已下载合同号 (合同号 TEXT PRIMARY KEY)"
                    )
                    # 插入合同号，忽略重复
                    cursor.execute(
                        "INSERT OR IGNORE INTO 已下载合同号 (合同号) VALUES (?)",
                        (str(contract_id),),
                    )
                    conn.commit()
                    cursor.close()
                    conn.close()
                    logger.info(f"合同号 {contract_id} 已写入数据库")
                except Exception as e:
                    logger.warning(f"写入数据库失败: {e}")
            return True

        except Exception as e:
            logger.error(f"等待下载并处理文件时出错: {e}")
            return False

    def _is_in_contract_page(self):
        """判断当前是否在合同管理页面"""
        try:
            # 可根据实际页面特征调整
            if "contract" in self.driver.current_url:
                return True
            iframes = self.driver.find_elements(
                By.CSS_SELECTOR, "iframe[src*='contract']"
            )
            if iframes:
                return True
            return False
        except Exception:
            return False

    def _close_all_pop_windows(self):
        """
        关闭所有 class 为 'mini-panel mini-corner-all mini-window' 的弹窗
        """
        try:
            logger.debug("检查并关闭所有弹窗")
            self.driver.switch_to.default_content()
            modals = self.driver.find_elements(
                By.CSS_SELECTOR, ".mini-panel.mini-corner-all.mini-window"
            )
            closed_count = 0
            for modal in modals:
                if modal.is_displayed():
                    try:
                        close_btn = modal.find_element(
                            By.CSS_SELECTOR, ".mini-tools-close"
                        )
                        if close_btn.is_displayed() and close_btn.is_enabled():
                            close_btn.click()
                            closed_count += 1
                            logger.info("已关闭一个弹窗")
                            sleep(0.3)
                    except Exception as e:
                        logger.warning(f"关闭弹窗时出错: {e}")
            if closed_count > 0:
                logger.info(f"共关闭 {closed_count} 个弹窗")
        except Exception as e:
            logger.warning(f"查找弹窗时出错: {e}")

    def crawl_data(self) -> None:
        """主抓取方法"""
        try:
            self.setup_driver()
            if not self.login():
                raise Exception("登录失败")

            if not self.navigate_to_contracts():
                raise Exception("导航到合同管理页面失败")

            # 清理下载目录
            self.delete_to_recycle_bin(self.config.DOWNLOAD_DIR)

            # 确保目标目录存在
            os.makedirs(self.config.TARGET_DIR, exist_ok=True)

            page = 1
            max_pages = 11  # 设置最大页数限制

            # 跳转到第一页
            if not self._ensure_main_iframe():
                logger.error("无法切换到主表格iframe")
                return

            if self._jump_to_page(page):
                logger.info(f"成功跳转到第{page}页")
                sleep(2)
            else:
                logger.warning(f"跳转到第{page}页失败")

            while page <= max_pages:
                try:
                    # 确保在主表格iframe中
                    if not self._ensure_main_iframe():
                        logger.error(f"无法切换到主表格iframe，跳过第{page}页")
                        break

                    # 获取当前页面的行信息
                    row_infos = self._get_current_page_row_infos()
                    if not row_infos or len(row_infos) == 0:
                        logger.info("未找到数据行，可能已到最后一页")
                        break

                    logger.info(f"第{page}页开始处理，共找到 {len(row_infos)} 行数据")

                    # 检查合同号是否已下载（从数据库读取）
                    try:
                        db_path = self.config.DB_PATH
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute("SELECT 合同号 FROM 已下载合同号")
                        downloaded_ids = set(row[0] for row in cursor.fetchall())
                        cursor.close()
                        conn.close()
                    except Exception as e:
                        logger.warning(f"读取数据库已下载合同号失败: {e}")
                        downloaded_ids = set()

                    # 处理每一行
                    edit_no = 0
                    for i, row_info in enumerate(row_infos):
                        try:
                            # 确保在主表格iframe中
                            if not self._ensure_main_iframe():
                                logger.warning(
                                    f"无法确保在主表格iframe中，跳过第{i + 1}行"
                                )
                                continue

                            row_id = row_info["row_id"]
                            contract_no = row_info["contract_no"]
                            contract_name = row_info["contract_name"]
                            audit_status = row_info["audit_status"]

                            if audit_status == "审核通过" and contract_no:
                                logger.info(
                                    f"✅ 找到需要处理的合同: {contract_no}，文件名: {contract_name}"
                                )

                                # 点击编辑按钮
                                if not self._click_edit_button_by_row_id(
                                    row_id, contract_no, edit_no
                                ):
                                    logger.warning(
                                        f"合同 {contract_no}: 点击编辑按钮失败，跳过"
                                    )
                                    edit_no += 1
                                    continue
                                edit_no += 1
                                sleep(1)

                                # 切换到合同编辑页面iframe
                                contract_edit_iframe_name = (
                                    self._get_current_contract_edit_iframe_name()
                                )
                                if contract_edit_iframe_name:
                                    self.driver.switch_to.frame(
                                        contract_edit_iframe_name
                                    )
                                    logger.info("已切换到合同编辑页面iframe")
                                else:
                                    logger.warning("未找到合同编辑页面iframe，跳过")
                                    continue
                                sleep(2)
                                # 获取合同ID和名称
                                try:
                                    self.wait.until(
                                        EC.presence_of_element_located(
                                            (By.ID, "remark")
                                        )
                                    )
                                    remark = self.driver.find_element(By.ID, "remark")
                                    contract_id = remark.text.strip()
                                    # 只保留英文字母、数字和“-”，其余全部过滤（包括汉字）
                                    contract_id = re.sub(
                                        r"[^A-Za-z0-9-]", "", contract_id
                                    )
                                    contract_name = self.driver.find_element(
                                        By.ID, "hetongname"
                                    ).text.strip()
                                    # if contract_id and (contract_id in downloaded_ids):
                                    if (
                                        contract_id in downloaded_ids
                                        and (len(contract_id) > 0)
                                        and ("--" not in contract_id)
                                    ):
                                        logger.info(f"合同 {contract_id}: 已下载，跳过")
                                        self._close_all_pop_windows()
                                        continue  # 跳过已下载合同

                                    # 点击查看合同按钮
                                    self.driver.find_element(By.ID, "ckht").click()
                                    sleep(1)

                                    # 切换到新窗口
                                    handles = self.driver.window_handles
                                    self.driver.switch_to.window(handles[-1])

                                    # 等待PDF iframe加载并src属性正确
                                    try:
                                        sleep(10)
                                        self.driver.switch_to.default_content()
                                        pdf_iframe = self.driver.find_element(
                                            By.ID, "EpointPDFControl1"
                                        )
                                        if pdf_iframe:
                                            self.driver.switch_to.frame(pdf_iframe)

                                        sec_iframe = self.driver.find_element(
                                            By.CLASS_NAME, "pdf-content"
                                        )

                                        # 切换到PDF iframe
                                        self.driver.switch_to.frame(sec_iframe)
                                    except Exception:
                                        logger.warning("PDF页面加载检测失败")

                                    # 清理下载目录
                                    self.delete_to_recycle_bin(self.config.DOWNLOAD_DIR)

                                    # 点击下载按钮
                                    self.driver.find_element(By.ID, "download").click()
                                    logger.info(f"合同 {contract_no}: 已点击下载按钮")
                                    sleep(2)
                                    # 使用封装的方法等待下载完成并处理文件
                                    download_processed = (
                                        self._wait_and_process_download(
                                            contract_id, contract_no, contract_name
                                        )
                                    )

                                    if not download_processed:
                                        logger.warning(
                                            f"合同 {contract_no}: 文件处理失败"
                                        )

                                    # 关闭PDF新窗口并切回电子采购窗口和主iframe
                                    try:
                                        self.driver.close()  # 关闭PDF新窗口
                                    except Exception as e:
                                        logger.warning(f"关闭PDF窗口失败: {e}")
                                    try:
                                        handles = self.driver.window_handles
                                        self.driver.switch_to.window(handles[-1])
                                        self._close_all_pop_windows()
                                    except Exception as e:
                                        logger.warning(f"关闭弹出iframe时发生异常: {e}")

                                except Exception as contract_e:
                                    logger.warning(f"处理合同详情时出错: {contract_e}")

                        except Exception as row_e:
                            logger.warning(f"处理第{i + 1}行时出错: {row_e}")
                            # 尝试恢复到主iframe
                            try:
                                self.driver.switch_to.default_content()
                                self._ensure_main_iframe()
                            except:
                                pass

                        finally:
                            try:
                                self._close_all_pop_windows()
                            except Exception as e:
                                logger.warning(f"关闭弹出iframe时发生异常: {e}")
                            try:
                                # 关闭PDF窗口后，确保回到电子采购窗口和主iframe
                                if (
                                    hasattr(self, "eprocurement_window_handle")
                                    and self.eprocurement_window_handle
                                ):
                                    self.driver.switch_to.window(
                                        self.eprocurement_window_handle
                                    )
                                    self.driver.switch_to.default_content()
                                    self.driver.switch_to.frame("tab-content-95040005")
                                    try:
                                        self._close_all_pop_windows()
                                    except Exception as e:
                                        logger.warning(f"关闭弹出iframe时发生异常: {e}")

                            except Exception as e:
                                logger.warning(f"恢复电子采购窗口和主iframe失败: {e}")
                            try:
                                self._handle_mask_layer()
                            except Exception as e:
                                logger.warning(f"移除遮罩层失败: {e}")
                            sleep(1)

                    # 翻页逻辑
                    try:
                        # 确保在主表格iframe中
                        if not self._ensure_main_iframe():
                            logger.error("翻页前无法切换到主表格iframe")
                            self._close_all_pop_windows()

                        # 处理可能的遮罩层
                        self._handle_mask_layer()

                        # 尝试跳转到下一页
                        next_page = page + 1
                        if self._jump_to_page(next_page):
                            page = next_page
                            logger.info(f"成功跳转到第{page}页")
                            sleep(2)
                        else:
                            # 检查是否有下一页按钮
                            try:
                                next_button = self.driver.find_element(
                                    By.CSS_SELECTOR, "#mini-39"
                                )
                                if "mini-disabled" in next_button.get_attribute(
                                    "class"
                                ):
                                    logger.info("已到最后一页")
                                    break
                                else:
                                    # 尝试点击下一页按钮
                                    try:
                                        next_button.click()
                                        page += 1
                                        logger.info(
                                            f"已点击下一页按钮，现在在第{page}页"
                                        )
                                        sleep(2)
                                    except:
                                        # 尝试JavaScript点击
                                        try:
                                            self.driver.execute_script(
                                                "arguments[0].click();", next_button
                                            )
                                            page += 1
                                            logger.info(
                                                f"已通过JavaScript点击下一页按钮，现在在第{page}页"
                                            )
                                            sleep(2)
                                        except:
                                            logger.warning("点击下一页按钮失败")
                                            break
                            except NoSuchElementException:
                                logger.info("未找到下一页按钮，可能已到最后一页")
                                break
                    except Exception as page_e:
                        logger.warning(f"翻页时出错: {page_e}")
                        break

                except Exception as loop_e:
                    log_exception_location("crawl_data", f"处理第{page}页时出错")
                    logger.error(f"处理第{page}页时出错: {loop_e}")
                    break

            logger.info("合同下载任务完成")

        except Exception as e:
            log_exception_location("crawl_data", "数据抓取过程发生错误")
            logger.error(f"数据抓取过程发生错误: {e}")
            raise
        finally:
            if self.driver:
                try:
                    self.driver.quit()
                    logger.info("浏览器已关闭")
                except Exception as e:
                    logger.warning(f"关闭浏览器时发生错误: {e}")


def main():
    # 清空日志文件
    log_path = os.path.join(os.path.dirname(__file__), "crawler.log")
    with open(log_path, "w", encoding="utf-8"):
        pass
    config = Config()
    crawler = ContractCrawler(config)

    try:
        start_time = time.time()
        crawler.crawl_data()
        duration = (time.time() - start_time) / 60
        logger.info("任务完成，运行时间：%.2f 分钟", duration)
    except Exception as e:
        log_exception_location("main", "程序主函数执行失败")
        logger.error("程序执行失败: %s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()
