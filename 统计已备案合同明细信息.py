#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-06-02 17:46:54
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-05 19:36:38
# 文件相对于项目的路径   : \Crawl_AI\统计已备案合同明细信息.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#

import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from time import sleep
import sqlite3

try:
    from ddddocr import DdddOcr
except ImportError:
    DdddOcr = None  # 允许无依赖环境下静默

CHROME_DRIVER_PATH = r'C:\Program Files\Google\Chrome\Application\chromedriver.exe'
LOGIN_URL = 'https://sso.crrcgo.cc/login?v=1&client=cangqiong&returnUrl=https%3A%2F%2Fportal.crrcgo.cc%2F%2F%3F&isPortalMobile=false'
USERNAME = '010800006291'
PASSWORD = 'Z6h2en91@'
OUTPUT_FILE = '已备案合同明细.xlsx'

options = webdriver.ChromeOptions()
options.add_argument('--disable-gpu')
options.add_argument('--no-sandbox')
options.add_argument('--disable-dev-shm-usage')
options.add_argument('log-level=3')
options.add_experimental_option('excludeSwitches', ['enable-logging'])
options.page_load_strategy = 'normal'
service = Service(CHROME_DRIVER_PATH)
driver = webdriver.Chrome(service=service, options=options)
driver.maximize_window()
driver.set_page_load_timeout(30)
wait = WebDriverWait(driver, 20)


def handle_captcha(driver, wait, ocr=None, max_retries=3):
    for attempt in range(max_retries):
        captcha_img = None
        try:
            captcha_img = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//img[contains(@class, 'validImg')]")
                )
            )
            # 确保图片加载
            for _ in range(10):
                w = driver.execute_script(
                    'return arguments[0].naturalWidth;', captcha_img
                )
                h = driver.execute_script(
                    'return arguments[0].naturalHeight;', captcha_img
                )
                if w > 0 and h > 0:
                    break
                time.sleep(0.2)
            img_bytes = captcha_img.screenshot_as_png
            if ocr:
                code = ocr.classification(img_bytes)
                if isinstance(code, dict) and 'result' in code:
                    code = code['result']
            else:
                code = input('请输入验证码: ')
            code = str(code).strip()
            if len(code) == 4 and code.isalnum() and len(set(code)) > 1:
                return code
        except Exception:
            pass
        # 刷新验证码
        try:
            captcha_img = wait.until(
                EC.visibility_of_element_located(
                    (By.XPATH, "//img[contains(@class, 'validImg')]")
                )
            )
            captcha_img.click()
        except Exception:
            pass
        time.sleep(1)
    return '0000'


def login():
    driver.get(LOGIN_URL)
    wait.until(EC.presence_of_element_located((By.NAME, 'username')))
    wait.until(EC.presence_of_element_located((By.NAME, 'password')))
    driver.find_element(By.NAME, 'username').send_keys(USERNAME)
    driver.find_element(By.NAME, 'password').send_keys(PASSWORD)
    # 验证码自动识别
    ocr = DdddOcr(show_ad=False) if DdddOcr else None
    code = handle_captcha(driver, wait, ocr)
    code_input = wait.until(EC.presence_of_element_located((By.NAME, 'validCode')))
    code_input.clear()
    code_input.send_keys(code)
    driver.find_element(By.CLASS_NAME, 'loginBtn').click()
    # 登录结果判断与重试
    for _ in range(10):
        time.sleep(1)
        # 检查是否进入portal页面
        if 'portal.crrcgo.cc' in driver.current_url:
            break
        # 检查验证码错误
        msgs = driver.find_elements(By.CLASS_NAME, 'el-message__content')
        if msgs and ('验证码' in msgs[0].text):
            code = handle_captcha(driver, wait, ocr)
            code_input = wait.until(
                EC.presence_of_element_located((By.NAME, 'validCode'))
            )
            code_input.clear()
            code_input.send_keys(code)
            driver.find_element(By.CLASS_NAME, 'loginBtn').click()
    # 登录后点击电子采购
    e_btn = wait.until(
        EC.element_to_be_clickable(
            (
                By.XPATH,
                "//a[contains(@class, 'kd-cq-btn') and .//span[contains(text(),'电子采购')]]",
            )
        )
    )
    e_btn.click()
    # 切换新窗口
    wait.until(lambda d: len(d.window_handles) > 1)
    driver.switch_to.window(driver.window_handles[-1])
    wait.until(EC.presence_of_element_located((By.TAG_NAME, 'body')))
    time.sleep(2)

    # 导航菜单：电子合同管理→采购合同备案

    def retry_click(xpath, description, max_retries=3):
        for _ in range(max_retries):
            try:
                el = wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
                el.click()
                return True
            except Exception:
                time.sleep(1)
        return False

    # 电子合同管理
    retry_click('//*[@id="theme-side"]/div[1]/div[11]', '电子合同管理菜单')
    # 采购合同备案
    retry_click('//*[@id="theme-side"]/div[1]/div[11]/div/div[2]/a', '合同备案')
    time.sleep(2)
    # 切换iframe
    try:
        iframe = wait.until(
            EC.presence_of_element_located((By.ID, 'tab-content-95130002'))
        )
        driver.switch_to.frame(iframe)
    except Exception:
        # 兜底：取第一个可见iframe
        iframes = driver.find_elements(By.TAG_NAME, 'iframe')
        for f in iframes:
            if f.is_displayed():
                driver.switch_to.frame(f)
                break
    time.sleep(1)


def get_table_rows(page):
    """
    从当前页获取表格数据,采用批处理模式防止stale element
    """
    data = []
    try:
        # 等待表格加载完成 - 增加更长的等待时间
        print(f'等待第{page}页表格加载...')
        time.sleep(5)  # 增加到5秒

        # 确保没有遮罩层
        if not wait_for_mask_to_disappear():
            print(f'第{page}页遮罩层未消失，但继续尝试获取数据')

        # 多次验证表格是否真正可用
        table_ready = False
        for ready_check in range(3):
            try:
                rows_view = wait.until(
                    EC.presence_of_element_located(
                        (By.CSS_SELECTOR, 'div.mini-grid-rows-view')
                    )
                )
                table = rows_view.find_element(
                    By.CSS_SELECTOR, 'table.mini-grid-table.mini-grid-rowstable'
                )
                rows = table.find_elements(
                    By.CSS_SELECTOR, 'tr.mini-grid-row, tr.mini-grid-row-alt'
                )

                if len(rows) > 0:
                    # 验证第一行是否可以正常访问
                    first_row = rows[0]
                    cells = first_row.find_elements(
                        By.CSS_SELECTOR, 'td.mini-grid-cell'
                    )
                    if len(cells) > 0:
                        print(f'第{page}页表格验证通过，准备开始数据抓取')
                        table_ready = True
                        break

                print(f'第{page}页表格验证失败，重试中...({ready_check + 1}/3)')
                time.sleep(2)

            except Exception as e:
                print(f'第{page}页表格验证出错：{e}')
                time.sleep(2)

        if not table_ready:
            print(f'第{page}页表格验证失败，无法获取数据')
            return []

        # 确保页面稳定，多次检查表格行数是否一致
        stable_row_count = None
        for stability_check in range(5):  # 增加稳定性检查次数
            try:
                rows_view = wait.until(
                    EC.presence_of_element_located(
                        (By.CSS_SELECTOR, 'div.mini-grid-rows-view')
                    )
                )
                table = rows_view.find_element(
                    By.CSS_SELECTOR, 'table.mini-grid-table.mini-grid-rowstable'
                )
                rows = table.find_elements(
                    By.CSS_SELECTOR, 'tr.mini-grid-row, tr.mini-grid-row-alt'
                )
                current_row_count = len(rows)

                if stable_row_count is None:
                    stable_row_count = current_row_count
                elif stable_row_count == current_row_count:
                    print(f'第{page}页表格数据稳定，行数：{current_row_count}')
                    break
                else:
                    print(f'第{page}页表格数据不稳定，重新检查...')
                    stable_row_count = current_row_count

                time.sleep(2)  # 增加等待时间
            except Exception as e:
                print(f'稳定性检查失败：{e}')
                time.sleep(2)

        # 1. 先获取表格容器
        rows_view = wait.until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'div.mini-grid-rows-view'))
        )
        table = rows_view.find_element(
            By.CSS_SELECTOR, 'table.mini-grid-table.mini-grid-rowstable'
        )

        # 2. 批量获取所有行
        rows = table.find_elements(
            By.CSS_SELECTOR, 'tr.mini-grid-row, tr.mini-grid-row-alt'
        )
        row_count = len(rows)
        print(f'第{page}页找到{row_count}行数据')

        # 3. 遍历处理每一行,重新获取引用避免stale
        for i in range(row_count):
            try:
                # 重新获取表格和行引用避免stale
                rows_view = driver.find_element(
                    By.CSS_SELECTOR, 'div.mini-grid-rows-view'
                )
                table = rows_view.find_element(
                    By.CSS_SELECTOR, 'table.mini-grid-table.mini-grid-rowstable'
                )
                current_rows = table.find_elements(
                    By.CSS_SELECTOR, 'tr.mini-grid-row, tr.mini-grid-row-alt'
                )

                if i >= len(current_rows):
                    print(f'行索引{i}超出范围,当前行数:{len(current_rows)}')
                    break

                row = current_rows[i]

                # 滚动到行可见
                driver.execute_script(
                    'arguments[0].scrollIntoView({block: "center"});', row
                )
                sleep(0.3)  # 增加等待时间

                # 获取单元格
                cells = row.find_elements(By.CSS_SELECTOR, 'td.mini-grid-cell')
                if len(cells) < 3:  # 检查列数
                    print(f'第{page}页第{i + 1}行列数不足:{len(cells)}')
                    continue

                row_data = []
                for idx, cell in enumerate(cells[1:], 1):  # 跳过前两列
                    try:
                        inner_div = cell.find_element(
                            By.CSS_SELECTOR, 'div.mini-grid-cell-inner'
                        )

                        if idx == 1:  # 第一列是状态列
                            state = ''
                            print(f'正在分析第{page}页第{i + 1}行的备案状态...')

                            # 方法1：检查子div的id属性
                            sub_divs = inner_div.find_elements(By.TAG_NAME, 'div')
                            for sub_div in sub_divs:
                                try:
                                    id_attr = sub_div.get_attribute('id')
                                    if id_attr:
                                        id_lower = id_attr.lower()
                                        print(f'  - 发现id属性: {id_attr}')
                                        if 'edit' in id_lower:
                                            state = '编辑中'
                                            print(f'  - 通过id判断状态: {state}')
                                            break
                                        elif 'search' in id_lower or 'view' in id_lower:
                                            state = '已备案'
                                            print(f'  - 通过id判断状态: {state}')
                                            break
                                except Exception as e:
                                    print(f'  - 获取id属性失败:{e}')

                            # 方法2：检查按钮或链接元素
                            if not state:
                                try:
                                    buttons = inner_div.find_elements(
                                        By.TAG_NAME, 'button'
                                    )
                                    links = inner_div.find_elements(By.TAG_NAME, 'a')

                                    for element in buttons + links:
                                        try:
                                            element_text = element.text.strip()
                                            element_title = element.get_attribute(
                                                'title'
                                            )
                                            element_class = element.get_attribute(
                                                'class'
                                            )

                                            print(
                                                f'  - 发现按钮/链接: text="{element_text}", title="{element_title}", class="{element_class}"'
                                            )

                                            # 根据文本内容判断
                                            if element_text:
                                                if (
                                                    '编辑' in element_text
                                                    or 'edit' in element_text.lower()
                                                ):
                                                    state = '编辑中'
                                                    print(
                                                        f'  - 通过按钮文本判断状态: {state}'
                                                    )
                                                    break
                                                elif (
                                                    '查看' in element_text
                                                    or '详情' in element_text
                                                    or 'view' in element_text.lower()
                                                ):
                                                    state = '已备案'
                                                    print(
                                                        f'  - 通过按钮文本判断状态: {state}'
                                                    )
                                                    break

                                            # 根据title属性判断
                                            if element_title:
                                                if (
                                                    '编辑' in element_title
                                                    or 'edit' in element_title.lower()
                                                ):
                                                    state = '编辑中'
                                                    print(
                                                        f'  - 通过title判断状态: {state}'
                                                    )
                                                    break
                                                elif (
                                                    '查看' in element_title
                                                    or '详情' in element_title
                                                    or 'view' in element_title.lower()
                                                ):
                                                    state = '已备案'
                                                    print(
                                                        f'  - 通过title判断状态: {state}'
                                                    )
                                                    break

                                        except Exception as e:
                                            print(f'  - 分析按钮/链接失败: {e}')

                                except Exception as e:
                                    print(f'  - 查找按钮/链接失败: {e}')

                            # 方法3：输出整个单元格的HTML用于调试
                            if not state:
                                try:
                                    cell_html = inner_div.get_attribute('outerHTML')
                                    print(
                                        f'  - 状态列HTML内容: {cell_html[:500]}'
                                    )  # 显示前500个字符

                                    # 尝试通过HTML内容判断
                                    if 'edit' in cell_html.lower():
                                        state = '编辑中'
                                        print(f'  - 通过HTML内容判断状态: {state}')
                                    elif (
                                        'search' in cell_html.lower()
                                        or 'view' in cell_html.lower()
                                    ):
                                        state = '已备案'
                                        print(f'  - 通过HTML内容判断状态: {state}')

                                except Exception as e:
                                    print(f'  - 获取HTML内容失败: {e}')

                            # 如果仍然没有获取到状态，设置为未知
                            if not state:
                                state = '状态未知'
                                print(f'  - 无法确定状态，设置为: {state}')
                            else:
                                print(f'  - 最终确定状态: {state}')

                            row_data.append(state)
                        else:
                            # 先尝试获取title,再获取text
                            val = inner_div.get_attribute('title')
                            if val is None or val.strip() == '':
                                val = inner_div.text
                            row_data.append(val)

                    except Exception as e:
                        print(f'处理单元格失败[{idx}]:{str(e)}')
                        row_data.append('')

                if row_data:  # 只添加非空行
                    data.append(row_data)

            except Exception as e:
                print(f'处理第{page}页第{i + 1}行失败:{str(e)}')
                continue

    except Exception as e:
        print(f'表格处理失败:{str(e)}')

    return data


def wait_for_mask_to_disappear(max_wait=10):
    """
    等待遮罩层消失
    """
    print('检查是否存在遮罩层...')
    for i in range(max_wait):
        try:
            # 检查是否存在遮罩层
            masks = driver.find_elements(By.CSS_SELECTOR, 'div.mini-mask-background')
            visible_masks = [mask for mask in masks if mask.is_displayed()]

            if not visible_masks:
                print('遮罩层已消失')
                return True
            else:
                print(f'等待遮罩层消失...({i + 1}/{max_wait})')
                time.sleep(1)
        except Exception:
            # 如果找不到遮罩层元素，说明没有遮罩层
            return True

    print('遮罩层等待超时')
    return False


def wait_for_page_load(expected_page, previous_page_first_row_data=None):
    """
    等待页面数据加载完成的函数
    """
    max_retries = 15  # 减少重试次数

    print(f'开始等待第{expected_page}页加载...')

    # 首先等待遮罩层消失
    if not wait_for_mask_to_disappear():
        print('等待遮罩层消失失败')
        return False

    for attempt in range(max_retries):
        try:
            time.sleep(2)
            print(f'第{expected_page}页加载验证 - 尝试 {attempt + 1}/{max_retries}')

            # 检查表格是否有数据
            rows_view = driver.find_element(By.CSS_SELECTOR, 'div.mini-grid-rows-view')
            table = rows_view.find_element(
                By.CSS_SELECTOR, 'table.mini-grid-table.mini-grid-rowstable'
            )
            rows = table.find_elements(
                By.CSS_SELECTOR, 'tr.mini-grid-row, tr.mini-grid-row-alt'
            )

            if len(rows) > 0:
                print(f'第{expected_page}页数据加载完成，找到{len(rows)}行')
                return True
            else:
                print(f'第{expected_page}页暂无数据，继续等待...')

        except Exception as e:
            print(
                f'等待第{expected_page}页加载，尝试{attempt + 1}/{max_retries}，错误：{e}'
            )
            time.sleep(2)

    print(f'第{expected_page}页加载超时')
    return False


def get_first_row_data(page):
    """
    获取当前页第一行的数据用于比较
    """
    try:
        rows_view = driver.find_element(By.CSS_SELECTOR, 'div.mini-grid-rows-view')
        table = rows_view.find_element(
            By.CSS_SELECTOR, 'table.mini-grid-table.mini-grid-rowstable'
        )
        rows = table.find_elements(
            By.CSS_SELECTOR, 'tr.mini-grid-row, tr.mini-grid-row-alt'
        )

        if len(rows) > 0:
            first_row = rows[0]
            cells = first_row.find_elements(By.CSS_SELECTOR, 'td.mini-grid-cell')
            if len(cells) >= 3:
                first_row_data = []
                for cell in cells[2:5]:  # 取前3列
                    try:
                        inner_div = cell.find_element(
                            By.CSS_SELECTOR, 'div.mini-grid-cell-inner'
                        )
                        val = inner_div.get_attribute('title')
                        if val is None or val.strip() == '':
                            val = inner_div.text
                        first_row_data.append(val)
                    except Exception:
                        first_row_data.append('')
                return first_row_data
    except Exception as e:
        print(f'获取第{page}页第一行数据失败：{e}')
    return None


def click_next_page_button(max_retries=5):
    """
    点击下一页按钮，处理可能的遮罩层干扰
    """
    for attempt in range(max_retries):
        try:
            print(f'尝试点击下一页按钮 ({attempt + 1}/{max_retries})')

            # 先等待遮罩层消失
            wait_for_mask_to_disappear()

            # 等待下一页按钮可点击
            next_btn = wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, '//*[@id="mini-42"]/div[2]/a[10]')
                )
            )

            # 滚动到按钮位置
            driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center'});", next_btn
            )
            time.sleep(1)

            # 再次检查遮罩层
            if not wait_for_mask_to_disappear():
                print('遮罩层仍然存在，尝试其他方法')
                # 尝试使用JavaScript点击
                driver.execute_script('arguments[0].click();', next_btn)
            else:
                # 正常点击
                next_btn.click()

            print('成功点击下一页按钮')
            return True

        except Exception as e:
            print(f'点击下一页按钮失败：{e}')
            if attempt < max_retries - 1:
                time.sleep(2)
                continue
            else:
                return False

    return False


def recover_from_error():
    """
    从错误中恢复，尝试刷新页面或重新定位到正确的iframe
    """
    try:
        print('尝试从错误中恢复...')

        # 尝试切换回主框架然后重新进入iframe
        driver.switch_to.default_content()
        time.sleep(2)

        # 重新进入iframe
        try:
            iframe = wait.until(
                EC.presence_of_element_located((By.ID, 'tab-content-95130002'))
            )
            driver.switch_to.frame(iframe)
            print('成功重新进入iframe')
        except Exception:
            # 兜底：取第一个可见iframe
            iframes = driver.find_elements(By.TAG_NAME, 'iframe')
            for f in iframes:
                if f.is_displayed():
                    driver.switch_to.frame(f)
                    print('成功进入备用iframe')
                    break

        # 等待遮罩层消失
        wait_for_mask_to_disappear()

        return True

    except Exception as e:
        print(f'错误恢复失败：{e}')
        return False


def main():
    login()
    all_data = []
    processed_pages = set()  # 记录已处理的页面
    seen_data = set()  # 记录已见过的数据行，用于去重

    # 获取总页数
    try:
        max_page_text = driver.find_element(
            By.XPATH, '//*[@id="mini-42"]/div[2]/a[9]'
        ).text
        max_page = int(max_page_text)
        print(f'总共有 {max_page} 页数据')
    except Exception as e:
        print(f'获取总页数失败：{e}')
        max_page = 1

    current_page = 1

    # 处理第一页
    if current_page not in processed_pages:
        print(f'开始处理第{current_page}页')
        rows = get_table_rows(current_page)
        if rows:
            # 添加数据去重逻辑
            new_rows = 0
            for row in rows:
                row_tuple = tuple(row)  # 转换为元组用于哈希
                if row_tuple not in seen_data:
                    seen_data.add(row_tuple)
                    all_data.append(row)
                    new_rows += 1
            print(
                f'第{current_page}页获取到{len(rows)}行数据，其中{new_rows}行为新数据'
            )
            processed_pages.add(current_page)
        else:
            print(f'第{current_page}页没有获取到数据')

    # 获取第一页的第一行数据用于后续比较
    # previous_first_row = get_first_row_data(current_page)  # 不再需要

    # 处理后续页面
    for target_page in range(2, max_page + 1):
        # 检查是否已经处理过这一页
        if target_page in processed_pages:
            print(f'第{target_page}页已经处理过，跳过')
            continue

        try:
            print(f'准备翻到第{target_page}页')

            # 点击下一页
            if click_next_page_button():
                print(f'成功翻到第{target_page}页，开始等待页面加载...')

                # 等待页面加载完成
                if wait_for_page_load(target_page):
                    print(f'第{target_page}页加载验证通过，开始抓取数据...')

                    # 直接开始数据抓取
                    rows = get_table_rows(target_page)
                    if rows:
                        # 添加数据去重逻辑
                        new_rows = 0
                        for row in rows:
                            row_tuple = tuple(row)  # 转换为元组用于哈希
                            if row_tuple not in seen_data:
                                seen_data.add(row_tuple)
                                all_data.append(row)
                                new_rows += 1
                        print(
                            f'第{target_page}页获取到{len(rows)}行数据，其中{new_rows}行为新数据'
                        )
                        processed_pages.add(target_page)
                        # 不再需要更新 previous_first_row
                    else:
                        print(f'第{target_page}页没有获取到数据')
                else:
                    print(f'第{target_page}页加载失败，跳过')
            else:
                print(f'无法点击下一页按钮，跳过第{target_page}页')

            current_page = target_page

        except Exception as e:
            print(f'处理第{target_page}页时发生错误：{e}')

            # 尝试从错误中恢复
            if recover_from_error():
                print('错误恢复成功，继续处理下一页')
                current_page = target_page
                continue
            else:
                print('错误恢复失败，停止处理')
                break

    print(f'总共处理了 {len(processed_pages)} 页，获取到 {len(all_data)} 行唯一数据')
    print(f'已处理的页面：{sorted(processed_pages)}')

    # 保存数据
    if all_data:
        df = pd.DataFrame(all_data)
        df.to_excel(OUTPUT_FILE, index=False, header=False)
        print(f'数据已保存到 {OUTPUT_FILE}')
    else:
        print('没有获取到任何数据')

    driver.quit()

    with sqlite3.connect(r'd:\user\PythonProject\ZConline\app.db', timeout=60) as conn:
        conn.execute('PRAGMA journal_mode=WAL;')
        cursor = conn.cursor()

    for data in all_data:
        crrc_contract_no = data[1]
        contract_status = data[12]
        if crrc_contract_no:
            sql = f"UPDATE 已备案合同明细 SET 合同状态 = '{contract_status}' WHERE 合同编号 = '{crrc_contract_no}'"
            cursor.execute(sql)
    cursor.connection.commit()


if __name__ == '__main__':
    main()
