import os
import sqlite3
from glob import glob

# 数据库和目录路径配置
DB_PATH = r'd:\user\pythonproject\ZConline\app.db'
CONTRACT_DIR = r'd:\user\合同备案'


def get_contract_records():
    """从数据库读取合同记录"""
    conn = sqlite3.connect(DB_PATH)
    try:
        cursor = conn.cursor()
        # 获取全部字段并按合同编号排序
        cursor.execute("""
            SELECT *
            FROM 品类及合同文件 
            ORDER BY 合同编号
        """)
        return cursor.fetchall()
    finally:
        conn.close()


def find_contract_file(contract_no):
    """在合同备案目录中查找对应的合同文件"""
    # 获取目录下所有文件
    files = glob(os.path.join(CONTRACT_DIR, '*.*'))

    for file in files:
        filename = os.path.basename(file)
        # 按_分割文件名
        parts = filename.split('_', 1)
        if len(parts) > 0 and parts[0].upper().strip() == contract_no.upper().strip():
            return filename
    return None


def update_contract_filename(contract_no, filename):
    """更新数据库中的合同文件名"""
    conn = sqlite3.connect(DB_PATH)
    try:
        cursor = conn.cursor()
        cursor.execute(
            """
            UPDATE 品类及合同文件
            SET 合同文件名 = ?
            WHERE 合同编号 = ?
        """,
            (filename, contract_no),
        )
        conn.commit()
        return cursor.rowcount > 0
    finally:
        conn.close()


def main():
    # 获取所有合同记录
    records = get_contract_records()
    print(f'获取到{len(records)}条合同记录')

    update_count = 0
    for record in records:
        # 获取合同编号 (假设合同编号是第一个字段)
        contract_no = record[0]
        current_filename = record[-1]  # 假设合同文件名是最后一个字段

        # 查找合同文件
        found_filename = find_contract_file(contract_no)
        if found_filename:
            # 如果找到文件且与当前数据库中的文件名不同，则更新数据库
            if found_filename != current_filename:
                if update_contract_filename(contract_no, found_filename):
                    print(f'更新合同 {contract_no} 的文件名为: {found_filename}')
                    update_count += 1
        else:
            print(f'未找到合同 {contract_no} 对应的文件')

    print(f'完成更新，共更新了{update_count}条记录')


if __name__ == '__main__':
    main()
