#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-05-30 20:56:11
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-05-31 09:29:39
# 文件相对于项目的路径   : \Crawl_AI\check_contract_files.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
import os
import pandas as pd
import re
import glob

# 配置路径
excel_path = r"D:\user\合同备案\采购合同数据.xlsx"
contract_dir = r"D:\user\合同备案"

# 读取Excel
try:
    df = pd.read_excel(excel_path)
except Exception as e:
    print(f"无法读取Excel文件: {e}")
    exit(1)

# 确保有"合同文件名"和"合同是否存在"列
if "合同文件名" not in df.columns:
    print('缺少"合同文件名"列')
    exit(1)
if "合同是否存在" not in df.columns:
    df["合同是否存在"] = ""

# 按合同编号排序（如果有该列）
if "合同编号" in df.columns:
    df = df.sort_values("合同编号")

# 检查文件是否存在
for idx, row in df.iterrows():
    file_name = str(row["合同文件名"]).strip()
    found = False
    search_names = [file_name]
    # 如果file_name查找不到，后续会用合同名称
    if file_name and file_name.lower() != "nan":
        # 先用file_name查找
        for search_name in search_names:
            name_to_try = search_name
            file_path = os.path.join(contract_dir, name_to_try.strip())
            if os.path.isfile(file_path):
                df.at[idx, "合同是否存在"] = "是"
                df.at[idx, "合同文件名"] = (
                    f'=HYPERLINK("{file_path.strip()}", "{name_to_try.strip()}")'
                )
                found = True
                break
            # 没找到，尝试分离扩展名
            name, ext = os.path.splitext(name_to_try)
            name = name.strip()
            # 如果没有扩展名，尝试加三种扩展名
            if not ext:
                for suf in [".pdf", ".doc", ".docx"]:
                    try_name = (name + suf).strip()
                    try_path = os.path.join(contract_dir, try_name)
                    if os.path.isfile(try_path):
                        df.at[idx, "合同是否存在"] = "是"
                        df.at[idx, "合同文件名"] = (
                            f'=HYPERLINK("{try_path.strip()}", "{try_name}")'
                        )
                        found = True
                        break
                if found:
                    break
            # 尝试去掉开头年份和空格
            match = re.match(r"^(\d{4})[\s_]+(.+)$", name)
            if match:
                new_name = match.group(2).strip()
                for suf in [".pdf", ".doc", ".docx"]:
                    try_name = (new_name + suf).strip()
                    try_path = os.path.join(contract_dir, try_name)
                    if os.path.isfile(try_path):
                        df.at[idx, "合同是否存在"] = "是"
                        df.at[idx, "合同文件名"] = (
                            f'=HYPERLINK("{try_path.strip()}", "{try_name}")'
                        )
                        found = True
                        break
                if found:
                    break
            # 新增：尝试去掉ZC+两位数字+A+三位数字+空格
            match2 = re.match(r"^(ZC\d{2}-A\d{3})\s+(.+)$", name)
            if match2:
                new_name2 = match2.group(2).strip()
                for suf in [".pdf", ".doc", ".docx"]:
                    try_name = (new_name2 + suf).strip()
                    try_path = os.path.join(contract_dir, try_name)
                    if os.path.isfile(try_path):
                        df.at[idx, "合同是否存在"] = "是"
                        df.at[idx, "合同文件名"] = (
                            f'=HYPERLINK("{try_path.strip()}", "{try_name}")'
                        )
                        found = True
                        break
                if found:
                    break
        # 如果file_name所有方式都没找到，尝试用合同名称
        if not found and "合同名称" in row and str(row["合同名称"]).strip():
            alt_name = str(row["合同名称"]).strip()
            if alt_name and alt_name.lower() != "nan":
                for search_name in [alt_name]:
                    name_to_try = search_name
                    file_path = os.path.join(contract_dir, name_to_try.strip())
                    if os.path.isfile(file_path):
                        df.at[idx, "合同是否存在"] = "是"
                        df.at[idx, "合同文件名"] = (
                            f'=HYPERLINK("{file_path.strip()}", "{name_to_try.strip()}")'
                        )
                        found = True
                        break
                    name, ext = os.path.splitext(name_to_try)
                    name = name.strip()
                    if not ext:
                        for suf in [".pdf", ".doc", ".docx"]:
                            try_name = (name + suf).strip()
                            try_path = os.path.join(contract_dir, try_name)
                            if os.path.isfile(try_path):
                                df.at[idx, "合同是否存在"] = "是"
                                df.at[idx, "合同文件名"] = (
                                    f'=HYPERLINK("{try_path.strip()}", "{try_name}")'
                                )
                                found = True
                                break
                        if found:
                            break
                    match = re.match(r"^(\d{4})[\s_]+(.+)$", name)
                    if match:
                        new_name = match.group(2).strip()
                        for suf in [".pdf", ".doc", ".docx"]:
                            try_name = (new_name + suf).strip()
                            try_path = os.path.join(contract_dir, try_name)
                            if os.path.isfile(try_path):
                                df.at[idx, "合同是否存在"] = "是"
                                df.at[idx, "合同文件名"] = (
                                    f'=HYPERLINK("{try_path.strip()}", "{try_name}")'
                                )
                                found = True
                                break
                        if found:
                            break
                    match2 = re.match(r"^(ZC\d{2}-A\d{3})\s+(.+)$", name)
                    if match2:
                        new_name2 = match2.group(2).strip()
                        for suf in [".pdf", ".doc", ".docx"]:
                            try_name = (new_name2 + suf).strip()
                            try_path = os.path.join(contract_dir, try_name)
                            if os.path.isfile(try_path):
                                df.at[idx, "合同是否存在"] = "是"
                                df.at[idx, "合同文件名"] = (
                                    f'=HYPERLINK("{try_path.strip()}", "{try_name}")'
                                )
                                found = True
                                break
                        if found:
                            break
    # 模糊查找（仅在精确查找都没找到时执行）
    if not found:
        # 先用file_name模糊查找
        fuzzy_keys = []
        if file_name and file_name.lower() != "nan":
            fuzzy_keys.append(file_name.strip())
        if "合同名称" in row and str(row["合同名称"]).strip():
            alt_name = str(row["合同名称"]).strip()
            if alt_name and alt_name.lower() != "nan":
                fuzzy_keys.append(alt_name)
        for key in fuzzy_keys:
            # 查找所有包含key的文件（不区分扩展名）
            for suf in ["*.pdf", "*.doc", "*.docx"]:
                pattern = os.path.join(contract_dir, f"*{key}*{suf[-4:]}")
                files = glob.glob(pattern)
                if files:
                    # 只取第一个
                    real_file = files[0]
                    real_name = os.path.basename(real_file)
                    df.at[idx, "合同是否存在"] = "是"
                    df.at[idx, "合同文件名"] = (
                        f'=HYPERLINK("{real_file.strip()}", "{real_name}")'
                    )
                    found = True
                    break
            if found:
                break
    if not found:
        df.at[idx, "合同是否存在"] = "否"
        df.at[idx, "合同文件名"] = ""

# 保存结果
output_path = excel_path.replace(".xlsx", "_带文件校验.xlsx")
df.to_excel(output_path, index=False)
print(f"校验完成，结果已保存到: {output_path}")
