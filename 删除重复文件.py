import os
import re
from glob import glob
from docx import Document
import PyPDF2
import sqlite3
from win32com.shell import shell, shellcon
import pandas as pd

matches = []


def move_to_recycle_bin(filepath):
    """移动文件到回收站"""
    if os.path.exists(filepath):
        try:
            res = shell.SHFileOperation(
                (
                    0,
                    shellcon.FO_DELETE,
                    filepath,
                    None,
                    shellcon.FOF_SILENT
                    | shellcon.FOF_ALLOWUNDO
                    | shellcon.FOF_NOCONFIRMATION,
                    None,
                    None,
                )
            )
            if res[1] != 0:
                print(f'删除文件{filepath}失败')
            else:
                print(f'删除文件{filepath}成功')
        except Exception as e:
            print(f'删除文件失败 {filepath}: {e}')
    else:
        print(f'文件：{filepath}不存在！')


# 支持的文件扩展名
EXTS = ['.doc', '.docx', '.pdf']
# EXTS = ['.pdf']
CONTRACT_DIR = r'D:\user\合同汇总'

# 合同编号正则
PATTERNS = [
    re.compile(r'ZC\d{2}-A\d{3}'),
    # 新规则：CRRC+2位数字+1位字母或数字+3位数字+1位字母或数字+7位数字
    re.compile(r'CRRC\d{2}[A-Z0-9]\d{3}[A-Z0-9]\d{7}'),
]


def rename_file_if_match_pattern(file_path):
    basename = os.path.basename(file_path)
    name, ext = os.path.splitext(basename)

    matched_pattern = None
    match = None
    for pattern in PATTERNS:
        match = pattern.search(name)
        if match and match.end() == len(name):  # 确保匹配出现在字符串结尾
            matched_pattern = pattern
            break

    if matched_pattern and match:
        contract_no = match.group(0)
        print(
            f'[DEBUG] basename: {basename}, name: {name}, contract_no: {contract_no}, match.start(): {match.start()}'
        )
        if not contract_no:
            print(f'[ERROR] 合同号为空，跳过重命名: {basename}\n')
            return
        prefix = name[: match.start()]
        if prefix:
            new_name_without_ext = f'{contract_no}_{prefix}'
        else:
            new_name_without_ext = contract_no
        new_name = f'{new_name_without_ext}{ext}'
        if not new_name_without_ext.strip():
            print(f'[ERROR] 新文件名主体为空，跳过重命名: {basename}\n')
            return
        new_path = os.path.join(os.path.dirname(file_path), new_name)
        try:
            os.rename(file_path, new_path)
            print(f'重命名成功: {basename} -> {new_name}\n')
        except Exception as e:
            print(f'重命名失败: {basename}, 错误: {e}\n')
    else:
        print(f'未找到匹配的正则结尾: {basename}\n')


# 判断文件名是否已含合同号
def extract_contract_no_from_filename(filename):
    for pat in PATTERNS:
        m = pat.search(filename)
        if m:
            return m.group(0)
    return None


# 从文本中提取合同号
def extract_contract_no_from_text(text):
    for pat in PATTERNS:
        m = pat.search(text)
        if m:
            return m.group(0)
    return None


# 读取docx文本
def read_docx_text(path):
    try:
        doc = Document(path)
        return '\n'.join([p.text for p in doc.paragraphs])
    except Exception:
        return ''


# 读取pdf文本
def read_pdf_text(path):
    # 首先尝试使用PyPDF2直接提取文本
    try:
        with open(path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            text = ''
            for page in reader.pages:
                extracted = page.extract_text()
                if extracted:
                    text += extracted
            if text.strip():  # 如果有内容，返回结果
                return text
    except Exception as e:
        print(f'PyPDF2 读取失败: {e}')
    return ''


# 读取doc文本
def read_doc_text(path):
    try:
        import win32com.client

        word = win32com.client.Dispatch('Word.Application')
        word.Visible = False
        doc = word.Documents.Open(path)
        text = doc.Content.Text
        doc.Close()
        word.Quit()
        return text
    except Exception as e:
        print(f'读取doc文件失败: {path}, 错误: {e}')
        return ''


def process_file(file, conn, cursor):
    print(f'开始处理文件: {file}')
    basename = os.path.basename(file)
    name, ext = os.path.splitext(basename)
    ClearName = name
    """
    # 查找所有匹配的合同号
    for pat in PATTERNS:
        found = pat.finditer(ClearName)
        for m in found:
            start = m.start()
            end = m.end()
            value = m.group(0)
            matches.append((start, end, value, file))
    # 按出现顺序排序
    matches.sort(key=lambda x: x[0])
    if len(matches) >= 2:
        # 只保留第一个，删除其余
        first_start, first_end, first_no, filename = matches[0]
        # 构建新ClearName：第一个合同号 + 剩余部分（去除所有其他合同号）
        rest = ClearName[:first_start] + first_no + ClearName[first_end:]
        # 去除其余合同号
        for _, _, no, _ in matches[1:]:
            rest = rest.replace(no, '')
        # 保证第一个合同号在最前面
        if not rest.startswith(first_no):
            rest = first_no + '_' + rest.replace(first_no, '', 1).lstrip('_')
        else:
            # 保证后面紧跟_
            if not rest[len(first_no) :].startswith('_'):
                rest = first_no + '_' + rest[len(first_no) :].lstrip('_')
        new_name = rest.strip('_') + ext
        new_path = os.path.join(CONTRACT_DIR, new_name)
        if file != new_path:
            try:
                os.rename(file, new_path)
                print(f'重命名成功: {basename} -> {new_name}')
            except Exception as e:
                print(f'重命名失败: {basename} -> {new_name}, 错误: {e}')
        return
    elif len(matches) == 1:
        start, end, contract_no, filename = matches[0]
        # 如果合同号已在最前面
        if start == 0:
            # 保证后面紧跟_
            rest = ClearName[end:]
            if not rest.startswith('_'):
                rest = '_' + rest.lstrip('_')
            new_name = contract_no + rest + ext
            new_name = new_name.strip('_')
        else:
            # 移动到最前面
            rest = ClearName[:start] + ClearName[end:]
            rest = rest.lstrip('_')
            if rest:
                new_name = f'{contract_no}_{rest}{ext}'
            else:
                new_name = f'{contract_no}{ext}'
        new_path = os.path.join(CONTRACT_DIR, new_name)
        if file != new_path:
            try:
                os.rename(file, new_path)
                print(f'重命名成功: {basename} -> {new_name}')
            except Exception:
                print(f'重命名失败: {basename} -> {new_name}, 删除{file}')
                if os.path.exists(new_path):
                    os.remove(file)
        return
        """
    # 下面为原有未找到合同号的处理逻辑
    # contract_no = extract_contract_no_from_filename(basename)
    contract_no = ''
    if contract_no:
        if re.match(
            rf'^{re.escape(contract_no)}[\s_\-]', basename, flags=re.IGNORECASE
        ):
            return  # 已经规范，无需重命名
        pattern = rf'^{contract_no}[\s_\-]*'
        rest_name = re.sub(pattern, '', basename, flags=re.IGNORECASE)
        name, ext1 = os.path.splitext(rest_name)
        pattern = rf'.*?{re.escape(contract_no)}$'
        new_name_without_ext = re.sub(pattern, '', name, flags=re.IGNORECASE).rstrip(
            '_'
        )
        new_name = f'{new_name_without_ext}{ext1}'
        new_name = re.sub(
            rf'({re.escape(contract_no)})[\s_\-]+\1',
            r'\1',
            new_name,
            flags=re.IGNORECASE,
        )
        new_path = os.path.join(CONTRACT_DIR, new_name)
        if file != new_path:
            try:
                os.rename(file, new_path)
                print(f'重命名成功: {basename} -> {new_name}')
            except Exception as e:
                print(f'重命名失败: {basename} -> {new_name}, 错误: {e}')
    else:
        text = ''
        if file.lower().endswith('.docx'):
            text = read_docx_text(file)
        elif file.lower().endswith('.pdf'):
            try:
                text = read_pdf_text(file)
            except Exception as e:
                print(f'读取pdf文件失败: {file}, 错误: {e}')
        elif file.lower().endswith('.doc'):
            text = read_doc_text(file)
        idx = text.find('合同编号')
        if idx != -1:
            search_text = text[idx : idx + 100]
            contract_no = extract_contract_no_from_text(search_text)
        if not contract_no:
            contract_no = extract_contract_no_from_text(text)
        if contract_no:
            basename_contract_id = extract_contract_no_from_text(basename)
            if basename_contract_id and basename_contract_id != contract_no:
                new_name = re.sub(
                    basename_contract_id,
                    contract_no,
                    basename,
                    flags=re.IGNORECASE,
                )
                new_path = os.path.join(CONTRACT_DIR, new_name)
                if file != new_path:
                    try:
                        os.rename(file, new_path)
                        print(f'重命名: {basename} -> {new_name}')
                    except Exception:
                        move_to_recycle_bin(file)
            elif basename_contract_id:
                new_path = os.path.join(CONTRACT_DIR, basename)
            else:
                new_name = f'{contract_no}_{basename}'
                new_name = re.sub(
                    rf'({re.escape(contract_no)})[\s_\-]+\1',
                    r'\1',
                    new_name,
                    flags=re.IGNORECASE,
                )
                new_path = os.path.join(CONTRACT_DIR, new_name)
                if file != new_path:
                    try:
                        os.rename(file, new_path)
                        print(f'重命名: {basename} -> {new_name}')
                    except Exception:
                        move_to_recycle_bin(file)
            try:
                cursor.execute(
                    f"select conname,provider,conmoney,taxrate,operator from contract WHERE conid='{contract_no}'"
                )
                conname, provider, conmoney, taxrate, operator = cursor.fetchone()
                cursor.execute(
                    f"select 1 from 品类及合同文件 WHERE 合同编号='{contract_no}'"
                )
                if cursor.fetchone():
                    cursor.execute(
                        'UPDATE 品类及合同文件 SET 合同名称=?, 对手方名称=?, 合同金额=?, 约定税率=?, 合同文件名=? WHERE 合同编号=?',
                        (
                            conname,
                            provider,
                            conmoney,
                            taxrate,
                            new_path,
                            contract_no,
                        ),
                    )
                    conn.commit()
                    print(f'更新{contract_no}成功!')
                else:
                    if '周云飞' in operator:
                        category_name = (
                            '信息化软硬件及工装设备类-工装类-工装-工装模具-维修'
                        )
                        category_id = '371-07'
                    elif any(
                        name in operator
                        for name in ['郑东', '罗怀磊', '黄俊', '田云', '刘丽']
                    ):
                        category_name = '信息化软硬件及工装设备类-设备类-设备工程服务-设备工程服务-设备维修类'
                        category_id = '321-01'
                    elif '龙东旭' in operator:
                        category_name = (
                            '信息化软硬件及工装设备类-设备类-设备采购-设备采购-杂项设备'
                        )
                        category_id = '301-10'
                    else:
                        category_name = '工程类类-工程基建类-工程施工-工程施工-工程维修'
                        category_id = '403-05'
                    cursor.execute(
                        'insert into 品类及合同文件(合同编号, 合同名称,合同金额,约定税率,对手方名称,品类编码,品类名称,合同文件名) values (?,?,?,?,?,?,?,?)',
                        (
                            contract_no,
                            conname,
                            conmoney,
                            taxrate,
                            provider,
                            category_id,
                            category_name,
                            new_path,
                        ),
                    )
                    conn.commit()
                    print(f'插入新合同记录: {contract_no}成功!')
            except Exception as e:
                print(f'处理文件失败: {new_path}, 错误: {e}')

        else:
            print(f'未找到合同号: {basename}\n')


def main():
    db_path = r'd:\user\PythonProject\ZConline\app.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    # 清空crawler.log日志
    log_path = os.path.join(os.path.dirname(__file__), 'crawler.log')
    with open(log_path, 'w', encoding='utf-8'):
        pass
    files = []
    for ext in EXTS:
        files.extend(glob(os.path.join(CONTRACT_DIR, f'*{ext}')))
    for file in files:
        new_path = (
            file.replace(' ', '')
            .replace(r'()', '')
            # .replace(r')', '')
            .replace(r'__', '_')
        ).strip()
        if file != new_path:
            if not os.path.exists(new_path):
                if os.path.exists(file):
                    os.rename(file, new_path)
                    print(f'重命名成功: {file} -> {new_path}')
                else:
                    print(f' {file}文件缺失，请检查！')
            else:
                move_to_recycle_bin(file)
            process_file(new_path, conn, cursor)
        else:
            # print(f' {file}无需重命名')
            process_file(file, conn, cursor)

    cursor.close()
    conn.close()


if __name__ == '__main__':
    main()
