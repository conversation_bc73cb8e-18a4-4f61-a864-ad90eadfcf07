# seleniumbase 改造版合同自动备案脚本
import time
import logging
import sqlite3
from datetime import datetime
from dateutil.relativedelta import relativedelta
from seleniumbase import BaseCase
from dataclasses import dataclass

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


@dataclass
class Config:
    CHROME_DRIVER_PATH: str = (
        r'C:\Program Files\Google\Chrome\Application\chromedriver.exe'
    )
    LOGIN_URL: str = 'https://sso.crrcgo.cc/login?v=1&client=cangqiong&returnUrl=https%3A%2F%2Fportal.crrcgo.cc%2F%2F%3F&isPortalMobile=false'
    USERNAME: str = '010800006291'
    PASSWORD: str = 'Z6h2en91@'
    MAX_RETRIES: int = 3
    WAIT_TIMEOUT: int = 20
    PAGE_LOAD_TIMEOUT: int = 30
    RETRY_INTERVAL: int = 2


class ContractCrawlerSBase(BaseCase):
    config: Config = Config()
    db_path: str = r'd:\user\PythonProject\ZConline\app.db'

    def setUp(self):
        super().setUp()
        self.driver.set_page_load_timeout(self.config.PAGE_LOAD_TIMEOUT)
        self.driver.maximize_window()

    def login(self):
        self.open(self.config.LOGIN_URL)
        self.type('input[name="username"]', self.config.USERNAME)
        self.type('input[name="password"]', self.config.PASSWORD)
        # 验证码处理略（可用OCR或人工输入）
        self.click('button.loginBtn')
        self.sleep(3)
        self.assert_element('span._1RGaSniK', timeout=10)
        logger.info('登录成功')

    def navigate_to_contracts(self):
        self.click('//*[@id="theme-side"]/div[1]/div[11]')
        self.sleep(1)
        self.click('//*[@id="theme-side"]/div[1]/div[11]/div/div[2]/a')
        self.sleep(5)
        self.switch_to_frame('iframe#tab-content-95130002')
        logger.info('已进入合同备案页面')

    def fill_contract_form(self, contract, provider):
        self.type('input#htno$text', contract.get('conid', ''))
        self.type('input#hetongname$text', contract.get('conname', ''))
        self.type('input#sellunitname$text', contract.get('provider', ''))
        self.type(
            'input#sellnsrsbh$text', provider.get('Tax_Identification_Number', '')
        )
        # 更多字段依次填写...
        logger.info(f'已填写合同表单: {contract.get("conid")}')

    def save_and_submit(self):
        self.click('button#btn_5b8e7420-ef55-41db-8ef6-b04ea5c1c8eb')
        self.sleep(2)
        if self.is_element_visible('.mini-messagebox'):
            self.click('.mini-messagebox-button.ok')
        logger.info('合同保存并提交完成')

    def run_contracts(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "SELECT * FROM contract WHERE strftime('%Y', registrationtime) = '2024'"
        )
        contract_rows = cursor.fetchall()
        contract_columns = [desc[0] for desc in cursor.description]
        for contract_row in contract_rows:
            contract = dict(zip(contract_columns, contract_row))
            provider_name = contract.get('provider')
            cursor.execute(
                'SELECT * FROM provider WHERE providername=?', (provider_name,)
            )
            provider_row = cursor.fetchone()
            if not provider_row:
                logger.warning(f'未找到供应商信息: {provider_name}')
                continue
            provider_columns = [desc[0] for desc in cursor.description]
            provider = dict(zip(provider_columns, provider_row))
            self.navigate_to_contracts()
            self.fill_contract_form(contract, provider)
            self.save_and_submit()
            logger.info(f'合同处理完成: {contract.get("conid")}')
        conn.close()

    def test_main(self):
        self.login()
        self.run_contracts()


if __name__ == '__main__':
    import pytest

    pytest.main([__file__, '-s'])
