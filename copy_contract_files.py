import os
import shutil
from glob import glob
import re

# 源目录
SOURCE_DIRS = [r'D:\user\合同汇总', r'D:\user\temp']
# 目标目录
TARGET_DIR = r'D:\user\合同备案'

# 支持的文件扩展名
EXTS = ['.doc', '.docx', '.pdf']

# 合同编号正则
PATTERNS = [
    re.compile(r'ZC\d{2}-A\d{3}'),
    re.compile(r'CRRC\d{2}[A-Z0-9]\d{3}[A-Z0-9]\d{7}'),
]


def get_contract_no(filename):
    """从文件名获取合同编号（第一个_前的部分）"""
    parts = os.path.basename(filename).split('_', 1)
    if len(parts) > 0:
        # 验证第一部分是否符合合同号格式
        for pat in PATTERNS:
            if pat.match(parts[0]):
                return parts[0]
    return None


def get_existing_contracts():
    """获取目标目录中已存在的合同号"""
    existing_contracts = set()
    for ext in EXTS:
        files = glob(os.path.join(TARGET_DIR, f'*{ext}'))
        for file in files:
            contract_no = get_contract_no(file)
            if contract_no:
                existing_contracts.add(contract_no)
    return existing_contracts


def main():
    # 确保目标目录存在
    os.makedirs(TARGET_DIR, exist_ok=True)

    # 获取已存在的合同号
    existing_contracts = get_existing_contracts()
    print(f'目标目录已有 {len(existing_contracts)} 个合同文件')

    # 统计
    copy_count = 0
    skip_count = 0
    error_count = 0

    # 遍历源目录
    for source_dir in SOURCE_DIRS:
        if not os.path.exists(source_dir):
            print(f'源目录不存在，跳过: {source_dir}')
            continue

        print(f'\n处理源目录: {source_dir}')

        # 获取所有支持的文件
        for ext in EXTS:
            files = glob(os.path.join(source_dir, f'**/*{ext}'), recursive=True)

            for file in files:
                contract_no = get_contract_no(file)
                basename = os.path.basename(file)

                if not contract_no:
                    print(f'未找到合同号，跳过: {basename}')
                    skip_count += 1
                    continue

                if contract_no in existing_contracts:
                    print(f'合同已存在，跳过: {basename}')
                    skip_count += 1
                    continue

                # 拷贝文件
                try:
                    target_path = os.path.join(TARGET_DIR, basename)
                    shutil.copy2(file, target_path)
                    print(f'已拷贝: {basename}')
                    copy_count += 1
                    existing_contracts.add(contract_no)  # 添加到已存在集合
                except Exception as e:
                    print(f'拷贝失败: {basename}, 错误: {e}')
                    error_count += 1

    # 打印统计结果
    print('\n完成！')
    print(f'成功拷贝: {copy_count} 个文件')
    print(f'跳过: {skip_count} 个文件')
    print(f'失败: {error_count} 个文件')


if __name__ == '__main__':
    main()
