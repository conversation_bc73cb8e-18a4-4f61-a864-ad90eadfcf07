<html class="win10 mini-window-lock-scroll" style="font-size: 100px;"><head>
    <title></title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <script src="../../frame/fui/js/cssboot.js"></script><style>.page-loading{position:fixed;z-index:1000;top:0;right:0;bottom:0;left:0;background-color:#fff}</style><style id="lazy-load-style" type="text/css"></style><link rel="stylesheet" href="/tpframe/frame/fui/mutableui/action/action.css?_=1671433232449">
<link rel="stylesheet" href="/tpframe/frame/fui/css/modicons.min.css?_=1671433232449">
<link id="common-skin" rel="stylesheet" name="fui-ui-style" data-name="pomegranatered" href="/tpframe/frame/fui/mutableui/skins/pomegranatered/skin.css?_=1671433232449">

<link rel="stylesheet" href="https://epp.crrcgo.cc/tpframe/uc/versionswitch/libs/layer/skin/default/layer.css?v=3.0.11110" id="layuicss-skinlayercss"><script src="/tpframe/frame/fui/js/widgets/numeral/numeral.js" type="text/javascript"></script><script src="/tpframe/frame/fui/js/widgets/laydate/laydate.js" type="text/javascript"></script><style id="fixed-hover-scroller-for-daterange">.laydate-time-list li ol {margin-right: 10px}.laydate-time-list>li:hover ol{margin-right:0;}.laydate-time-list li > p {padding-right: 10px}</style><link href="/tpframe/uc/ywscanfilelist/ywscanfilelist.css" rel="stylesheet" type="text/css" id="uc-ywscanfilelist-style"><script src="/tpframe/frame/fui/js/widgets/webuploader/webuploader.js" type="text/javascript"></script></head>
<body style="font-size: 14px;" class="mini-window-lock-scroll">

<div class="fui-toolbar" overflow-init="true" overflow-uid="5FD2F771-0A9D-406F-87C2-956005E8D1F1" style="position: relative;">
    <div class="btn-group mr10">
        <i class="fui-toolbar-over-trigger hidden r icon-rightdouble action-icon" style=""></i><div class="mini-output r" style="border-width: 0px;"><div style="display:none">&nbsp;</div></div>
        <div class="uc-handlecontrols l" id="handlecontrols" data-options="{type:0}" style="border-width: 0px;"><div id="handlecontrols_infoOperate" style="float:left">
	
	</div>

	<div id="handlecontrols_btnlock" style="float:left;display:none">
 		<a class="mini-button mini-corner-all mini-button-textOnly mini-button-ghost mini-button-state-default" href="javascript:void(0)"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">解除锁定</span></a>
	
	</div>

	<div id="handlecontrols_lockpart" style="float:left;"></div>
	

	<div id="handlecontrols_btnlst" class="btn-group" style="float:left;"><a class="mini-button mini-corner-all mini-button-textOnly mini-button-state-primary" href="javascript:void(0)" id="btn_5b8e7420-ef55-41db-8ef6-b04ea5c1c8eb" style="margin-left: 5px;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">保存修改</span></a><a class="mini-button mini-corner-all mini-button-textOnly mini-button-state-primary" href="javascript:void(0)" id="btn_4b5ebb68-e00f-405c-a8b9-3225ddcfb68d" style="margin-left: 5px;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">提报</span></a></div>

	<div id="handlecontrols_trackpart" style="float:right;padding-left:800px;display:none">
		&nbsp;	&nbsp;
		<a class="mini-button mini-corner-all mini-button-textOnly mini-button-ghost mini-button-state-default" href="javascript:void(0)"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">流程追踪</span></a>
		&nbsp;	&nbsp;
		当前处理步骤：<span class="text-special"></span>
	</div>
	
	<div class="mini-output" id="handlecontrols_errorpart" style="border-width: 0px; float: left; display: none; padding: 0px;"></div></div>
    </div>
    <div class="r mr10">
        <div class="mini-output" style="border-width: 0px;"><div style="display:none">&nbsp;</div><div title="流程追踪" class="toolbarImg lczz" onclick="epoint.openDialog('查看流程','frame/pages/epointworkflow/client/processoa9browser?processVersionInstanceGuid=4a69e45e-9a89-4506-ad12-5604d0380246');"></div>&nbsp;&nbsp;</div>
    </div>
<div class="toolbar-ext-area" style="top: 100%; display: none; width: 2px;"></div></div>
<div class="fui-content" style="height: 1136px;">
    <div class="mini-tabs mini-tabs-position-top" id="tabs" style="border-width: 0px; height: 100%;"><table class="mini-tabs-table mini-tabs-plain" cellspacing="0" cellpadding="0"><tbody><tr style="width:100%;"><td></td><td style="text-align:left;vertical-align:top;width:100%;"><div class="mini-tabs-scrollCt" style="padding-right: 0px; width: 1966px;"><div class="mini-tabs-nav" style="display: none;"><a class="mini-tabs-leftButton mini-icon mini-disabled" href="javascript:void(0)" hidefocus="" onclick="return false"></a><a class="mini-tabs-rightButton mini-icon mini-disabled" href="javascript:void(0)" hidefocus="" onclick="return false"></a></div><div class="mini-tabs-buttons" a="1" style="display: block;"></div><div class="mini-tabs-headers mini-tabs-header-top" style="width: auto; left: 0px; margin-left: 0px;"><table class="mini-tabs-header" cellspacing="0" cellpadding="0"><tbody><tr><td class="mini-tabs-space mini-tabs-firstSpace"><div></div></td><td title="" id="mini-5$1" index="0" class="mini-tab mini-corner-all mini-tab-active" style=""><span class="mini-tab-text">基本信息</span></td><td class="mini-tabs-space2"><div></div></td><td title="" id="mini-5$2" index="1" class="mini-tab mini-corner-all " style=""><span class="mini-tab-text">处理历史</span></td><td class="mini-tabs-space mini-tabs-lastSpace" style="width: 100%;"><div></div></td></tr></tbody></table></div></div><div class="mini-tabs-bodys mini-tabs-body-top" style="width: 1966px; height: 1079px;"><div id="mini-5$body$1" class="mini-tabs-body" style="height: 1063px;">
            <div class="fui-accordions">
                <div role="accordion" opened="true" class="fui-accordion opened">
                    <div role="head" title="买方信息 " class="fui-acc-hd"><span class="fui-acc-order">01</span><i class="fui-acc-toggle"></i><h4 class="fui-acc-title">买方信息 </h4></div>
                    <div role="body" class="fui-acc-bd">
                        <div class="fui-form">
                            <div class="form-inner">
                                <div class="form-row">
                                    <a class="mini-button mini-corner-all mini-button-textOnly mini-button-ghost mini-button-state-default" href="javascript:void(0)"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">挑选公司</span></a>
                                    <a class="mini-button mini-corner-all mini-button-textOnly mini-button-ghost mini-button-state-default" href="javascript:void(0)" style="display: none;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">挑选供应商</span></a>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">合同买方名称：</label><div label="合同买方名称" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required mini-textbox-readOnly" id="buyunitname" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="buyunitname$text" readonly=""><span class="mini-maxlength-info" style="display:none;">10/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                    <label class="form-label required">合同买方统一社会信用代码：</label><div label="合同买方统一社会信用代码" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required" id="buynsrsbh" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="buynsrsbh$text"><span class="mini-maxlength-info" style="display:none;">18/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div role="accordion" opened="true" class="fui-accordion opened">
                    <div role="head" title="卖方信息 " class="fui-acc-hd"><span class="fui-acc-order">02</span><i class="fui-acc-toggle"></i><h4 class="fui-acc-title">卖方信息 </h4></div>
                    <div role="body" class="fui-acc-bd">
                        <div class="fui-form">
                            <div class="form-inner">
                                <!--<div role="row">
                                    <div class="mini-button" onclick="selectsellCompany">挑选公司</div>
                                    <div class="mini-button" onclick="selectsellGys">挑选供应商</div>
                                </div>-->
                                <div class="form-row">
                                    <label class="form-label required">合同卖方类别：</label><div label="合同卖方类别" starred="true" class="form-control span2">
                                        <div class="mini-radiobuttonlist mini-required" id="selljnwtype" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-12$0" index="0" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="JNQY" id="mini-12$ck$0" type="radio"><label for="mini-12$ck$0" onclick="return false;">境内企业</label></div><div id="mini-12$1" index="1" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="JWQY" id="mini-12$ck$1" type="radio"><label for="mini-12$ck$1" onclick="return false;">境外企业</label></div><div id="mini-12$2" index="2" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="GR" id="mini-12$ck$2" type="radio"><label for="mini-12$ck$2" onclick="return false;">个人</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="selljnwtype$value" value="JNQY"></td></tr></tbody></table></div>
                                    </div>
                                    <label class="form-label required">合同卖方性质：</label><div label="合同卖方性质" starred="true" class="form-control span2">
                                        <div class="mini-radiobuttonlist mini-required" id="selldanweitype" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-13$0" index="0" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="0" id="mini-13$ck$0" type="radio"><label for="mini-13$ck$0" onclick="return false;">内部单位</label></div><div id="mini-13$1" index="1" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="1" id="mini-13$ck$1" type="radio"><label for="mini-13$ck$1" onclick="return false;">外部单位</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="selldanweitype$value" value="1"></td></tr></tbody></table></div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">合同卖方名称：</label><div label="合同卖方名称" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required" id="sellunitname" style="border-width: 0px; width: 70%; padding: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="sellunitname$text"><span class="mini-maxlength-info" style="display:none;">15/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                        <a class="mini-button mini-corner-all mini-button-textOnly mini-button-ghost mini-button-state-default" href="javascript:void(0)" style="margin-left: 2%;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">挑选</span></a>

                                    </div>
                                    <label class="form-label required">合同卖方统一社会信用代码：</label><div label="合同卖方统一社会信用代码" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required" id="sellnsrsbh" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="sellnsrsbh$text"><span class="mini-maxlength-info" style="display:none;">4/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div role="accordion" opened="true" class="fui-accordion opened">
                    <div role="head" title="合同信息 " class="fui-acc-hd"><span class="fui-acc-order">03</span><i class="fui-acc-toggle"></i><h4 class="fui-acc-title">合同信息 </h4></div>
                    <div role="body" class="fui-acc-bd">
                        <div class="fui-form">
                            <div class="form-inner">
                                <div class="form-row">
                                    <label class="form-label">历史合同信息挑选：</label><div label="历史合同信息挑选" class="form-control span5">
                                        <a class="mini-button mini-corner-all mini-button-textOnly mini-button-ghost mini-button-state-default" href="javascript:void(0)" style="margin-left: 2%;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">
                                            挑选
                                        </span></a>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">合同编号：</label><div label="合同编号" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required" id="htno" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="htno$text"><span class="mini-maxlength-info" style="display:none;">9/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                    <label class="form-label required">合同名称：</label><div label="合同名称" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required" id="hetongname" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="hetongname$text"><span class="mini-maxlength-info" style="display:none;">11/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label">合同类型：</label><div label="合同类型" class="form-control span2">
                                        <span class="mini-outputtext" id="htqstype" data-options="{code:'合同登记-合同类型'}" title="采购合同" style="border-width: 0px;">采购合同</span>
                                    </div>
                                    <label class="form-label">合同来源：</label><div label="合同来源" class="form-control span2">
                                        <span class="mini-outputtext" id="sourcetype" title="电子采购新增" style="border-width: 0px;">电子采购新增</span>
                                    </div>
                                </div>
                                <!-- 司库以及财务共享字段-->
                                <div id="hiderow1" class="form-row" style="display: none;">
                                    <label class="form-label">是否推送财务共享：</label><div label="是否推送财务共享" class="form-control span2">
                                        <div class="mini-radiobuttonlist" id="ispushcwgx" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-25$0" index="0" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="1" id="mini-25$ck$0" type="radio"><label for="mini-25$ck$0" onclick="return false;">是</label></div><div id="mini-25$1" index="1" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="0" id="mini-25$ck$1" type="radio"><label for="mini-25$ck$1" onclick="return false;">否</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="ispushcwgx$value" value="1"></td></tr></tbody></table></div>
                                    </div>
                                    <label class="form-label required">是否推送司库：</label><div label="是否推送司库" starred="true" class="form-control span2">
                                        <div class="mini-radiobuttonlist mini-readonly mini-required" id="ispushsiku" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-26$0" index="0" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="1" id="mini-26$ck$0" type="radio"><label for="mini-26$ck$0" onclick="return false;">是</label></div><div id="mini-26$1" index="1" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="0" id="mini-26$ck$1" type="radio"><label for="mini-26$ck$1" onclick="return false;">否</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="ispushsiku$value" value="1"></td></tr></tbody></table></div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">框架合同：</label><div label="框架合同" starred="true" class="form-control span2">
                                        <div class="mini-radiobuttonlist" id="iskjxy" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-27$0" index="0" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="1" id="mini-27$ck$0" type="radio"><label for="mini-27$ck$0" onclick="return false;">是</label></div><div id="mini-27$1" index="1" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="2" id="mini-27$ck$1" type="radio"><label for="mini-27$ck$1" onclick="return false;">否</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="iskjxy$value" value="2"></td></tr></tbody></table></div>
                                    </div>
                                    <label class="form-label required">是否需要审批后上传合同附件：</label><div label="是否需要审批后上传合同附件" starred="true" class="form-control span2">
                                        <div class="mini-radiobuttonlist mini-required" id="is_sp_upload_file" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-28$0" index="0" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="1" id="mini-28$ck$0" type="radio"><label for="mini-28$ck$0" onclick="return false;">是</label></div><div id="mini-28$1" index="1" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="0" id="mini-28$ck$1" type="radio"><label for="mini-28$ck$1" onclick="return false;">否</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="is_sp_upload_file$value" value="0"></td></tr></tbody></table></div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">合同约定有效期：</label><div label="合同约定有效期" starred="true" class="form-control span1">
                                        <span class="mini-buttonedit mini-daterangepicker mini-datepicker mini-required" range-type="date" id="daterangepicker" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" id="daterangepicker$text" lay-key="1"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="daterangepicker$value"></span>
                                    </div>
                                    <label class="form-label required">合同签订时间：</label><div label="合同签订时间" starred="true" class="form-control span1">
                                        <span class="mini-buttonedit mini-datepicker mini-required" id="htqstime" style="border-width: 0px; width: 220px; padding: 0px;"><span class="mini-buttonedit-border mini-corner-all"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" id="htqstime$text" lay-key="2"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="htqstime$value"></span>
                                    </div>
                                    <label class="form-label required">合同结算类型：</label><div label="合同结算类型" starred="true" class="form-control span1">
                                        <span class="mini-buttonedit mini-combobox mini-popupedit mini-required" id="billingtype" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all" style="padding-right: 30px;"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly="" id="billingtype$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close" title="" style="display: none;"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');" style="display: inline-block;"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="billingtype$value" value="02"></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">币种：</label><div label="币种" starred="true" class="form-control span2">
                                        <span class="mini-buttonedit mini-combobox mini-popupedit mini-required" id="jinecurrency" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all" style="padding-right: 30px;"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly="" id="jinecurrency$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close" title="" style="display: none;"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');" style="display: inline-block;"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="jinecurrency$value" value="CNY"></span>
                                    </div>
                                    <label class="form-label required">合同金额（不含税元）：</label><div label="合同金额（不含税元）" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required" id="notaxhtjine" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="notaxhtjine$text"><span class="mini-maxlength-info" style="display:none;">11/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">合同金额（含税元）：</label><div label="合同金额（含税元）" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required" id="htjine" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="htjine$text"><span class="mini-maxlength-info" style="display:none;">4/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                    <label class="form-label required">税率（%）：</label><div label="税率（%）" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required" id="taxrate" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="taxrate$text"><span class="mini-maxlength-info" style="display:none;">4/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">履约地点：</label><div label="履约地点" starred="true" class="form-control span2">
                                        <span class="mini-textbox mini-required" id="performanceaddress" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="performanceaddress$text"><span class="mini-maxlength-info" style="display:none;">6/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                    <label class="form-label"></label><div label="" class="form-control span2">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">质保金：</label><div label="质保金" starred="true" class="form-control span2">
                                        <div class="mini-radiobuttonlist mini-required" id="zhibaojin" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-46$0" index="0" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="0" id="mini-46$ck$0" type="radio"><label for="mini-46$ck$0" onclick="return false;">无</label></div><div id="mini-46$1" index="1" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="1" id="mini-46$ck$1" type="radio"><label for="mini-46$ck$1" onclick="return false;">有</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="zhibaojin$value" value="1"></td></tr></tbody></table></div>
                                    </div>
                                    <label class="form-label required">质保期（月）：</label><div label="质保期（月）" starred="true" id="zhibaoqiDiv" class="form-control span2">
                                        <span class="mini-buttonedit mini-spinner mini-required" id="zhibaoqi" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" id="zhibaoqi$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close"></span><span name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');"><span class="mini-buttonedit-up"><span class="mini-icon"></span></span><span class="mini-buttonedit-down"><span class="mini-icon"></span></span></span></span></span><input name="" type="hidden" id="zhibaoqi$value" value="12"></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">合同变更：</label><div label="合同变更" starred="true" class="form-control span2">
                                        <div class="mini-radiobuttonlist mini-required" id="isbg" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-49$0" index="0" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="1" id="mini-49$ck$0" type="radio"><label for="mini-49$ck$0" onclick="return false;">是</label></div><div id="mini-49$1" index="1" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="0" id="mini-49$ck$1" type="radio"><label for="mini-49$ck$1" onclick="return false;">否</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="isbg$value" value="0"></td></tr></tbody></table></div>
                                    </div>
                                    <label class="form-label required">补充协议：</label><div label="补充协议" starred="true" class="form-control span2">
                                        <div class="mini-radiobuttonlist mini-required" id="buchongxieyi" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-51$0" index="0" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="1" id="mini-51$ck$0" type="radio"><label for="mini-51$ck$0" onclick="return false;">是</label></div><div id="mini-51$1" index="1" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="0" id="mini-51$ck$1" type="radio"><label for="mini-51$ck$1" onclick="return false;">否</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="buchongxieyi$value" value="0"></td></tr></tbody></table></div>
                                    </div>
                                </div>
                                <div id="zhibaojindiv" class="form-row">
                                    <label class="form-label required">质保金比例(%)：</label><div label="质保金比例(%)" class="form-control span2">
                                        <span class="mini-textbox" id="zhibaojinratio" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="zhibaojinratio$text"><span class="mini-maxlength-info" style="display:none;">1/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                    <label class="form-label required">质保条款：</label><div label="质保条款" class="form-control span2">
                                        <span class="mini-textbox" id="zhibaotiaokuan" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="zhibaotiaokuan$text"><span class="mini-maxlength-info" style="display:none;">9/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">结算条件：</label><div label="结算条件" starred="true" class="form-control span5">
                                        <span class="mini-textbox mini-textarea mini-required" id="jiesuanconditions" style="border-width: 0px; width: 100%; padding: 0px;"><span class="mini-textbox-border mini-corner-all"><textarea class="mini-textbox-input" autocomplete="off" placeholder="" id="jiesuanconditions$text" style="height: 60px;"></textarea><span class="mini-maxlength-info" style="display:none;">7/10000000</span></span><input type="hidden"></span>
                                    </div>
                                </div>

                                <!--增加推送字段-->
                                <div class="form-row">
                                    <label class="form-label">合同实际所属部门：</label><div label="合同实际所属部门" class="form-control span2">
                                        <span class="mini-buttonedit mini-treeselect mini-popupedit" id="hetongbelongtoouguid" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly="" id="hetongbelongtoouguid$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="hetongbelongtoouguid$value" value=""></span>
                                    </div>
                                    <div class="form-control span2"></div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div role="accordion" opened="true" class="fui-accordion opened">
                    <div role="head" title="采购相关信息 " class="fui-acc-hd"><span class="fui-acc-order">04</span><i class="fui-acc-toggle"></i><h4 class="fui-acc-title">采购相关信息 </h4></div>
                    <div role="body" class="fui-acc-bd">
                        <div class="fui-form">
                            <div class="form-inner">
                                <div class="form-row">
                                    <label class="form-label required">采购方式：</label><div label="采购方式" starred="true" class="form-control span2">
                                        <span class="mini-buttonedit mini-combobox mini-popupedit mini-required" id="sourcingcgfs" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all" style="padding-right: 30px;"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly="" id="sourcingcgfs$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close" title="" style="display: none;"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');" style="display: inline-block;"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="sourcingcgfs$value" value="4"></span>
                                    </div>
                                    <label class="form-label required">采购方式类别：</label><div label="采购方式类别" starred="true" class="form-control span2">
                                        <div class="mini-radiobuttonlist mini-required" id="sourcingcgxs" style="border-width: 0px;"><table cellpadding="0" border="0" cellspacing="0" style="display:table;"><tbody><tr><td><div class="mini-list-inner"><div id="mini-70$0" index="0" class="mini-radiobuttonlist-item" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="1" id="mini-70$ck$0" type="radio"><label for="mini-70$ck$0" onclick="return false;">公开采购</label></div><div id="mini-70$1" index="1" class="mini-radiobuttonlist-item mini-radiobuttonlist-item-selected" style=""><span tabindex="0" class="mini-list-icon mini-icon"></span><input style="display:none;" onmousedown="this._checked = this.checked;" onclick="this.checked = this._checked" value="2" id="mini-70$ck$1" type="radio"><label for="mini-70$ck$1" onclick="return false;">邀请采购</label></div></div><div class="mini-errorIcon"></div><input type="hidden" id="sourcingcgxs$value" value="2"></td></tr></tbody></table></div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">采购平台名称：</label><div label="采购平台名称" starred="true" class="form-control span5">
                                        <span class="mini-buttonedit mini-combobox mini-popupedit mini-required" id="dzcgplatform" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all" style="padding-right: 30px;"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly="" id="dzcgplatform$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close" title="" style="display: none;"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');" style="display: inline-block;"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="dzcgplatform$value" value="1"></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">采购对象分类：</label><div label="采购对象分类" starred="true" class="form-control span5">
                                        <span class="mini-buttonedit mini-combobox mini-popupedit mini-required" id="caigoufenlei" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all" style="padding-right: 30px;"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly="" id="caigoufenlei$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close" title="" style="display: none;"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');" style="display: inline-block;"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="caigoufenlei$value" value="2"></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">独家采购属性：</label><div label="独家采购属性" starred="true" class="form-control span2">
                                        <span class="mini-buttonedit mini-combobox mini-popupedit mini-required" id="dujiacaigou" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all" style="padding-right: 30px;"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly="" id="dujiacaigou$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close" title="" style="display: none;"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');" style="display: inline-block;"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="dujiacaigou$value" value="0"></span>
                                    </div>
                                    <label class="form-label required">代理采购属性：</label><div label="代理采购属性" starred="true" class="form-control span2">
                                        <span class="mini-buttonedit mini-combobox mini-popupedit mini-required" id="dailicaigou" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all" style="padding-right: 30px;"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly="" id="dailicaigou$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close" title="" style="display: none;"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');" style="display: inline-block;"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="dailicaigou$value" value="0"></span>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label class="form-label required">集采属性：</label><div label="集采属性" starred="true" class="form-control span2">
                                        <span class="mini-buttonedit mini-combobox mini-popupedit mini-required" id="centralizecgtype" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all" style="padding-right: 30px;"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly="" id="centralizecgtype$text"><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close" title="" style="display: none;"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');" style="display: inline-block;"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span></span><input name="" type="hidden" id="centralizecgtype$value" value="4"></span>
                                    </div>
                                    <label class="form-label"></label><div label="" class="form-control span2">
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div role="accordion" opened="true" class="fui-accordion opened">
                    <div role="head" title="物料信息 " class="fui-acc-hd"><span class="fui-acc-order">05</span><i class="fui-acc-toggle"></i><h4 class="fui-acc-title">物料信息 </h4></div>
                    <div role="body" class="fui-acc-bd">
                        <div class="btn-group mr10">
                            <a class="mini-button mini-corner-all mini-button-textOnly mini-button-state-primary" href="javascript:void(0)" style="margin-left: 5px;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">新增物料</span></a>
                            <a class="mini-button mini-corner-all mini-button-textOnly mini-button-ghost mini-button-state-default" href="javascript:void(0)"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">删除物料</span></a>
                        </div>
                        <div class="mini-panel mini-grid mini-datagrid mini-grid-showHGridLines" tabindex="0" id="datagrid1" style="border-width: 0px; width: 100%; display: block; padding: 0px;"><div class="mini-panel-border mini-grid-border"><div class="mini-panel-header" style="display: none;"><div class="mini-panel-header-inner"><span class="mini-panel-icon mini-icon mini-iconfont " style="display: none;"></span><div class="mini-panel-title" title="&amp;nbsp">&nbsp;</div><div class="mini-tools"><span id="0" class="mini-icon mini-iconfont fa mini-tools-collapse " style=";display:none;"></span><span id="1" class="mini-icon mini-iconfont fa mini-tools-close " style=";display:none;"></span></div></div></div><div class="mini-panel-viewport mini-grid-viewport" style="height: auto;"><div class="mini-panel-toolbar" style="display: none;"></div><div class="mini-grid-columns" style="display: block;"><div class="mini-grid-columns-lock" style="width: 0px; left: -10px; height: auto;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table><div class="mini-grid-topRightCell"></div></div><div class="mini-grid-columns-view" style="margin-left: 0px; margin-right: 0px; width: auto; height: auto; padding-right: 0px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td id="2" style="padding:0;border:0;margin:0;height:0px;width:40px;width:0.4rem"></td><td id="3" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="4" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="5" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="6" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="7" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="8" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="9" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="10" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="11" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="12" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td><td id="mini-93$headerCell2$2" class="mini-grid-headerCell  mini-checkcolumn  mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title=""><span class="mini-grid-headertxt is-existed-sorticon"><span class="mini-grid-checkbox mini-icon" id="mini-93checkall"></span></span></div><div id="2" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$3" class="mini-grid-headerCell    mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="物料编码"><span class="mini-grid-headertxt is-existed-sorticon">物料编码</span></div><div id="3" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$4" class="mini-grid-headerCell    mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="物料描述"><span class="mini-grid-headertxt is-existed-sorticon">物料描述</span></div><div id="4" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$5" class="mini-grid-headerCell    mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="计量单位"><span class="mini-grid-headertxt is-existed-sorticon">计量单位</span></div><div id="5" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$6" class="mini-grid-headerCell    mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="物料数量"><span class="mini-grid-headertxt is-existed-sorticon">物料数量</span></div><div id="6" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$7" class="mini-grid-headerCell    mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="单价（含税元）"><span class="mini-grid-headertxt is-existed-sorticon">单价（含税元）
                                </span></div><div id="7" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$8" class="mini-grid-headerCell    mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="所属品类代码"><span class="mini-grid-headertxt is-existed-sorticon">所属品类代码</span></div><div id="8" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$9" class="mini-grid-headerCell    mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="所属品类"><span class="mini-grid-headertxt is-existed-sorticon">所属品类</span></div><div id="9" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$10" class="mini-grid-headerCell    mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="产品平台"><span class="mini-grid-headertxt is-existed-sorticon">产品平台</span></div><div id="10" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$11" class="mini-grid-headerCell    mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="业务类型"><span class="mini-grid-headertxt is-existed-sorticon">业务类型</span></div><div id="11" class="mini-grid-column-splitter"></div></div></td><td id="mini-93$headerCell2$12" class="mini-grid-headerCell    mini-grid-bottomCell  mini-grid-rightCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="原产地"><span class="mini-grid-headertxt is-existed-sorticon">原产地</span></div><div id="12" class="mini-grid-column-splitter"></div></div></td></tr></tbody></table><div class="mini-grid-topRightCell"></div></div><div class="mini-grid-columns-lock" style="width: 0px; left: auto; right: -10px; height: auto;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table><div class="mini-grid-topRightCell"></div></div><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-filterRow" style="display: none;"><div class="mini-grid-filterRow-lock" style="height: 100%; width: 0px; left: -10px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="position:absolute;top:0;left:0;height:100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-filterRow-view" style="margin-left: 0px; margin-right: 0px; width: auto; padding-right: 0px; height: 0px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="position: absolute; top: 0px; left: 0px; height: 100%; width: 1768px;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td id="2" style="padding:0;border:0;margin:0;height:0px;width:40px;width:0.4rem"></td><td id="3" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="4" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="5" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="6" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="7" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="8" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="9" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="10" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="11" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="12" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td><td id="mini-93$filter$2" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$3" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$4" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$5" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$6" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$7" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$8" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$9" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$10" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$11" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-93$filter$12" class="mini-grid-filterCell" style="">&nbsp;</td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-filterRow-lock" style="height: 100%; width: 0px; left: auto; right: -10px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="position:absolute;top:0;left:0;height:100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-panel-body mini-grid-rows" style="height: auto;"><div class="mini-grid-rows-lock" style="width: 0px; left: -10px;"><div class="mini-grid-rows-content"><table class="mini-grid-table mini-grid-rowstable" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:1px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table></div></div><div class="mini-grid-rows-view" style="overflow: auto; margin-left: 0px; margin-right: 0px; width: auto;"><div class="mini-grid-rows-content"><table class="mini-grid-table mini-grid-rowstable" cellspacing="0" cellpadding="0" border="0" style="width: 100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td id="2" style="padding:0;border:0;margin:0;height:0px;width:40px;width:0.4rem"></td><td id="3" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="4" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="5" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="6" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="7" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="8" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="9" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="10" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="11" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="12" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td style="width:0px;"></td></tr><tr id="mini-93$emptytext2" style="display:none;"><td style="width:0"></td><td class="mini-grid-emptyText" colspan="11">暂无数据</td></tr><tr class="mini-grid-row  " style="height: 52px;" id="mini-93$row2$68"><td style="width:0;"></td><td id="68$cell$2" class="mini-grid-cell  mini-checkcolumn " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title=""><span class="mini-grid-checkbox mini-icon" id="mini-93$checkcolumn$68$2"></span></div></td><td id="68$cell$3" class="mini-grid-cell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="--">--</div></td><td id="68$cell$4" class="mini-grid-cell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="--">--</div></td><td id="68$cell$5" class="mini-grid-cell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="ST,项目">ST,项目</div></td><td id="68$cell$6" class="mini-grid-cell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="1">1</div></td><td id="68$cell$7" class="mini-grid-cell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="7998.00">7998.00</div></td><td id="68$cell$8" class="mini-grid-cell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="321-01-006">321-01-006</div></td><td id="68$cell$9" class="mini-grid-cell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="信息化软硬件及工装设备类-设备类设备工程服务-设备维修类-起重运输设备类">信息化软硬件及工装设备类-设备类设备工程服务-设备维修类-起重运输设备类</div></td><td id="68$cell$10" class="mini-grid-cell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="铁路货车">铁路货车</div></td><td id="68$cell$11" class="mini-grid-cell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="新造">新造</div></td><td id="68$cell$12" class="mini-grid-cell  mini-grid-rightCell " style="border-right:0;"><div class="mini-grid-cell-inner  mini-grid-cell-nowrap " style="" title="四川省眉山市">四川省眉山市</div></td></tr></tbody></table></div></div><div class="mini-grid-rows-lock" style="overflow: hidden auto; width: 0px; left: auto; right: -10px;"><div class="mini-grid-rows-content"><table class="mini-grid-table mini-grid-rowstable" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:1px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table></div></div><div class="mini-grid-vscroll" style="display: none;"><div class="mini-grid-vscroll-content"></div></div></div><div class="mini-grid-summaryRow" style="display: none;"><div class="mini-grid-summaryRow-lock" style="width: 0px; left: -10px; height: auto;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-summaryRow-view" style="margin-left: 0px; margin-right: 0px; width: auto; height: auto; padding-right: 0px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td id="2" style="padding:0;border:0;margin:0;height:0px;width:40px;width:0.4rem"></td><td id="3" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="4" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="5" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="6" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="7" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="8" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="9" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="10" style="padding:0;border:0;margin:0;height:0px;width:80px;width:0.8rem"></td><td id="11" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="12" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td><td id="mini-93$summary$2_0" class="mini-grid-summaryCell mini-checkcolumn" style=";">&nbsp;</td><td id="mini-93$summary$3_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-93$summary$4_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-93$summary$5_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-93$summary$6_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-93$summary$7_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-93$summary$8_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-93$summary$9_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-93$summary$10_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-93$summary$11_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-93$summary$12_0" class="mini-grid-summaryCell " style=";">&nbsp;</td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-summaryRow-lock" style="width: 0px; left: auto; right: -10px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-pager" style="display: none;"><div class="mini-pagination" id="mini-95" style="border-width: 0px;"><div class="pagination-pagerinfo">共1条</div> <div class="pagination-buttons"> <a class="pagination-button pagination-prev mini-icon disabled" action="prev"></a> <a class="pagination-button pagination-current">1</a> <a class="pagination-button pagination-next  mini-icon disabled" action="next"></a> </div><span class="mini-buttonedit mini-combobox mini-popupedit" id="mini-150" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly=""><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span><span class="pagination-sizeinfo">条/页</span></span><input name="" type="hidden" value="10"></span><span class="pagination-pagerchange">跳至<input class="pagination-number" value="1">页</span></div></div><div class="mini-panel-footer" style="display: none;"></div><div class="mini-resizer-trigger" style="display: none;"></div></div><a href="#" class="mini-grid-focus" style="position:absolute;left:0px;top:0px;width:0px;height:0px;outline:none;" hidefocus="" onclick="return false"></a></div></div>
                    </div>
                </div>
                <div role="accordion" opened="true" class="fui-accordion opened">
                    <div role="head" title="附件信息" class="fui-acc-hd"><span class="fui-acc-order">06</span><i class="fui-acc-toggle"></i><h4 class="fui-acc-title">附件信息</h4></div>
                    <div role="body" class="fui-acc-bd">
                        <div class="form-wrap">
                            <div class="form-content">
                                <div class="uc-ywscanfilelist" id="ywscanfilelist1" data-options="" style="border-width: 0px;"><div class="mini-webuploader mini-uploader-ui-oa" id="ywscanfilelist1-inner" style="border-width: 0px;"><div class="mini-uploader-tips"></div><div class="mini-uploader-list"><div class="file-wrap"><div class="file-head clearfix"><table class="file-head-tb"><tbody><tr><td class="mini-grid-headerCell mini-grid-bottomCell" style="width: 25%;"><div class="mini-grid-headerCell-outer"><div title="" class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap ">电子件名称</div></div></td> <td class="mini-grid-headerCell mini-grid-bottomCell"><div class="mini-grid-headerCell-outer"><div title="" class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap ">电子件列表</div></div></td><td class="mini-grid-headerCell mini-grid-bottomCell" style="width: 122px"><div class="mini-grid-headerCell-outer"><div title="" class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap ">电子件管理</div></div></td><td class="mini-grid-headerCell mini-grid-bottomCell" style="width: 174px"><div class="mini-grid-headerCell-outer"><div title="" class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap ">备注</div></div></td><td class="mini-grid-headerCell mini-grid-bottomCell" style="width: 100px"><div class="mini-grid-headerCell-outer"><div title="" class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap ">模板下载</div></div></td></tr></tbody></table></div><div class="file-body"><table class="file-list" id="file-list"><tr class="mini-grid-row  "><td class="mini-grid-cell" style="width: 25%; padding-left:0px; border-right-color: currentColor; border-bottom-color: currentColor; border-right-width: 0px; border-bottom-width: 0px; border-right-style: none; border-bottom-style: none;"><div class="mini-grid-cell-inner"><span class="action-icon help" data-tooltip="采购合同制作电子件"></span> 采购合同制作电子件</div></td><td class="file-list-cell list"><div class="file-list-container"><ul class="file-lists" style="margin-top:5px;margin-bottom:5px;" action="attachactyewuaction.getFileUploadModel?ClientGuid=3174a84f-f734-47d6-8cad-f072efa63758" localattachaction="attachactyewuaction"><li class="item clearfix" id="ywscanfilelist1" isusewebztb="false" fileserverid="" filecode="HT003"><span class="tipt" style="color:#999999">无电子件</span></li><li></li></ul></div></td><td class="file-list-cell btn" style="width:135px"> <div class="mini-webuploader mini-uploader-ui-oa" id="rowuploader_HT003" style="border-width: 0px;"><div class="mini-uploader-btns show-icon" style="visibility: visible;"><div class="mini-uploader-btn-upload"><label class="mini-btn-pick webuploader-container"><div class="webuploader-pick"><i class="mini-button-icon icon-upload"></i><span class="mini-btn-pick-text">上传</span></div><div id="rt_rt_1islo7ddtl9f1g0t1v9517di1rqp4" style="position: absolute; inset: 0px auto auto 0px; width: 80px; height: 30px; overflow: hidden;"><input type="file" capture="camera" name="file" class="webuploader-element-invisible" multiple="multiple" accept=""><label style="opacity: 0; width: 100%; height: 100%; display: block; cursor: pointer; background: rgb(255, 255, 255);"></label></div></label><a class="mini-button mini-corner-all mini-button-state-default mini-button-textOnly" href="javascript:void(0)" id="mini-157" style="display: none;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">开始上传</span></a></div><div class="mini-uploader-btn-download" style="display: none;"><a class="mini-button mini-corner-all mini-button-textOnly mini-uploader-batch-download mini-button-ghost mini-button-state-default" href="javascript:void(0)" id="mini-158"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">下载全部</span></a></div></div><div class="mini-uploader-tips"></div><div class="mini-uploader-list"></div></div></td> <td class="file-list-cell btn" style="width:174px;padding-right:12px;padding-left:12px;"><div label="采购合同制作电子件" style="width:100%"><span class="mini-textbox" id="remark_HT003" title="" style="border-width: 0px; width: 100%; padding: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="remark_HT003$text" name="remark" maxlength="500" style="width: calc(100% - 0.412812rem);"><span class="mini-maxlength-info" style="">0/500</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span></div></td><td class="file-list-cell btn" style="width:100px"> <span>暂无模板</span></td></tr><tr class="mini-grid-row  "><td class="mini-grid-cell" style="width: 25%; padding-left:0px; border-right-color: currentColor; border-bottom-color: currentColor; border-right-width: 0px; border-bottom-width: 0px; border-right-style: none; border-bottom-style: none;"><div class="mini-grid-cell-inner"><span class="action-icon help" data-tooltip="合同"></span> 合同</div></td><td class="file-list-cell list"><div class="file-list-container"><ul class="file-lists" style="margin-top:5px;margin-bottom:5px;" action="attachactyewuaction.getFileUploadModel?ClientGuid=3174a84f-f734-47d6-8cad-f072efa63758" localattachaction="attachactyewuaction"><li class="item clearfix" id="39e0067c-7f7d-412f-bdfb-ec972e6d84c9" isusewebztb="false" fileserverid="70e21480-38bd-4317-907d-45e92995432e" filecode="HeTongCompany_001"><div><a onclick="javascript:epoint.openTopDialog('合同','huiyuaninfomis2/pages/attachmanage/file_viewer?ShowType=&amp;IsNeedQZ=1&amp;isyewu=true&amp;FileGuid=70e21480-38bd-4317-907d-45e92995432e&amp;TaskCode=JSGC_ContractProductionCompany&amp;ClientGuid=3174a84f-f734-47d6-8cad-f072efa63758&amp;FileCode=HeTongCompany_001&amp;PageIndex=0&amp;AttachGuid=39e0067c-7f7d-412f-bdfb-ec972e6d84c9&amp;AttachConName=F20230816&amp;status=&amp;IsNeedSH=false&amp;BiaoDuanGuid=null&amp;pviguid=4a69e45e-9a89-4506-ad12-5604d0380246&amp;activityguid=66ab709d-e9fd-486f-9a02-d37be31b8a86&amp;FileCode2Show=null',refreshFileList)" style="cursor:pointer;color:blue;"><div class="file-name" title="射芯机取料用葫芦吊更换.pdf">射芯机取料用葫芦吊更...&nbsp;&nbsp;<span style="color:#999999">[未签章]</span></div></a></div><div class="info clearfix">  <a href="https://epp.crrcgo.cc/tpframe/rest/downattachaction/download?AttachGuid=39e0067c-7f7d-412f-bdfb-ec972e6d84c9&amp;FileCode=HeTongCompany_001&amp;ClientGuid=3174a84f-f734-47d6-8cad-f072efa63758&amp;BiaoDuanGuid=null&amp;isCommondto=true&amp;timestamp=1748781742122" style="color:blue;" class="liItemA action-icon icon-download">点击下载</a> <div class="action-wrap " style="float:right;"><span class="btn delete" style="color:red">删除</span></div></div></li><li><div><div class="file-size div-inline" style="margin-left:10px;color:#999999;display:inline;">1171 KB</div><div class="date div-inline" style="margin-left:5px;color:#999999;display:inline;">2025-06-01</div><div class="file-from div-inline" style="cursor:pointer;margin-left:5px;color:#999999;display:inline;" title="郑东">郑东</div><a class="liItemA action-icon icon-download" style="margin-left:15px;" href="https://epp.crrcgo.cc/tpframe/rest/downattachaction/download?AttachGuid=39e0067c-7f7d-412f-bdfb-ec972e6d84c9&amp;FileCode=HeTongCompany_001&amp;ClientGuid=3174a84f-f734-47d6-8cad-f072efa63758&amp;BiaoDuanGuid=null&amp;isCommondto=true&amp;timestamp=1748781742122" title="点击下载 " target="_blank"></a></div></li></ul></div></td><td class="file-list-cell btn" style="width:135px"> <div class="mini-webuploader mini-uploader-ui-oa" id="rowuploader_HeTongCompany_001" style="border-width: 0px;"><div class="mini-uploader-btns show-icon" style="visibility: visible;"><div class="mini-uploader-btn-upload"><label class="mini-btn-pick webuploader-container"><div class="webuploader-pick"><i class="mini-button-icon icon-upload"></i><span class="mini-btn-pick-text">上传</span></div><div id="rt_rt_1islo7de01dlm11c31am1o1m1rag7" style="position: absolute; inset: 0px auto auto 0px; width: 80px; height: 30px; overflow: hidden;"><input type="file" capture="camera" name="file" class="webuploader-element-invisible" multiple="multiple" accept=""><label style="opacity: 0; width: 100%; height: 100%; display: block; cursor: pointer; background: rgb(255, 255, 255);"></label></div></label><a class="mini-button mini-corner-all mini-button-state-default mini-button-textOnly" href="javascript:void(0)" id="mini-161" style="display: none;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">开始上传</span></a></div><div class="mini-uploader-btn-download" style="display: none;"><a class="mini-button mini-corner-all mini-button-textOnly mini-uploader-batch-download mini-button-ghost mini-button-state-default" href="javascript:void(0)" id="mini-162"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">下载全部</span></a></div></div><div class="mini-uploader-tips"></div><div class="mini-uploader-list"></div></div></td> <td class="file-list-cell btn" style="width:174px;padding-right:12px;padding-left:12px;"><div label="合同" style="width:100%"><span class="mini-textbox" id="remark_HeTongCompany_001" title="" style="border-width: 0px; width: 100%; padding: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="remark_HeTongCompany_001$text" name="remark" maxlength="500" style="width: calc(100% - 0.412812rem);"><span class="mini-maxlength-info" style="">0/500</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span></div></td><td class="file-list-cell btn" style="width:100px"> <span>暂无模板</span></td></tr></table></div></div></div></div></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div opened="true" role="accordion" class="fui-accordion opened">
                    <div role="head" title="审批路径" class="fui-acc-hd"><span class="fui-acc-order">07</span><i class="fui-acc-toggle"></i><h4 class="fui-acc-title">审批路径</h4></div>
                    <div role="body" class="fui-acc-bd">
                        <div class="form-wrap">
                            <div class="form-content">
                                <div class="uc-qypathmodel" id="qypathmodel1" data-options="" style="border-width: 0px;"><span class="mini-outputtext" id="textsplx" title="当前审批模式：无需审批" style="border-width: 0px; color: rgb(244, 80, 81); font-size: 16px; padding: 0px;">当前审批模式：无需审批</span>
<div id="workflowid" style="display: none;">
<div class="fui-form">
	<div class="form-inner">
		<div id="btnSelsptextrow1" role="row" class="form-row">
			<label class="form-label required">本地审批路径：</label>
			<div class="form-control span5">
				<span class="mini-textbox" id="btnSelsptext" style="border-width: 0px; width: 33%; padding: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="btnSelsptext$text" maxlength="150" style="width: calc(100% - 0.412812rem);"><span class="mini-maxlength-info" style="">0/150</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
				<a class="mini-button mini-corner-all mini-button-textOnly mini-button-ghost mini-button-state-default" href="javascript:void(0)" id="qypathmodel1_selpathbtn" style="width: 12%; margin-left: 10px;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">选择审批路径</span></a>
			</div>
		</div>
		<div id="btnSelsptextrow2" role="row" class="form-row">
			<label class="form-label">单位名称：</label>
			<div class="form-control span5">
				<span class="mini-outputtext" id="btnSelspExcuteDanWei" style="border-width: 0px;" title=""></span>
			</div>
			<label class="form-label">审批路径名称：</label>
			<div class="form-control span5">
				<span class="mini-outputtext" id="btnSelspoutput" style="border-width: 0px;" title=""></span>
			</div>
		</div>
	</div>
	<a class="mini-button mini-corner-all mini-button-textOnly mini-button-ghost mini-button-state-default" href="javascript:void(0)" id="qypathmodel1_editpathbtn" style="display: none;"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">修改默认审批人</span></a>
</div>
<div class="mini-panel mini-grid mini-datagrid mini-grid-showHGridLines" tabindex="0" id="qypathmodel1_qypathmodel" style="border-width: 0px; width: 100%; display: none; padding: 0px;"><div class="mini-panel-border mini-grid-border"><div class="mini-panel-header" style="display: none;"><div class="mini-panel-header-inner"><span class="mini-panel-icon mini-icon mini-iconfont " style="display: none;"></span><div class="mini-panel-title" title="&amp;nbsp">&nbsp;</div><div class="mini-tools"><span id="0" class="mini-icon mini-iconfont fa mini-tools-collapse " style=";display:none;"></span><span id="1" class="mini-icon mini-iconfont fa mini-tools-close " style=";display:none;"></span></div></div></div><div class="mini-panel-viewport mini-grid-viewport" style="height: auto;"><div class="mini-panel-toolbar" style="display: none;"></div><div class="mini-grid-columns" style="display: block;"><div class="mini-grid-columns-lock" style="width: 0px; left: -10px; height: auto;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table><div class="mini-grid-topRightCell"></div></div><div class="mini-grid-columns-view" style="margin-left: 0px; margin-right: 0px; width: auto; height: auto; padding-right: 0px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td id="13" style="padding:0;border:0;margin:0;height:0px;width:40px;width:0.4rem"></td><td id="14" style="padding:0;border:0;margin:0;height:0px;width:50px;width:0.5rem"></td><td id="15" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="16" style="padding:0;border:0;margin:0;height:0px;width:100px;width:1rem"></td><td id="18" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="19" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="20" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="21" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td><td id="mini-130$headerCell2$13" class="mini-grid-headerCell  mini-checkcolumn  mini-grid-bottomCell " style=""><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title=""><span class="mini-grid-headertxt is-existed-sorticon">&nbsp;</span></div><div id="13" class="mini-grid-column-splitter"></div></div></td><td id="mini-130$headerCell2$14" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:left;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="序"><span class="mini-grid-headertxt is-existed-sorticon">序</span></div><div id="14" class="mini-grid-column-splitter"></div></div></td><td id="mini-130$headerCell2$15" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:left;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="审批步骤"><span class="mini-grid-headertxt is-existed-sorticon">审批步骤</span></div><div id="15" class="mini-grid-column-splitter"></div></div></td><td id="mini-130$headerCell2$16" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:left;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="审批类型"><span class="mini-grid-headertxt is-existed-sorticon">审批类型</span></div><div id="16" class="mini-grid-column-splitter"></div></div></td><td id="mini-130$headerCell2$18" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:left;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="审批人"><span class="mini-grid-headertxt is-existed-sorticon">审批人</span></div><div id="18" class="mini-grid-column-splitter"></div></div></td><td id="mini-130$headerCell2$19" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:left;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="审批人ID"><span class="mini-grid-headertxt is-existed-sorticon">审批人ID</span></div><div id="19" class="mini-grid-column-splitter"></div></div></td><td id="mini-130$headerCell2$20" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:left;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="单位"><span class="mini-grid-headertxt is-existed-sorticon">单位</span></div><div id="20" class="mini-grid-column-splitter"></div></div></td><td id="mini-130$headerCell2$21" class="mini-grid-headerCell    mini-grid-bottomCell  mini-grid-rightCell " style="text-align:left;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="邮箱"><span class="mini-grid-headertxt is-existed-sorticon">邮箱</span></div><div id="21" class="mini-grid-column-splitter"></div></div></td></tr></tbody></table><div class="mini-grid-topRightCell"></div></div><div class="mini-grid-columns-lock" style="width: 0px; left: auto; right: -10px; height: auto;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table><div class="mini-grid-topRightCell"></div></div><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-filterRow" style="display: none;"><div class="mini-grid-filterRow-lock" style="height: 100%; width: 0px; left: -10px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="position:absolute;top:0;left:0;height:100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-filterRow-view" style="margin-left: 0px; margin-right: 0px; width: auto; padding-right: 0px; height: 0px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="position: absolute; top: 0px; left: 0px; height: 100%; width: 1768px;"><tbody><tr class="datagrid-columns-width-tr" style="height: 0px;"><td style="height:0px;width:0;"></td><td id="13" style="padding:0;border:0;margin:0;height:0px;width:40px;width:0.4rem"></td><td id="14" style="padding:0;border:0;margin:0;height:0px;width:50px;width:0.5rem"></td><td id="15" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="16" style="padding:0;border:0;margin:0;height:0px;width:100px;width:1rem"></td><td id="18" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="19" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="20" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="21" class="datagrid-autowidth-column" data-min-width="100" style="padding: 0px; border: 0px; margin: 0px; height: 0px; width: 100px;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td><td id="mini-130$filter$13" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-130$filter$14" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-130$filter$15" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-130$filter$16" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-130$filter$18" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-130$filter$19" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-130$filter$20" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-130$filter$21" class="mini-grid-filterCell" style="">&nbsp;</td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-filterRow-lock" style="height: 100%; width: 0px; left: auto; right: -10px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="position:absolute;top:0;left:0;height:100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-panel-body mini-grid-rows" style="height: auto;"><div class="mini-grid-rows-lock" style="width: 0px; left: -10px;"><div class="mini-grid-rows-content"><table class="mini-grid-table mini-grid-rowstable" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:1px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table></div></div><div class="mini-grid-rows-view" style="overflow: auto; margin-left: 0px; margin-right: 0px; width: auto;"><div class="mini-grid-rows-content"><table class="mini-grid-table mini-grid-rowstable" cellspacing="0" cellpadding="0" border="0" style="width: 100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:1px;"><td style="height:0px;width:0;"></td><td id="13" style="padding:0;border:0;margin:0;height:0px;width:40px;width:0.4rem"></td><td id="14" style="padding:0;border:0;margin:0;height:0px;width:50px;width:0.5rem"></td><td id="15" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="16" style="padding:0;border:0;margin:0;height:0px;width:100px;width:1rem"></td><td id="18" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="19" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="20" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="21" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td style="width:0px;"></td></tr></tbody></table></div></div><div class="mini-grid-rows-lock" style="overflow: hidden auto; width: 0px; left: auto; right: -10px;"><div class="mini-grid-rows-content"><table class="mini-grid-table mini-grid-rowstable" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:1px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table></div></div><div class="mini-grid-vscroll" style="display: none;"><div class="mini-grid-vscroll-content"></div></div></div><div class="mini-grid-summaryRow" style="display: none;"><div class="mini-grid-summaryRow-lock" style="width: 0px; left: -10px; height: auto;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-summaryRow-view" style="margin-left: 0px; margin-right: 0px; width: auto; height: auto; padding-right: 0px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 100%;"><tbody><tr class="datagrid-columns-width-tr" style="height: 0px;"><td style="height:0px;width:0;"></td><td id="13" style="padding:0;border:0;margin:0;height:0px;width:40px;width:0.4rem"></td><td id="14" style="padding:0;border:0;margin:0;height:0px;width:50px;width:0.5rem"></td><td id="15" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="16" style="padding:0;border:0;margin:0;height:0px;width:100px;width:1rem"></td><td id="18" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="19" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="20" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="21" class="datagrid-autowidth-column" data-min-width="100" style="padding: 0px; border: 0px; margin: 0px; height: 0px; width: 100px;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td><td id="mini-130$summary$13_0" class="mini-grid-summaryCell mini-checkcolumn" style=";">&nbsp;</td><td id="mini-130$summary$14_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-130$summary$15_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-130$summary$16_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-130$summary$18_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-130$summary$19_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-130$summary$20_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-130$summary$21_0" class="mini-grid-summaryCell " style=";">&nbsp;</td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-summaryRow-lock" style="width: 0px; left: auto; right: -10px;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-pager" style="display: none;"><div class="mini-pagination" id="mini-132" style="border-width: 0px;"><div class="pagination-pagerinfo">共0条</div> <div class="pagination-buttons"> <a class="pagination-button pagination-prev mini-icon disabled" action="prev"></a> <a class="pagination-button pagination-next  mini-icon disabled" action="next"></a> </div><span class="mini-buttonedit mini-combobox mini-popupedit" id="mini-133" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly=""><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span><span class="pagination-sizeinfo">条/页</span></span><input name="" type="hidden" value="10"></span><span class="pagination-pagerchange">跳至<input class="pagination-number" value="1">页</span></div></div><div class="mini-panel-footer" style="display: none;"></div><div class="mini-resizer-trigger" style="display: none;"></div></div><a href="#" class="mini-grid-focus" style="position:absolute;left:0px;top:0px;width:0px;height:0px;outline:none;" hidefocus="" onclick="return false"></a></div></div>
</div>
<div id="detailid">暂未选择审批路径！</div></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div><div id="mini-5$body$2" class="mini-tabs-body " style=";display:none;">
            <div class="fui-accordions">
                <div role="accordion" opened="true" class="fui-accordion opened" onloadtopdf="true">
                    <div role="head" title="处理历史" class="fui-acc-hd"><span class="fui-acc-order">08</span><i class="fui-acc-toggle"></i><h4 class="fui-acc-title">处理历史</h4></div>
                    <div role="body" class="fui-acc-bd">
                        <div class="form-wrap">
                            <div class="form-content">
                                <div class="uc-workitemlist" id="workitemlist1" data-options="{IsUseHis:true,WorkflowType:0}" style="border-width: 0px;"><div class="mini-panel mini-grid mini-datagrid mini-grid-showHGridLines" tabindex="0" id="workitemlist1_workitemlist" style="border-width: 0px; width: 100%; display: block; padding: 0px;"><div class="mini-panel-border mini-grid-border"><div class="mini-panel-header" style="display: none;"><div class="mini-panel-header-inner"><span class="mini-panel-icon mini-icon mini-iconfont " style="display: none;"></span><div class="mini-panel-title" title="&amp;nbsp">&nbsp;</div><div class="mini-tools"><span id="0" class="mini-icon mini-iconfont fa mini-tools-collapse " style=";display:none;"></span><span id="1" class="mini-icon mini-iconfont fa mini-tools-close " style=";display:none;"></span></div></div></div><div class="mini-panel-viewport mini-grid-viewport"><div class="mini-panel-toolbar" style="display: none;"></div><div class="mini-grid-columns" style="display: block;"><div class="mini-grid-columns-lock"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table><div class="mini-grid-topRightCell"></div></div><div class="mini-grid-columns-view"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td id="22" style="padding:0;border:0;margin:0;height:0px;width:125px;width:1.25rem"></td><td id="23" style="padding:0;border:0;margin:0;height:0px;width:300px;width:3rem"></td><td id="24" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="25" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="26" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="27" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="28" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="29" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td><td id="mini-139$headerCell2$22" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:center;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="步骤"><span class="mini-grid-headertxt is-existed-sorticon">步骤</span></div><div id="22" class="mini-grid-column-splitter"></div></div></td><td id="mini-139$headerCell2$23" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:center;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="办理人员"><span class="mini-grid-headertxt is-existed-sorticon">办理人员</span></div><div id="23" class="mini-grid-column-splitter"></div></div></td><td id="mini-139$headerCell2$24" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:center;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="收到时间"><span class="mini-grid-headertxt is-existed-sorticon">收到时间</span></div><div id="24" class="mini-grid-column-splitter"></div></div></td><td id="mini-139$headerCell2$25" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:center;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="处理时间"><span class="mini-grid-headertxt is-existed-sorticon">处理时间</span></div><div id="25" class="mini-grid-column-splitter"></div></div></td><td id="mini-139$headerCell2$26" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:center;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="处理意见"><span class="mini-grid-headertxt is-existed-sorticon">处理意见</span></div><div id="26" class="mini-grid-column-splitter"></div></div></td><td id="mini-139$headerCell2$27" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:center;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="处理耗时"><span class="mini-grid-headertxt is-existed-sorticon">处理耗时</span></div><div id="27" class="mini-grid-column-splitter"></div></div></td><td id="mini-139$headerCell2$28" class="mini-grid-headerCell    mini-grid-bottomCell " style="text-align:center;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="是否已阅"><span class="mini-grid-headertxt is-existed-sorticon">是否已阅</span></div><div id="28" class="mini-grid-column-splitter"></div></div></td><td id="mini-139$headerCell2$29" class="mini-grid-headerCell    mini-grid-bottomCell  mini-grid-rightCell " style="text-align:center;"><div class="mini-grid-headerCell-outer"><div class="mini-grid-headerCell-inner  mini-grid-headerCell-nowrap " title="已阅时间"><span class="mini-grid-headertxt is-existed-sorticon">已阅时间</span></div><div id="29" class="mini-grid-column-splitter"></div></div></td></tr></tbody></table><div class="mini-grid-topRightCell"></div></div><div class="mini-grid-columns-lock"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table><div class="mini-grid-topRightCell"></div></div><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-filterRow" style="display: none;"><div class="mini-grid-filterRow-lock" style="height:100%;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="position:absolute;top:0;left:0;height:100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-filterRow-view"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="position:absolute;top:0;left:0;height:100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td id="22" style="padding:0;border:0;margin:0;height:0px;width:125px;width:1.25rem"></td><td id="23" style="padding:0;border:0;margin:0;height:0px;width:300px;width:3rem"></td><td id="24" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="25" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="26" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="27" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="28" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="29" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td><td id="mini-139$filter$22" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-139$filter$23" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-139$filter$24" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-139$filter$25" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-139$filter$26" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-139$filter$27" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-139$filter$28" class="mini-grid-filterCell" style="">&nbsp;</td><td id="mini-139$filter$29" class="mini-grid-filterCell" style="">&nbsp;</td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-filterRow-lock" style="height:100%;"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0" style="position:absolute;top:0;left:0;height:100%;"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-panel-body mini-grid-rows"><div class="mini-grid-rows-lock"><div class="mini-grid-rows-content"><table class="mini-grid-table mini-grid-rowstable" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:1px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table></div></div><div class="mini-grid-rows-view" style="overflow: auto;"><div class="mini-grid-rows-content"><table class="mini-grid-table mini-grid-rowstable" cellspacing="0" cellpadding="0" border="0"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td id="22" style="padding:0;border:0;margin:0;height:0px;width:125px;width:1.25rem"></td><td id="23" style="padding:0;border:0;margin:0;height:0px;width:300px;width:3rem"></td><td id="24" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="25" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="26" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="27" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="28" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="29" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td style="width:0px;"></td></tr><tr class="mini-grid-row  " style=" " id="mini-139$row2$69"><td style="width:0;"></td><td id="69$cell$22" class="mini-grid-cell " style="border-right:0;text-align:center;"><div class="mini-grid-cell-inner " style="" title="提报">提报</div></td><td id="69$cell$23" class="mini-grid-cell " style="border-right:0;text-align:center;"><div class="mini-grid-cell-inner " style="" title="郑东">郑东</div></td><td id="69$cell$24" class="mini-grid-cell " style="border-right:0;text-align:center;"><div class="mini-grid-cell-inner " style="" title="2025-05-28 06:06">2025-05-28 06:06</div></td><td id="69$cell$25" class="mini-grid-cell " style="border-right:0;text-align:center;"><div class="mini-grid-cell-inner " style="" title="--">--</div></td><td id="69$cell$26" class="mini-grid-cell " style="border-right:0;text-align:center;"><div class="mini-grid-cell-inner " style="" title="[未签署]"><font color="gray">[未签署]</font></div></td><td id="69$cell$27" class="mini-grid-cell " style="border-right:0;text-align:center;"><div class="mini-grid-cell-inner " style="" title="--">--</div></td><td id="69$cell$28" class="mini-grid-cell " style="border-right:0;text-align:center;"><div class="mini-grid-cell-inner " style="" title="是">是</div></td><td id="69$cell$29" class="mini-grid-cell  mini-grid-rightCell " style="border-right:0;text-align:center;"><div class="mini-grid-cell-inner " style="" title="2025-05-31 14:27">2025-05-31 14:27</div></td></tr></tbody></table></div></div><div class="mini-grid-rows-lock" style="overflow-x: hidden; overflow-y: auto;"><div class="mini-grid-rows-content"><table class="mini-grid-table mini-grid-rowstable" cellspacing="0" cellpadding="0" border="0" style="width: 0px;"><tbody><tr class="datagrid-columns-width-tr" style="height:1px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr></tbody></table></div></div><div class="mini-grid-vscroll"><div class="mini-grid-vscroll-content"></div></div></div><div class="mini-grid-summaryRow" style="display: none;"><div class="mini-grid-summaryRow-lock"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-summaryRow-view"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td id="22" style="padding:0;border:0;margin:0;height:0px;width:125px;width:1.25rem"></td><td id="23" style="padding:0;border:0;margin:0;height:0px;width:300px;width:3rem"></td><td id="24" style="padding:0;border:0;margin:0;height:0px;width:150px;width:1.5rem"></td><td id="25" style="padding:0;border:0;margin:0;height:0px;width:250px;width:2.5rem"></td><td id="26" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="27" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="28" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td id="29" class="datagrid-autowidth-column" data-min-width="100" style="padding:0;border:0;margin:0;height:0px;width:100%"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td><td id="mini-139$summary$22_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-139$summary$23_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-139$summary$24_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-139$summary$25_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-139$summary$26_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-139$summary$27_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-139$summary$28_0" class="mini-grid-summaryCell " style=";">&nbsp;</td><td id="mini-139$summary$29_0" class="mini-grid-summaryCell " style=";">&nbsp;</td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-summaryRow-lock"><table class="mini-grid-table" cellspacing="0" cellpadding="0" border="0"><tbody><tr class="datagrid-columns-width-tr" style="height:0px;"><td style="height:0px;width:0;"></td><td style="width:0px;"></td></tr><tr><td style="width:0;"></td></tr></tbody></table><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-scrollHeaderCell"></div></div><div class="mini-grid-pager" style="display: none;"><div class="mini-pagination" id="mini-141" style="border-width: 0px;"><div class="pagination-pagerinfo">共0条</div> <div class="pagination-buttons"> <a class="pagination-button pagination-prev mini-icon disabled" action="prev"></a> <a class="pagination-button pagination-next  mini-icon disabled" action="next"></a> </div><span class="mini-buttonedit mini-combobox mini-popupedit" id="mini-142" style="border-width: 0px;"><span class="mini-buttonedit-border mini-corner-all"><input type="text" class="mini-buttonedit-input" autocomplete="off" placeholder="" readonly=""><span class="mini-buttonedit-buttons"><span class="mini-buttonedit-close mini-icon" name="close"></span><span title="" name="trigger" class="mini-buttonedit-button mini-buttonedit-trigger" onmouseover="mini.addClass(this, 'mini-buttonedit-button-hover');" onmouseout="mini.removeClass(this, 'mini-buttonedit-button-hover');"><span class="mini-buttonedit-icon mini-icon mini-iconfont "></span></span></span><span class="pagination-sizeinfo">条/页</span></span><input name="" type="hidden" value="10"></span><span class="pagination-pagerchange">跳至<input class="pagination-number" value="1">页</span></div></div><div class="mini-panel-footer" style="display: none;"></div><div class="mini-resizer-trigger"></div></div><a href="#" class="mini-grid-focus" style="position:absolute;left:0px;top:0px;width:0px;height:0px;outline:none;" hidefocus="" onclick="return false"></a></div></div></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div></div></td><td></td></tr></tbody></table></div>
</div>
<div class="hidden">
    <span class="mini-textbox" id="htguid" style="border-width: 0px;"><span class="mini-textbox-prepend" style="display: none"></span><span class="mini-textbox-border mini-corner-all"><input type="text" class="mini-textbox-input" autocomplete="off" placeholder="" id="htguid$text"><span class="mini-maxlength-info" style="display:none;">36/5000</span></span><input type="hidden"><span class="mini-textbox-append" style="display: none"></span></span>
    <input type="hidden" class="mini-hidden" id="applyedanweiguid">
    <!--    <input bind="dataBeanCompany.rowguid" class="mini-hidden" id="elecompanyguid"/>-->
    <input type="hidden" class="mini-hidden" id="buyunitguid" value="0108">
    <input type="hidden" class="mini-hidden" id="sellunitguid">
    <input type="hidden" class="mini-hidden" id="productcommodity" value="信息化软硬件及工装设备类-设备类设备工程服务-设备维修类-起重运输设备类">
    <input type="hidden" class="mini-hidden" id="selllianxiren">
    <!--<input bind="dataBean.selldanweitype" class="mini-hidden" id="selldanweitype"/>-->
    <input type="hidden" class="mini-hidden" id="buydanweitype" value="0">

    <input type="hidden" class="mini-hidden" id="selldanweitypecompany">

    <input type="hidden" class="mini-hidden" id="hetongbelongtoouname">

</div>
<script src="../../rest/resource/jsboot"></script><script src="/tpframe/frame/fui/js/libs/sm2security.js?_=1671433232449"></script>
<script src="/tpframe/frame/fui/js/dist/libs-nov.min.js?_=1671433232449"></script>
<script src="/tpframe/frame/fui/js/miniui/local/zh_CN.js?_=1671433232449"></script>
<script src="/tpframe/frame/fui/js/miniui/miniui.min.js?_=1671433232449"></script>
<script src="/tpframe/frame/fui/js/dist/frame.min.js?_=1671433232449"></script><div class="mini-grid-cell-inner" style="position:absolute;left:-1000px;top:-1000px;"></div>
<script src="/tpframe/frame/fui/js/frame.custom.js?_=1671433232449"></script><link rel="stylesheet" href="/tpframe/css/ztb.css?_=1671433232449">
<script src="/tpframe/js/ztb.js?_=1671433232449"></script><div id="quicklogin"></div>
<script src="/tpframe/uc/epointca/epointca.js?_=1671433232449"></script>
<script src="/tpframe/uc/epointdocshowctl/epointdocshowctl.js?_=1671433232449"></script>
<script src="/tpframe/uc/bdlist/bdlist.js?_=1671433232449"></script>
<script src="/tpframe/uc/allbdlist/allbdlist.js?_=1671433232449"></script>
<script src="/tpframe/uc/handlecontrols/handlecontrols.js?_=1671433232449"></script>
<script src="/tpframe/uc/workitemlist/workitemlist.js?_=1671433232449"></script>
<script src="/tpframe/uc/qypathmodel/qypathmodel.js?_=1671433232449"></script>
<script src="/tpframe/uc/versionbglist/versionbglist.js?_=1671433232449"></script>
<script src="/tpframe/uc/ywscanfilelist/ywscanfilelist.js?_=1671433232449"></script>
<script src="/tpframe/frame/pages/epointworkflow/client/uc/workflowhistory/workflowhistory.js?_=1671433232449"></script>
<script src="/tpframe/uc/cardbdlist/jsgcbdlist.js?_=1671433232449"></script>
<script src="/tpframe/uc/fabaoinfo/fabaoinfo.js?_=1671433232449"></script>
<script src="/tpframe/uc/mtrorder/mtrorder.js?_=1671433232449"></script><script src="/tpframe/frame/fui/js/widgets/jquery.placeholder.min.js?_=1671433232449"></script>

<script src="/tpframe/frame/fui/js/epoint/epoint.controlconfig.js?_=1671433232449"></script>
<script src="/tpframe/frame/fui/js/epoint/epoint.form.js?_=1671433232449"></script>
<script src="/tpframe/uc/versionswitch/versionswitch.js?_=1671433232449"></script><script src="/tpframe/uc/versionswitch/libs/layer/layer.js?_=1671433232449"></script>

<script src="/tpframe/uc/feedbacklist/feedbacklist.js?_=1671433232449"></script>
<script src="/tpframe/uc/touzigusuan/touzigusuanunit.js?_=1671433232449"></script>
<script src="/tpframe/crrc/frame/fui/js/timercommon.js?_=1671433232449"></script>


<script>
    //初始化加载数据
    var grid1 = mini.get("datagrid1");
    var pguid;
    grid1.on("load", function() {
        grid1.mergeColumns(["prdanweiname","zhongbiaodanweiname" ]);
    });
    epoint.initPage('qycontractproductioncompanysimpleworkflowaction', null, function(data) {
        // ztb.grid.beginEdit(grid2);
        if(data.IsUseEContractSys != '1'){
            $("#contracttemplatediv").hide();
        }
        if(data.iszbb == "0"){
            Util.accordion.hideItem(6);
        }
        if(data.pguid){
            pguid = data.pguid;
        }
        zhibaojinchange();

        // 隐藏
        $('#hiderow1').hide();
    });

    function buylianxirenValueChange(){
        if (!mini.get("buyunitname").getValue()){
            epoint.alert("请选择买方公司！");
            return;
        }
        var url='./souselect_ouuser';
        epoint.openDialog("选择",url , function (ret) {
            if (ret && ret != "close") {
                var jsonret = JSON.parse(ret);
                mini.get('buylianxirenname').setValue(jsonret.username);
                mini.get('buylianxirenname').setText(jsonret.username);
                mini.get('buylianxiren').setValue(jsonret.userguid);
                mini.get('buytelephone').setValue(jsonret.mobile);
                epoint.validate(['buylianxirenname']);
            }
        });
    }

    function selldanweitypechange(){
        mini.get('sellunitguid').setValue('');
        mini.get('sellunitname').setValue('');
        mini.get('sellnsrsbh').setValue('');
    }

    function selllianxirenValueChange(){
        if (!mini.get("sellunitname").getValue()){
            epoint.alert("请选择卖方公司！");
            return;
        }
        var url='./souselect_ouuser';
        epoint.openDialog("选择",url , function (ret) {
            if (ret && ret != "close") {
                var jsonret = JSON.parse(ret);
                mini.get('selllianxirenname').setValue(jsonret.username);
                mini.get('selllianxirenname').setText(jsonret.username);
                mini.get('selllianxiren').setValue(jsonret.userguid);
                mini.get('selltelephone').setValue(jsonret.mobile);
                epoint.validate(['selllianxirenname']);
            }
        });
    }
    function makeHt_Click(){
        // 单点登录电子合同系统
        // window.open("../../netztbmis/pages/sso?Type=dzht&MenuType=20" + "&RowGuid=" + mini.get('htguid').getValue());
        if (epoint.validate()) {
            epoint.execute('saveAndPushHetong', null, null, function (ret) {
                if (ret.indexOf("echt") >= 0) {
                    //打开电子合同模版挑选页面
                    //window.location.href = "../../netztbmis/pages/sso?Type=dzht&MenuType=20";
                    var MenuType = "20";
                    if (ret.indexOf("type:edit") >= 0) {
                        MenuType = "WordEdit";
                    }
                    window.open("../../netztbmis/pages/sso?Type=dzht&MenuType=" + MenuType + "&RowGuid=" + ret.split(":")[1] + "&yewutype=2");
                } else {
                    epoint.alert(ret);
                }
            });
        } else {
            epoint.alert(ret);
        }
    }
    function onSplitRendererEdit(e) {
        return epoint.renderCell(e, "action-icon icon-search", "openSplitEdit");
    }


    // 打开付款方式编辑页面
    function addPayMethod() {
        epoint.openDialog('新增付款方式', './contractpaymethod_workflow?htguid=' + mini.get("htguid").getValue(), function() {
            epoint.refresh([ 'datagridPayMethod' ]);
        }, {
            width : 1600,
            height : 800
        });

    }

    // 删除合同付款方式数据
    function deletePayMethod() {
        if (mini.get("datagridPayMethod").getSelecteds().length <= 0) {
            epoint.alert("请选择付款信息！");
        } else {
            epoint.execute('deletePayMethod', null, null, function(ret) {
                if (ret) {
                    epoint.alert(ret);
                } else {
                    epoint.refresh([ 'datagridPayMethod' ]);
                }
            });
        }
    }

    function addsubject(){
        epoint.openTopDialog('新增物料', "./contractproducteditaddmaterialsimple_workflow?htguid=" + mini.get("htguid").getValue() + "&iscompany=1", function(ret) {
            epoint.refresh([ 'datagrid1' ]);
        });
    }

    function zhibaojinchange(){
        var zhibaojin = mini.get('zhibaojin').getValue();
        if (zhibaojin == '1') {
            $('#zhibaojindiv').show();
            mini.get("zhibaojinratio").required = true;
            $('#zhibaojinratio').parent().prev().addClass("required");
            mini.get("zhibaojinratio").requiredErrorText = "质保金比例必填！";

            mini.get("zhibaotiaokuan").required = true;
            $('#zhibaotiaokuan').parent().prev().addClass("required");
            mini.get("zhibaotiaokuan").requiredErrorText = "质保条款必填！";

            $("#zhibaoqiDiv").show();
            $("#zhibaoqiDiv").prev().show();
            mini.get("zhibaoqi").required = true;
            $('#zhibaoqi').parent().prev().addClass("required");
            mini.get("zhibaoqi").requiredErrorText = "质保期必填！";
        }
        else {
            $('#zhibaojindiv').hide();
            mini.get("zhibaojinratio").setValue(0);
            mini.get("zhibaojinratio").required = false;
            $('#zhibaojinratio').parent().prev().removeClass("required");
            mini.get("zhibaotiaokuan").required = false;
            $('#zhibaotiaokuan').parent().prev().removeClass("required");

            $("#zhibaoqiDiv").hide();
            $("#zhibaoqiDiv").prev().hide();
            mini.get("zhibaoqi").required = false;
            $('#zhibaoqi').parent().prev().removeClass("required");
        }
    }

    // 删除
    function deleteSelect() {
        epoint.execute('deleteSelect', null, null, function(ret) {
            if (ret) {
                epoint.alertAndRefresh(ret)
            }
        });
    }

    function selectbuyCompany(){
        epoint.openTopDialog('挑选公司', 'qy/kjxy/companyselectconfig_edit?companyselectconfig_edit.html=1', function (danweiinfo) {
            if (danweiinfo != 'close') {
                var jsondanwei = JSON.parse(danweiinfo);
                var danweiname = jsondanwei.danweiname;
                var danweiguid = jsondanwei.danweiguid;
                mini.get('buyunitname').setValue(danweiname);
                // mini.get('buyunitname').setText(danweiname);
                mini.get('buyunitguid').setValue(danweiguid);
                mini.get('buynsrsbh').setValue(jsondanwei.unitorgnum);
                mini.get('buydanweitype').setValue('0');
            }
        }, {
            'width': 570,
            'height': 680
        });
    }

    function selectbuyGys(){
        epoint.openTopDialog('挑选供应商', './select_gys', function (danweiinfo) {
            if (danweiinfo != 'close' && danweiinfo != 'ok') {
                var danweiguid = danweiinfo.split("&")[0];
                var danweiname = danweiinfo.split("&")[1];
                var buytelephone = danweiinfo.split("&")[2];
                var buylianxiren = danweiinfo.split("&")[3];
                var buynsrsbh = danweiinfo.split("&")[4];
                mini.get('buydanweitype').setValue('1');
                mini.get('buyunitname').setValue(danweiname);
                mini.get('buyunitguid').setValue(danweiguid);
                mini.get('buynsrsbh').setValue(buynsrsbh);
            }
        });
    }

    function selectsellDw(){
        var selldanweitype = mini.get('selldanweitype').getValue();
        if(!selldanweitype){
            epoint.alert("请先选择合同卖方性质！");
            return;
        }
        if("0" == selldanweitype){
            selectsellCompany();
        }else{
            selectsellGys();
        }

    }

    function selectsellCompany(){
        epoint.openTopDialog('挑选公司', 'qy/kjxy/companyselectconfig_edit?companyselectconfig_edit.html=1', function (danweiinfo) {
            if (danweiinfo != 'close') {
                var jsondanwei = JSON.parse(danweiinfo);
                var danweiname= jsondanwei.danweiname;
                var danweiguid= jsondanwei.danweiguid;
                mini.get('sellunitname').setValue(danweiname);
                // mini.get('sellunitname').setText(danweiname);
                mini.get('sellunitguid').setValue(danweiguid);
                mini.get('selldanweitype').setValue('0');
                mini.get('selldanweitypecompany').setValue('0');
                mini.get('sellnsrsbh').setValue(jsondanwei.unitorgnum);
                //mini.get('sellunitname').setEnabled(false);
               // mini.get('sellunitname').setAllowInput(false);
            }
        }, {
            'width': 570,
            'height': 680
        });
    }


    function selectsellGys(){
        epoint.openTopDialog('挑选供应商', './select_gys', function (danweiinfo) {
            if (danweiinfo != 'close') {
                var danweiguid= danweiinfo.split("&")[0];
                var danweiname= danweiinfo.split("&")[1];
                var sellnsrsbh = danweiinfo.split("&")[4];
                mini.get('sellunitname').setValue(danweiname);
                mini.get('sellunitguid').setValue(danweiguid);
                mini.get('sellnsrsbh').setValue(sellnsrsbh);
                mini.get('selldanweitype').setValue('1');
                mini.get('selldanweitypecompany').setValue('1');
                //mini.get('sellunitname').setEnabled(true);
                //mini.get('sellunitname').setAllowInput(true);
            }
        });
    }

    function caigoufenleichange(){
        epoint.refresh(['caigoufenlei']);
    }


    function calJined(e){
        var notaxhtjine=mini.get('notaxhtjine').getValue();
        var htjine=mini.get('htjine').getValue();
        if(!isNaN(e.value)){
            var ret=calculateContractValues(notaxhtjine, htjine);
            if(ret.notaxhtjine!=-1){
                mini.get('notaxhtjine').setValue(ret.notaxhtjine);
            }
            if(ret.htjine!=-1){
                mini.get('htjine').setValue(ret.htjine);
            }
            if(ret.taxrate!=-1){
                mini.get('taxrate').setValue(ret.taxrate);
            }

        }

    }

    function calculateContractValues(notaxhtjine, htjine) {
        // 创建一个JSON对象来存储结果
        var result = {};
        // 检查参数是否有效
        if (notaxhtjine === 0 || htjine === 0) {
            result["taxrate"] = -1;
            result["notaxhtjine"] = -1;
            result["htjine"] = -1;
            return result;
        }
        // 计算税率（假设税率是固定的，这里用差值与不含税金额的比值模拟）
        var taxRate = (htjine - notaxhtjine) / notaxhtjine ;
        taxRate = taxRate.toFixed(2);
        result["taxrate"] = taxRate*100;
        // 将不含税金额和含税金额也存入结果对象
        result["notaxhtjine"] = notaxhtjine;
        result["htjine"] = htjine;
        // 返回结果对象
        return result;
    }

    function onDelRenderer(e){
        return epoint.renderCell(e, "action-icon icon-remove", "delGysData");
    }

    function delGysData() {
        var rows = grid2.getSelecteds();
        if (rows.length > 0) {
            grid2.removeRows(rows, true);
        }
    }

    // 打开付款方式编辑页面
    function addJoinGys() {
        epoint.openDialog('新增投标供应商', './contractjoingys_workflow?htguid=' + mini.get("htguid").getValue(), function() {
            epoint.refresh([ 'datagridjoingys' ]);
        }, {
            width : 1600,
            height : 800
        });

    }

    function calc() {
        var unitprice = mini.get("unitprice").getValue();
        var num = mini.get("caigounum").getValue();
        var unit = mini.get("unit").getValue();
        var total = accMul(unitprice / unit, num);
        if (!isNaN(total) && total != "Infinity") {
            mini.get("yusuanprice").setValue(total.toFixed(2));
            taxrate = (Number(mini.get("taxrate").getValue()) + 100) / 100;
        }

        var taxrate = mini.get("taxrate").getValue();
        if (taxrate) {
            taxrate = (Number(mini.get("taxrate").getValue()) + 100) / 100;
            var yusuanunitpriceinctax = accMul(unitprice, taxrate);
            if (!isNaN(yusuanunitpriceinctax) && yusuanunitpriceinctax != "Infinity") {
                mini.get("yusuanunitpriceinctax").setValue(yusuanunitpriceinctax.toFixed(2));
            }

            var yusuanpriceinctax = accMul(Number(mini.get("yusuanprice").getValue()), taxrate);
            if (!isNaN(yusuanpriceinctax) && yusuanpriceinctax != "Infinity") {
                mini.get("yusuanpriceinctax").setValue(yusuanpriceinctax.toFixed(2));
            }

            var zuigaoxianjiainctax = accMul(Number(mini.get("zuigaoxianjia").getValue()), taxrate);
            if (!isNaN(zuigaoxianjiainctax) && zuigaoxianjiainctax != "Infinity") {
                mini.get("zuigaoxianjiainctax").setValue(zuigaoxianjiainctax.toFixed(2));
            }
        }
    }

    //乘法精度计算
    function accMul(arg1, arg2) {
        var m = 0, s1 = arg1.toString(), s2 = arg2.toString(), t;
        t = s1.split(".");
        // 判断有没有小数位，避免出错
        if (t[1]) {
            m += t[1].length
        }
        t = s2.split(".");
        if (t[1]) {
            m += t[1].length
        }
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m)
    }

    function hetongBelongChanged() {
        mini.get("hetongbelongtoouname").setValue(mini.get("hetongbelongtoouguid").getText());
    }

    function isSpUploadFileChange(){
        var isSpUploadFile = mini.get("is_sp_upload_file").getValue();
        if(isSpUploadFile === '1'){
            //是否需要审批后上传合同附件，为是的时候，需要已配置单位审批流程，否则不可选择“是”
            if(!pguid){
                mini.get("is_sp_upload_file").setValue("0");
                epoint.alert("请联系单位管理员在单位流程配置中配置合同归档审批流！");
            }
        }
    }


    function selecthistoryht(){
        epoint.openTopDialog('挑选历史合同', 'qy/contractproduction/historycontract_select?type=dzht&issampile=1', function (ret) {
            if(ret && ret != "close"){
                    //  data 是一个 JSON 字符串
                    try {
                        var parsedData = JSON.parse(ret);
                        // 需要设置值的键列表
                        var keysToSet = ['iskjxy','is_sp_upload_file','daterangepicker','htqstime','billingtype','jinecurrency',
                            'notaxhtjine','htjine','taxrate','performanceaddress','zhibaojin','zhibaoqi','isbg','buchongxieyi',
                            'zhibaojinratio','zhibaotiaokuan','jiesuanconditions','sourcingcgfs','sourcingcgxs','dzcgplatform','caigoufenlei','dujiacaigou',
                            'dailicaigou','centralizecgtype','hetongbelongtoouguid','hetongbelongtoouname'];

                        // 使用 forEach 遍历键列表
                        keysToSet.forEach(key => {
                            // 确保 key 存在于 parsedData 中
                            if (parsedData.hasOwnProperty(key)) {
                                //htstartdate 毫秒数 转换成 日期
                                if(key == 'daterangepicker'){
                                    mini.get('daterangepicker').setValue(parsedData[key]);
                                }else{
                                    if(parsedData[key]){
                                        mini.get(key).setValue(parsedData[key]);
                                    }else{
                                        mini.get(key).setValue('');
                                    }
                                }

                            } else {
                                //转换为null 赋值null
                                mini.get(key).setValue('');
                                console.error(`Key ${key} not found in the parsed data.`);
                            }
                        });

                        zhibaojinchange();
                        hetongBelongSelChanged();
                    } catch (error) {
                        console.error('Error parsing JSON');
                    }
            }
        });
    }

    function hetongBelongSelChanged() {
        mini.get("hetongbelongtoouguid").setText(mini.get("hetongbelongtoouname").getValue());
    }

</script>


<div class="mini-tooltip mini-tooltip-theme-error" style="border-width: 0px;"><div class="mini-tooltip-arrow"></div><div class="mini-tooltip-inner mini-corner-all"></div></div><div class="mini-tooltip mini-tooltip-theme-dark" style="border-width: 0px;"><div class="mini-tooltip-arrow"></div><div class="mini-tooltip-inner mini-corner-all"></div></div><div class="hidden" style="display:none"><svg><symbol id="icon-trash-svg" viewBox="0 0 400 400"><g><rect x="40.01" y="110.4" fill="#4E5463" width="319.96" height="40"></rect><rect x="120.16" y="40.53" fill="#4E5463" width="159.72" height="40"></rect><rect x="146.87" y="190.56" fill="currentColor" width="40" height="90.18"></rect><rect x="213.39" y="190.56" fill="currentColor" width="40" height="90.18"></rect><path fill="#4E5463" d="M281.84,360.32H118.21c-20.95,0-38-17.05-38-38V131.26h40v189.06h159.63V131.26h40v191.06 C319.84,343.28,302.8,360.32,281.84,360.32z"></path></g></symbol><symbol id="icon-modify-svg" viewBox="0 0 400 400"><g><path fill="#4E5463" d="M301.98,360.57H97.94c-20.95,0-38-17.05-38-38V118.44c0-20.95,17.05-38,38-38h57.1l25.07,40H99.94v200.13 h200.03v-95.49l40-24.68v122.17C339.98,343.52,322.93,360.57,301.98,360.57z"></path><rect x="139.43" y="120" transform="matrix(0.6394 -0.7689 0.7689 0.6394 -16.6869 244.4267)" fill="currentColor" width="225.6" height="40"></rect></g></symbol><symbol id="icon-download-svg" viewBox="0 0 400 400"><g><path fill="#4E5463" d="M60.04,320.4h280.04v39.99H60.04v-20V320.4z"></path><path fill="currentColor" d="M219.99,40.4v209.52H180V40.4h20H219.99z"></path><polygon fill="currentColor" points="312.84,159.27 298.7,145.13 200,243.83 101.03,144.87 72.75,173.15 171.72,272.11 171.72,272.11 200,300.39 326.98,173.41 "></polygon></g></symbol><symbol id="icon-clouddown-svg" viewBox="0 0 400 400"><g><path fill="currentColor" d="M219.91,190.42v119.54h-39.99V190.42h20H219.91z"></path><path fill="currentColor" d="M143.44,237.17L228.28,322L200,350.28l-84.83-84.83l14.14-14.14L143.44,237.17z"></path><path fill="currentColor" d="M284.83,265.45L200,350.28L171.72,322l84.83-84.83l14.14,14.14L284.83,265.45z"></path><path fill="#4E5463" d="M342.36,171.17c-12.12-12.58-27.07-21.57-43.46-26.33c0.29-2.88,0.44-5.79,0.44-8.71 c0-47.24-38.43-85.67-85.68-85.67c-43.92,0-80.23,33.22-85.11,75.85c-54.97,3.95-98.49,49.94-98.49,105.9 c0,56.45,44.29,102.75,99.94,105.99v-40.11c-33.58-3.15-59.94-31.49-59.94-65.87c0-36.49,29.69-66.18,66.18-66.18 c3.22,0,6.5,0.24,9.75,0.72l27.14,4l-4.49-27.06c-0.42-2.54-0.63-5.08-0.63-7.56c0-25.19,20.49-45.67,45.67-45.67 c25.19,0,45.68,20.49,45.68,45.67c0,5.97-1.14,11.78-3.39,17.27l-10.8,26.4l28.5,1.16c31.58,1.28,56.31,27.05,56.31,58.67 c0,32.39-26.35,58.74-58.74,58.74c-0.41,0-0.81-0.01-1.22-0.02v39.99c0.41,0,0.81,0.03,1.22,0.03 c54.45,0,98.74-44.29,98.74-98.74C369.96,213.97,360.16,189.66,342.36,171.17z"></path></g></symbol><symbol id="icon-view-svg" viewBox="0 0 400 400"><g><path fill="currentColor" d="M354.05,326.12l-60.92-60.92c-7.81-7.81-20.47-7.81-28.28,0c-7.81,7.81-7.81,20.47,0,28.28l60.92,60.92 c3.91,3.91,9.02,5.86,14.14,5.86c5.12,0,10.24-1.95,14.14-5.86C361.86,346.6,361.86,333.93,354.05,326.12z"></path><path fill="#4E5463" d="M184.8,329c-79.6,0-144.35-64.76-144.35-144.35c0-79.6,64.76-144.35,144.35-144.35 c79.6,0,144.35,64.76,144.35,144.35C329.16,264.24,264.4,329,184.8,329z M184.8,80.29c-57.54,0-104.35,46.81-104.35,104.35 S127.26,289,184.8,289c57.54,0,104.35-46.81,104.35-104.35S242.35,80.29,184.8,80.29z"></path><path fill="currentColor" d="M239.81,200.35c-11.05,0-20-8.95-20-20c0-16.58-13.49-30.07-30.07-30.07c-11.05,0-20-8.95-20-20 s8.95-20,20-20c38.64,0,70.07,31.43,70.07,70.07C259.81,191.39,250.85,200.35,239.81,200.35z"></path></g></symbol><symbol id="icon-search-svg" viewBox="0 0 400 400"><g><path fill="currentColor" d="M354.05,326.12l-60.92-60.92c-7.81-7.81-20.47-7.81-28.28,0c-7.81,7.81-7.81,20.47,0,28.28l60.92,60.92 c3.91,3.91,9.02,5.86,14.14,5.86c5.12,0,10.24-1.95,14.14-5.86C361.87,346.6,361.87,333.93,354.05,326.12z"></path><path fill="#4E5463" d="M184.8,329c-79.6,0-144.35-64.76-144.35-144.35c0-79.6,64.76-144.35,144.35-144.35 c79.6,0,144.35,64.76,144.35,144.35C329.16,264.24,264.4,329,184.8,329z M184.8,80.29c-57.54,0-104.35,46.81-104.35,104.35 S127.26,289,184.8,289c57.54,0,104.35-46.81,104.35-104.35S242.34,80.29,184.8,80.29z"></path></g></symbol><symbol id="icon-select-svg" viewBox="0 0 400 400"><g><path fill="currentColor" d="M209.47,210.35v119.99l119.99-119.99H209.47z"></path><path fill="#4E5463" d="M169.93,350.34H89.6c-21.77,0-39.48-17.71-39.48-39.48V89.99c0-21.77,17.71-39.48,39.48-39.48h220.77 c21.77,0,39.48,17.71,39.48,39.48v90.4h-40V90.51H90.12v219.83h79.81V350.34z"></path><path fill="currentColor" d="M329.46,350.37c-5.12,0-10.24-1.95-14.14-5.86l-79.8-79.8c-7.81-7.81-7.81-20.47,0-28.28 c7.81-7.81,20.47-7.81,28.28,0l79.8,79.8c7.81,7.81,7.81,20.47,0,28.28C339.7,348.41,334.58,350.37,329.46,350.37z"></path></g></symbol><symbol id="icon-voice-svg" viewBox="0 0 400 400"><g><path fill="#4E5463" d="M245.37,360.37l-28.78-27.78c34.4-35.63,53.34-82.53,53.34-132.06c0-49.57-18.97-96.5-53.41-132.14 l28.76-27.8c41.69,43.13,64.64,99.93,64.64,159.93C309.93,260.48,287.01,317.24,245.37,360.37z"></path><path fill="currentColor" d="M180.71,297.84l-28.75-27.82c18.13-18.74,28.12-43.42,28.12-69.51c0-26.1-10-50.8-28.15-69.54l28.73-27.83 c25.42,26.25,39.42,60.83,39.42,97.37C220.09,237.04,206.1,271.6,180.71,297.84z"></path><path fill="currentColor" d="M123.2,242.4c10.52-10.82,17-25.6,17-41.88c0-16.32-6.5-31.12-17.06-41.94l-43.27,41.91L123.2,242.4z"></path></g></symbol><symbol id="icon-location-svg" viewBox="0 0 400 400"><g><path fill="currentColor" d="M209.99,231c-33.39,0-60.55-27.16-60.55-60.55c0-33.39,27.16-60.55,60.55-60.55 c33.39,0,60.55,27.16,60.55,60.55C270.55,203.84,243.38,231,209.99,231z M209.99,149.9c-11.33,0-20.55,9.22-20.55,20.55 S198.66,191,209.99,191c11.33,0,20.55-9.22,20.55-20.55S221.33,149.9,209.99,149.9z"></path><path fill="#4E5463" d="M209.99,360.44c-5.91,0-11.52-2.62-15.32-7.15l-88.8-105.89c-1.39-1.66-2.74-3.39-4.01-5.15 c-2.08-2.88-3.85-5.64-5.42-8.44c-10.77-19.26-16.46-41.17-16.46-63.36c0-34.6,13.44-67.17,37.84-91.7 c24.39-24.52,56.87-38.13,91.45-38.31c34.79-0.18,67.57,13.27,92.33,37.9c24.76,24.63,38.4,57.34,38.4,92.11 c0,22.1-5.65,43.94-16.35,63.16c-1.59,2.86-3.45,5.77-5.53,8.65c-1.26,1.75-2.61,3.48-4,5.13l-88.81,105.9 C221.52,357.82,215.91,360.44,209.99,360.44z M209.99,80.44c-0.17,0-0.33,0-0.5,0c-49.36,0.27-89.51,40.65-89.51,90.01 c0,15.37,3.93,30.53,11.37,43.84c0.81,1.44,1.76,2.92,2.93,4.53c0.71,0.98,1.46,1.95,2.23,2.87l73.48,87.62l73.49-87.63 c0.77-0.92,1.52-1.88,2.22-2.85c1.18-1.63,2.16-3.17,3.01-4.68c0,0,0,0,0,0c7.39-13.28,11.3-28.39,11.3-43.7 c0-24.04-9.45-46.68-26.61-63.75C256.36,89.75,233.85,80.44,209.99,80.44z"></path></g></symbol><symbol id="icon-pause-svg" viewBox="0 0 400 400"><g><path fill="#4E5463" d="M199.91,360.23c-88.16,0-159.88-71.72-159.88-159.88c0-88.16,71.72-159.88,159.88-159.88 c88.16,0,159.88,71.72,159.88,159.88C359.79,288.51,288.07,360.23,199.91,360.23z M199.91,80.46 c-66.1,0-119.88,53.78-119.88,119.88s53.78,119.88,119.88,119.88s119.88-53.78,119.88-119.88S266.01,80.46,199.91,80.46z"></path><rect x="146.88" y="140.17" fill="currentColor" width="40" height="120.34"></rect><rect x="213.39" y="140.17" fill="currentColor" width="40" height="120.34"></rect></g></symbol><symbol id="icon-play-svg" viewBox="0 0 400 400"><g><path fill="#4E5463" d="M199.91,360.23c-88.16,0-159.88-71.72-159.88-159.88c0-88.16,71.72-159.88,159.88-159.88 c88.16,0,159.88,71.72,159.88,159.88C359.79,288.51,288.07,360.23,199.91,360.23z M199.91,80.46 c-66.1,0-119.88,53.78-119.88,119.88s53.78,119.88,119.88,119.88c66.1,0,119.88-53.78,119.88-119.88S266.01,80.46,199.91,80.46z"></path><path fill="currentColor" d="M170.16,270.3c-3.33,0-6.67-0.83-9.69-2.51c-6.36-3.52-10.31-10.22-10.31-17.49v-99.9 c0-7.27,3.95-13.97,10.31-17.49c6.36-3.52,14.13-3.32,20.3,0.54l79.87,49.95c5.84,3.66,9.39,10.06,9.39,16.96 c0,6.89-3.55,13.3-9.39,16.96l-79.87,49.95C177.53,269.28,173.85,270.3,170.16,270.3z M190.16,186.49v27.7l22.15-13.85 L190.16,186.49z"></path></g></symbol><symbol id="icon-print-svg" viewBox="0 0 400 400"><g><path fill="#4E5463" d="M289.66,150.27H109.86V40.4h179.81V150.27z M149.86,110.27h99.81V80.4h-99.81V110.27z"></path><path fill="currentColor" d="M289.86,359.4h-180V196.07h180V359.4z M149.86,319.4h100v-83.33h-100V319.4z"></path><path fill="#4E5463" d="M359.98,290.4h-50.02v-40h10.02V150.27h-240V250.4H90v40H39.98V148.27c0-20.95,17.05-38,38-38h244 c20.95,0,38,17.05,38,38V290.4z"></path></g></symbol><symbol id="icon-transform-svg" viewBox="0 0 400 400"><g><path fill="#4E5463" d="M189.95,190.35H39.73V78.52c0-20.95,17.05-38,38-38h112.22V190.35z M79.73,150.35h70.22V80.52H79.73V150.35 z"></path><path fill="#4E5463" d="M322.04,360.63H209.82V210.81h150.22v111.83C360.04,343.59,342.99,360.63,322.04,360.63z M249.82,320.63 h70.22v-69.83h-70.22V320.63z"></path><polygon fill="currentColor" points="360.04,190.46 320.04,190.46 320.04,120.4 263.32,120.4 263.32,80.4 360.04,80.4 "></polygon><polygon fill="currentColor" points="136.45,320.5 39.73,320.5 39.73,210.43 79.73,210.43 79.73,280.5 136.45,280.5 "></polygon><polygon fill="currentColor" points="289.84,160.41 209.99,100.4 289.84,40.4 "></polygon><polygon fill="currentColor" points="110.09,240.49 189.94,300.5 110.09,360.5 "></polygon></g></symbol><symbol id="icon-export-svg" viewBox="0 0 400 400"><g><path fill="#4E5463" d="M310.34,350.34H89.57c-21.77,0-39.48-17.71-39.48-39.48V89.99c0-21.77,17.71-39.48,39.48-39.48h90.42v40 H90.09v219.83h219.73V210.01h40v100.86C349.81,332.63,332.11,350.34,310.34,350.34z"></path><path fill="currentColor" d="M219.95,260.4h-40v-102c0-20.95,17.05-38,38-38h111.96v40H219.95V260.4z"></path><path fill="currentColor" d="M329.91,160.4c-5.12,0-10.24-1.95-14.14-5.86l-69.8-69.79c-7.81-7.81-7.81-20.47,0-28.28 c7.81-7.81,20.47-7.81,28.28,0l69.8,69.79c7.81,7.81,7.81,20.47,0,28.28C340.15,158.44,335.03,160.4,329.91,160.4z"></path></g></symbol><symbol id="icon-back-svg" viewBox="0 0 400 400"><g><polygon fill="#4E5463" points="296.12,289.87 160.29,289.87 160.29,249.87 266.98,249.87 284.9,170.17 139.86,170.09 139.86,129.54 340.03,129.49"></polygon><polygon fill="currentColor" points="139.86,69.79 50.08,145.89 139.86,230.38"></polygon></g></symbol><symbol id="icon-disband-svg" viewBox="0 0 400 400"><path style="fill: currentColor;" d="M359.9,265.31l-27.92,28.31l27.86,28.49 M359.84,322.11l-28.21,28.35L303.9,322.1l-27.14,27.52l-28.34-28.23 l27.51-27.89l-27.08-27.69l28.21-28.35l26.95,27.56l27.55-27.94l28.34,28.23"></path><path style="fill: #4E5463;" d="M230.82,320.42H79.93V293.8l82.88-38.98c10-4.7,14.29-16.62,9.59-26.61l-24.47-51.09v-45.7 c0-28.72,23.28-52,52-52s52,23.28,52,52v46.29l-24.1,50.4c-4.09,8.71-1.35,18.88,6.04,24.45c9.71-22.05,27.65-39.66,49.92-48.94 l8.08-16.64l0.25,0.12v-44.16l-0.19-0.09v-11.44c0-50.81-41.19-92-92-92c-50.81,0-92,41.19-92,92v55.73l0.25-0.12l19.51,40.1 l-87.76,41.28v92.01H257.2C245.25,349.7,236.05,335.97,230.82,320.42z"></path></symbol></svg></div><div class="mini-tooltip mini-tooltip-theme-dark" style="border-width: 0px;"><div class="mini-tooltip-arrow"></div><div class="mini-tooltip-inner mini-corner-all"></div></div><input type="hidden" class="mini-hidden" id="_common_hidden_viewdata" value="{&quot;epoint_user_loginid&quot;:&quot;A9BDAF6D10D0D42A79CBB376A68947828E2E8E8265B8079B0E70907CCF7DB49A&quot;,&quot;CurWorkItemGuid&quot;:&quot;fac833dc-27e2-4182-8d80-415fd50c1bee&quot;,&quot;RowGuid&quot;:&quot;3174a84f-f734-47d6-8cad-f072efa63758&quot;,&quot;mainRowGuid&quot;:&quot;3174a84f-f734-47d6-8cad-f072efa63758&quot;,&quot;ui_control_visualconfig_first&quot;:&quot;true&quot;,&quot;MainPVGuid&quot;:&quot;32afafd0-623b-4495-8602-7baf3b609ef0&quot;,&quot;MainPVIGuid&quot;:&quot;4a69e45e-9a89-4506-ad12-5604d0380246&quot;,&quot;ProcessVersionInstanceGuid&quot;:&quot;4a69e45e-9a89-4506-ad12-5604d0380246&quot;,&quot;isadd&quot;:&quot;false&quot;,&quot;pageurl&quot;:&quot;6d34aa13e4f3c12d685a9e9b1b32ad178067d484da62e5f4376d7942f0b9f086916b0f883bf7694af5b08f0f2696c95c187a0c7201b3ab1273e000fbfa7ad410ee7a1f01053bfd85fa0e6c40bdacb11f00650396cd5ceed64011648501d8383583eb9d527e989c793f8e5b25cc8e5beeb10cd94867b87a2b5476a07d91d502901371097088a7fb34a3f47d65bbe6fe897685e12d53ad31943a27d420134b3641db93fc6c9c732e975ed9ed8ebff1556dc9331861aae56bfd8ea205a30c0f2c0639b16201585cc25258d835e3e12951d5afaff3a8e4a0abea416068129cbda24f5f1aaf562f1f4dbd40110a9c88dd8290e85823b74ae2c9cbe3f3ea01ba55d615&quot;,&quot;ui_control_visualconfig&quot;:&quot;[]&quot;,&quot;frame_ui_control_visualconfig&quot;:&quot;&quot;}"><div class="mini-panel mini-corner-all mini-window mini-window-drag mini-messagebox mini-panel-showheader" tabindex="0" id="mini-170" style="border-width: 0px; width: 360px; height: 143px; z-index: 1004; position: absolute; left: 819px; top: 519.5px;"><div class="mini-panel-border mini-corner-all"><div class="mini-panel-header" style="width: auto;"><div class="mini-panel-header-inner"><span class="mini-panel-icon mini-icon mini-iconfont " style="display: none;"></span><div class="mini-panel-title" title="提醒">提醒</div><div class="mini-tools"><span id="0" class="mini-icon mini-iconfont fa mini-tools-collapse " style=";display:none;"></span><span id="1" class="mini-icon mini-iconfont fa mini-tools-min " style=";display:none;"></span><span id="2" class="mini-icon mini-iconfont fa mini-tools-max " style=";display:none;"></span><span id="3" class="mini-icon mini-iconfont fa mini-tools-refresh " style=";display:none;"></span><span id="4" class="mini-icon mini-iconfont fa mini-tools-close " style=";"></span></div></div></div><div class="mini-panel-viewport" style="height: 96px;"><div class="mini-panel-toolbar" style="display: none;"></div><div class="mini-panel-body" style="overflow: hidden; height: 96px;"><div class="mini-messagebox-content" style=""><i class="mini-messagebox-icon mini-iconfont mini-messagebox-info" style=""></i><p class="mini-messagebox-msg">未匹配到该合同卖方，请核对数据。</p></div><div class="mini-messagebox-buttons"><a class="mini-button mini-corner-all mini-button-textOnly mini-messagebox-button ok mini-button-state-primary" href="javascript:void(0)" id="mini-171"><svg class="mini-button-loading-icon" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="128" height="128" fill="currentColor"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 *********** 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg><span class="mini-button-text">确定</span></a></div></div><div class="mini-panel-footer" style="display: none;"></div><div class="mini-resizer-trigger" style="display: none;"></div></div></div></div><div id="__modalmini-170" class="mini-modal mini-widget-content" style="display: block; z-index: 1003;"><iframe frameborder="0" style="position: absolute; z-index: -1; width: 100%; height: 100%; top: 0;left:0;scrolling:no;"></iframe></div></body></html>
