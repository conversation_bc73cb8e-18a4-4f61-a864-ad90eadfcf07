absl-py==2.1.0
aiofiles==23.2.1
aiohappyeyeballs==2.6.1
aiohttp==3.11.13
aiolimiter==1.2.1
aiosignal==1.3.2
alembic==1.12.0
altair==5.5.0
altgraph==0.17.4
aniso8601==10.0.0
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.8.0
APScheduler==3.10.4
astor==0.8.1
astroid==3.3.9
asttokens==3.0.0
async-timeout==5.0.1
atomicwrites==1.4.1
attrs==25.3.0
audioread==3.0.1
av==14.2.0
babel==2.17.0
baidusearch==1.0.3
bce-python-sdk==0.9.29
beartype==0.12.0
beautifulsoup4==4.13.3
blinker==1.9.0
bokeh==3.4.3
boltons==25.0.0
boto3==1.37.23
botocore==1.37.23
Bottleneck==1.4.2
braceexpand==0.1.7
browsergym==0.13.1
browsergym-assistantbench==0.13.1
browsergym-core==0.13.1
browsergym-experiments==0.13.1
browsergym-miniwob==0.13.1
browsergym-visualwebarena==0.13.1
browsergym-webarena==0.13.1
browsergym-workarena==0.4.1
build==0.10.0
certifi==2025.1.31
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.1
chattts==0.2.3
click==8.1.7
cloudpickle==3.1.1
cn2an==0.5.23
cobble==0.1.4
colorama==0.4.6
coloredlogs==15.0.1
colorlog==6.9.0
comm==0.2.2
contourpy==1.3.0
cryptography==40.0.2
cssselect==1.3.0
# cssutils==2.11.1
# ctranslate2==4.5.0
currency-symbols==2.0.3
cycler==0.12.1
Cython==3.0.12
dac==0.4.2
dataclasses-json==0.6.7
DataRecorder==3.6.2
datasets==3.4.0
debugpy==1.8.13
decorator==5.2.1
Deprecated==1.2.18
dill==0.3.4
Distance==0.1.3
distro==1.9.0
dnspython==2.7.0
docker==7.1.0
docopt==0.6.2
DownloadKit==2.0.7
DrissionPage==********
dtw-python==1.5.3
duckduckgo_search==7.5.5
edge-tts==7.0.0
editdistance==0.8.1
einops==0.8.1
einx==0.3.0
email-validator==2.0.0
encodec==0.1.1
eng_to_ipa==0.0.2
english-words==2.0.1
et_xmlfile==2.0.0
evaluate==0.4.3
exceptiongroup==1.2.2
executing==2.2.0
Faker==37.1.0
Farama-Notifications==0.0.4
fastapi==0.115.11
fastdtw==0.3.4
faster-whisper==1.1.1
ffmpy==0.5.0
filelock==3.17.0
Flask==2.0.1
flask-babel==4.0.0
Flask-Cors==3.0.10
Flask-JWT-Extended==4.3.1
Flask-Limiter==3.5.0
Flask-Login==0.6.3
Flask-Mail==0.10.0
Flask-Migrate==4.0.5
Flask-RESTful==0.3.9
Flask-SQLAlchemy==2.5.1
Flask-WTF==1.2.2
flatbuffers==25.2.10
fonttools==4.56.0
frozendict==2.4.6
frozenlist==1.5.0
fsspec==2024.12.0
ftfy==6.3.1
future==1.0.0
g2p-en==2.1.0
g2pM==*******
gitdb==4.0.12
GitPython==3.1.44
googlesearch-python==1.3.0
gradio==4.44.1
gradio_client==1.3.0
greenlet==3.1.1
gTTS==2.5.4
gunicorn==20.1.0
gymnasium==1.1.1
h11==0.14.0
h5py==3.13.0
html2text==2024.2.26
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.29.3
humanfriendly==10.0
HyperPyYAML==1.2.2
idna==3.10
imageio==2.37.0
importlib_metadata==8.6.1
importlib_resources==6.5.2
inflect==7.5.0
iniconfig==2.1.0
intel-openmp==2021.4.0
intervaltree==3.1.0
ipykernel==6.29.5
ipython==8.18.1
ipywidgets==8.1.5
isort==6.0.1
itsdangerous==2.2.0
jedi==0.19.2
jieba==0.42.1
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.4.2
jsonlines==4.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyterlab_widgets==3.0.13
kaldiio==2.18.1
kiwisolver==1.4.7
langid==1.1.6
lazy_loader==0.4
librosa==0.11.0
libvisualwebarena==0.0.15
libwebarena==0.0.4
limits==4.2
linkify-it-py==2.0.3
llvmlite==0.43.0
loguru==0.7.3
lxml==5.3.1
Mako==1.3.9
mammoth==1.9.0
markdown-it-py==2.2.0
MarkupSafe==2.1.5
marshmallow==3.26.1
matplotlib==3.9.4
matplotlib-inline==0.1.7
mccabe==0.7.0
mdit-py-plugins==0.3.3
mdurl==0.1.2
mido==1.3.3
mkl==2021.4.0
mock==5.2.0
more-itertools==10.6.0
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
multiprocess==*********
mypy-extensions==1.0.0
# MyShell-OpenVoice @ git+https://github.com/myshell-ai/OpenVoice.git@bb79fa78a5a7a7a3d7602b7f6d48705213a039c7
nara-wpe==0.0.11
narwhals==1.31.0
nest-asyncio==1.6.0
networkx==3.2.1
nltk==3.9.1
noisereduce==3.0.3
note-seq==0.0.3
# numba==0.60.0
numpy==1.26.4
omegaconf==2.3.0
onnx==1.17.0
# onnxruntime==1.19.2
# openai==1.66.3
# openai-whisper==20240930
# OpenCC==1.1.9
# opencc-python-reimplemented==0.1.7
# opencv-python==********
# openpyxl==3.0.9
# opt-einsum==3.3.0
# ordered-set==4.1.0
# orjson==3.10.15
# outcome==1.3.0.post0
# packaging==24.2
# paddle2onnx==1.3.1
# paddleaudio==1.1.0
# paddlefsl==1.1.0
# paddlenlp==2.6.1
# paddlepaddle==2.6.2
# paddlesde==0.2.5
# paddleslim==2.6.0
# paddlespeech==1.4.1
# paddlespeech-feat==0.1.0
pandas==1.3.3
parameterized==0.9.0
parso==0.8.4
pathos==0.2.8
pattern_singleton==1.2.0
pdfminer.six==20231228
pdfplumber==0.11.5
pefile==2023.2.7
pillow==10.4.0
platformdirs==4.3.6
playwright==1.51.0
pluggy==1.5.0
pooch==1.8.2
portalocker==3.1.1
pox==0.3.5
ppdiffusers==0.19.4
ppft==1.7.6.9
praatio==5.1.1
pretty_midi==0.2.10
prettytable==3.15.1
primp==0.14.0
proces==0.1.7
prompt_toolkit==3.0.50
propcache==0.3.0
protobuf==6.30.0
psutil==5.9.5
pure_eval==0.2.3
py==1.11.0
pyarrow==19.0.1
pybase16384==0.3.8
pybind11==2.13.6
pycparser==2.22
pycryptodome==3.21.0
pydantic==1.7.3
pydantic_core==2.27.2
pydev==0.0.1
pydub==0.25.1
pyee==12.1.1
Pygments==2.19.1
pyinstaller==6.12.0
pyinstaller-hooks-contrib==2025.2
PyJWT==2.1.0
pylint==3.3.5
pypandoc==1.15
pyparsing==3.2.1
PyPDF2==3.0.1
pypdfium2==4.30.1
pypinyin==0.53.0
pypinyin-dict==0.9.0
pyproject_hooks==1.2.0
pyreadline3==3.5.4
pyrubberband==0.4.0
PySocks==1.7.1
pytest==6.2.5
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.1.0
python-magic==0.4.27
python-multipart==0.0.20
pytz==2025.1
pywin32==308
pywin32-ctypes==0.2.3
pyworld==0.3.2
PyYAML==6.0.2
pyzmq==26.3.0
rarfile==4.2
records==0.6.0
redis==5.0.0
referencing==0.36.2
regex==2024.11.6
requests==2.31.0
requests-file==2.1.0
requests-mock==1.12.1
resampy==0.4.3
Resemblyzer==0.1.4
rich==13.9.4
rpds-py==0.23.1
ruamel.yaml==0.18.10
ruamel.yaml.clib==0.2.12
ruff==0.9.10
s3transfer==0.11.4
sacrebleu==2.5.1
safetensors==0.5.3
scikit-image==0.24.0
scikit-learn==1.6.1
scipy==1.13.1
selenium==4.31.0
semantic-version==2.10.0
sentencepiece==0.2.0
seqeval==1.2.2
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
sortedcontainers==2.4.0
soundfile==0.13.1
soupsieve==2.6
soxr==0.5.0.post1
SQLAlchemy==1.4.23
srt==3.5.3
stack-data==0.6.3
starlette==0.46.1
swig==4.3.0
sympy==1.13.1
tablib==3.8.0
tabulate==0.9.0
tbb==2021.13.1
tenacity==9.0.0
text-generation==0.7.0
TextGrid==1.6.1
threadpoolctl==3.5.0
tifffile==2024.8.30
tiktoken==0.9.0
timer==0.3.0
tldextract==5.2.0
ToJyutping==3.2.0
tokenizers==0.21.1
toml==0.10.2
tomli==2.2.1
tomlkit==0.12.0
torch==2.6.0
torchaudio==2.6.0
torchvision==0.21.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
trampoline==0.1.2
transformers==4.49.0
trio==0.29.0
trio-websocket==0.12.2
typeguard==4.4.2
typer==0.15.2
types-requests==********
types-tqdm==4.67.0.20250319
types-urllib3==**********
typing==*******
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
tzlocal==5.3.1
uc-micro-py==1.0.3
Unidecode==1.3.8
unidiff==0.7.5
urllib3==2.3.0
uv==0.6.13
uvicorn==0.34.0
vector-quantize-pytorch==1.22.2
visualdl==2.5.3
vocos==0.1.0
Wand==0.6.13
watchdog==6.0.0
wavmark==0.0.3
wcwidth==0.2.13
webcolors==24.11.1
webdriver-manager==4.0.2
webrtcvad==2.0.10
websocket-client==1.8.0
websockets==11.0.3
Werkzeug==2.0.1
wget==3.2
whisper-timestamped==1.15.8
widgetsnbextension==4.0.13
win32_setctime==1.2.0
wrapt==1.17.2
wsproto==1.2.0
WTForms==3.2.1
xlrd==2.0.1
xls2xlsx==0.2.0
xxhash==3.5.0
xyzservices==2025.1.0
yacs==0.1.8
yarl==1.18.3
zhon==2.1.1
zipp==3.21.0
