import os
import re
from glob import glob
import logging
from win32com.shell import shell, shellcon
from collections import defaultdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('delete_duplicate_contracts.log', encoding='utf-8'),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

# 支持的文件扩展名
EXTS = ['.doc', '.docx', '.pdf']
CONTRACT_DIR = r'D:\user\合同汇总'

# 合同编号正则
PATTERNS = [
    re.compile(r'ZC\d{2}-A\d{3}'),
    # 新规则：CRRC+2位数字+1位字母或数字+3位数字+1位字母或数字+7位数字
    re.compile(r'CRRC\d{2}[A-Z0-9]\d{3}[A-Z0-9]\d{7}'),
]


def move_to_recycle_bin(filepath):
    """移动文件到回收站"""
    if os.path.exists(filepath):
        try:
            res = shell.SHFileOperation(
                (
                    0,
                    shellcon.FO_DELETE,
                    filepath,
                    None,
                    shellcon.FOF_SILENT
                    | shellcon.FOF_ALLOWUNDO
                    | shellcon.FOF_NOCONFIRMATION,
                    None,
                    None,
                )
            )
            if res[1] != 0:
                logger.error(f'删除文件{filepath}失败')
                return False
            else:
                logger.info(f'删除文件{filepath}成功')
                return True
        except Exception as e:
            logger.error(f'删除文件失败 {filepath}: {e}')
            return False
    else:
        logger.warning(f'文件：{filepath}不存在！')
        return False


def extract_contract_no_from_filename(filename):
    """从文件名中提取合同号"""
    for pat in PATTERNS:
        m = pat.search(filename)
        if m:
            return m.group(0)
    return None


def main():
    """主函数：处理重复合同文件"""
    logger.info('开始处理重复合同文件')

    # 清空日志文件
    log_path = os.path.join(os.path.dirname(__file__), 'delete_duplicate_contracts.log')
    with open(log_path, 'w', encoding='utf-8'):
        pass

    # 获取所有文件
    files = []
    for ext in EXTS:
        files.extend(glob(os.path.join(CONTRACT_DIR, f'*{ext}')))

    total_files = len(files)
    logger.info(f'找到 {total_files} 个文件需要处理')

    if total_files == 0:
        logger.warning(f'目录 {CONTRACT_DIR} 中没有找到需要处理的文件')
        return
    # 按合同编号分组
    contract_groups = defaultdict(list)
    no_match_files = []

    for file in files:
        basename = os.path.basename(file)
        contract_no = extract_contract_no_from_filename(basename)

        if contract_no:
            contract_groups[contract_no].append(file)
        else:
            no_match_files.append(file)
            logger.debug(f'文件不符合正则规则，跳过处理: {basename}')

    logger.info(f'找到 {len(contract_groups)} 个不同的合同编号')
    logger.info(f'有 {len(no_match_files)} 个文件不符合正则规则')

    # 处理每个合同组
    deleted_count = 0
    for contract_no, group_files in contract_groups.items():
        if len(group_files) <= 1:
            logger.debug(f'合同编号 {contract_no} 只有一个文件，无需处理')
            continue

        # 将文件分为PDF和非PDF两组
        pdf_files = [f for f in group_files if f.lower().endswith('.pdf')]
        non_pdf_files = [f for f in group_files if not f.lower().endswith('.pdf')]

        files_to_keep = []
        files_to_delete = []

        # 如果有PDF文件，优先保留PDF文件
        if pdf_files:
            logger.info(f'合同编号 {contract_no} 有 {len(pdf_files)} 个PDF文件')

            if len(pdf_files) == 1:
                # 只有一个PDF文件，直接保留
                files_to_keep.append(pdf_files[0])
            else:
                # 多个PDF文件，保留basename最长的
                pdf_files.sort(key=lambda x: len(os.path.basename(x)), reverse=True)
                files_to_keep.append(pdf_files[0])
                files_to_delete.extend(pdf_files[1:])

            # 所有非PDF文件都删除
            files_to_delete.extend(non_pdf_files)
        else:
            # 没有PDF文件，保留basename最长的非PDF文件
            logger.info(
                f'合同编号 {contract_no} 没有PDF文件，共有 {len(non_pdf_files)} 个非PDF文件'
            )
            non_pdf_files.sort(key=lambda x: len(os.path.basename(x)), reverse=True)
            files_to_keep.append(non_pdf_files[0])
            files_to_delete.extend(non_pdf_files[1:])

        # 记录保留的文件
        for file in files_to_keep:
            keep_basename = os.path.basename(file)
            logger.info(f'保留文件: {keep_basename}')

        # 删除其他文件
        for file in files_to_delete:
            basename = os.path.basename(file)
            logger.info(f'移动到回收站: {basename}')
            if move_to_recycle_bin(file):
                deleted_count += 1

    logger.info(f'处理完成！共删除 {deleted_count} 个重复文件')


if __name__ == '__main__':
    main()
