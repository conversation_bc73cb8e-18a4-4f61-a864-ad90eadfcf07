#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-06-07 07:15:10
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-07 07:49:00
# 文件相对于项目的路径   : \Crawl_AI\OCR转换.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
import os
import sys
from pdf2docx import Converter
from pdf2image import convert_from_path
import pytesseract
from docx import Document
from tqdm import tqdm
import win32com.client


def ocr_pdf_to_word(pdf_path, docx_path):
    """处理扫描版PDF（图形内容）"""
    doc = Document()
    images = convert_from_path(pdf_path, dpi=300)

    print(f'正在处理 {os.path.basename(pdf_path)}:')
    for i, img in enumerate(tqdm(images, desc='OCR进度')):
        text = pytesseract.image_to_string(img, lang='chi_sim+eng')
        doc.add_paragraph(text)
        if i == 0:  # 首页添加水印说明
            doc.add_paragraph('\n※本文件通过OCR技术生成，请核对内容准确性※')

    doc.save(docx_path)
    print(f'已保存到 {docx_path}')


def normal_pdf_to_word(pdf_path, docx_path):
    """处理可编辑PDF（文本内容）"""
    cv = Converter(pdf_path)
    cv.convert(docx_path, start=0, end=None)
    cv.close()
    print(f'转换完成: {os.path.basename(docx_path)}')


def acrobat_pdf_to_word(pdf_path, docx_path):
    """使用Adobe Acrobat Pro自动将PDF（含OCR）转换为Word"""
    try:
        app = win32com.client.Dispatch('AcroExch.App')
        avDoc = win32com.client.Dispatch('AcroExch.AVDoc')
        if not avDoc.Open(pdf_path, ''):
            print('无法打开PDF文件')
            return
        pdDoc = avDoc.GetPDDoc()
        jsObj = pdDoc.GetJSObject()
        jsObj.SaveAs(docx_path, 'com.adobe.acrobat.docx')
        avDoc.Close(True)
        app.Exit()
        print(f'已用Adobe Acrobat Pro转换并保存到: {docx_path}')
    except Exception as e:
        print(f'Adobe Acrobat Pro转换失败: {e}')


if __name__ == '__main__':
    pdf_path = r'D:\user\PythonProject\Crawl_AI\集装箱生产专用叉车租赁合同.pdf'
    docx_path = os.path.splitext(pdf_path)[0] + '_converted.docx'
    try:
        acrobat_pdf_to_word(pdf_path, docx_path)
    except Exception as e:
        print(f'转换失败: {str(e)}')
        sys.exit(1)
