#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-06-13 18:36:58
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-15 14:38:39
# 文件相对于项目的路径   : \Crawl_AI\acrobat_batch_ocr_optimized.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
import os
import time
import win32com.client
import concurrent.futures
import logging
from tqdm import tqdm
from PDF转换 import read_pdf_text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('acrobat_ocr.log', encoding='utf-8'),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

# 配置参数
PDF_DIR = r'D:\user\合同汇总'
OUTPUT_DIR = r'D:\user\合同汇总\ocr_result'
MAX_WORKERS = 4  # 并行线程数，可以根据CPU核心数调整

# 确保输出目录存在
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)
    logger.info(f'创建输出目录: {OUTPUT_DIR}')


def check_pdf_editable(pdf_path):
    """
    检查PDF文件是否可编辑

    Args:
        pdf_path: PDF文件路径

    Returns:
        tuple: (pdf_path, is_editable)
    """
    try:
        is_editable = read_pdf_text(pdf_path)
        return pdf_path, is_editable
    except Exception as e:
        logger.error(f'检测PDF可编辑性失败: {pdf_path}, 错误: {e}')
        return pdf_path, False


def process_pdf_with_ocr(pdf_path, output_path):
    """
    使用Acrobat对PDF文件进行OCR处理

    Args:
        pdf_path: 输入PDF文件路径
        output_path: 输出PDF文件路径

    Returns:
        bool: 处理是否成功
    """
    try:
        acrobat = win32com.client.Dispatch('AcroExch.App')
        avdoc = win32com.client.Dispatch('AcroExch.AVDoc')

        if not avdoc.Open(pdf_path, ''):
            logger.error(f'无法打开: {pdf_path}')
            return False

        acrobat.Show()  # 可选，让窗口可见
        avdoc.BringToFront()

        # 执行OCR命令（菜单命令可能因语言不同而不同）
        try:
            avdoc.ExecuteThisJavaScript('app.execMenuItem("RecognizeTextInThisFile")')
        except Exception:
            try:
                avdoc.ExecuteThisJavaScript(
                    'app.execMenuItem("TextRecognition:RecognizeTextInThisFile")'
                )
            except Exception as e2:
                logger.error(f'无法执行OCR命令: {os.path.basename(pdf_path)}, {e2}')
                avdoc.Close(True)
                return False

        # 等待OCR完成（简单sleep，可根据实际情况优化）
        time.sleep(10)  # 视文件大小调整

        # 保存为新文件
        pdDoc = avdoc.GetPDDoc()
        save_result = pdDoc.Save(1, output_path)
        avdoc.Close(True)

        if save_result:
            logger.info(
                f'完成: {os.path.basename(pdf_path)} -> {os.path.basename(output_path)}'
            )
            return True
        else:
            logger.error(f'保存失败: {os.path.basename(output_path)}')
            return False

    except Exception as e:
        logger.error(f'处理失败: {os.path.basename(pdf_path)}, 错误: {e}')
        return False


def main():
    """主函数"""
    start_time = time.time()
    logger.info('开始批量PDF OCR处理')

    # 获取所有PDF文件
    pdf_files = [f for f in os.listdir(PDF_DIR) if f.lower().endswith('.pdf')]
    total_files = len(pdf_files)
    logger.info(f'找到 {total_files} 个PDF文件')

    if total_files == 0:
        logger.warning(f'目录 {PDF_DIR} 中没有找到PDF文件')
        return

    # 并行检测PDF文件是否可编辑
    logger.info(f'使用 {MAX_WORKERS} 个线程并行检测PDF文件是否可编辑')
    non_editable_pdfs = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 提交所有任务
        future_to_pdf = {
            executor.submit(
                check_pdf_editable, os.path.join(PDF_DIR, pdf_name)
            ): pdf_name
            for pdf_name in pdf_files
        }

        # 使用tqdm显示进度
        with tqdm(total=total_files, desc='检测PDF可编辑性') as pbar:
            for future in concurrent.futures.as_completed(future_to_pdf):
                pdf_name = future_to_pdf[future]
                try:
                    pdf_path, is_editable = future.result()
                    if not is_editable:
                        non_editable_pdfs.append(pdf_name)
                        logger.debug(f'需要OCR处理: {pdf_name}')
                    else:
                        logger.debug(f'跳过(可编辑): {pdf_name}')
                except Exception as e:
                    logger.error(f'处理 {pdf_name} 时发生错误: {e}')
                finally:
                    pbar.update(1)

    # 统计需要OCR处理的文件数量
    ocr_count = len(non_editable_pdfs)
    logger.info(f'检测完成，共有 {ocr_count} 个文件需要OCR处理')

    if ocr_count == 0:
        logger.info('没有需要OCR处理的文件，程序结束')
        return

    # 单线程进行OCR处理
    logger.info('开始单线程OCR处理')
    acrobat = None

    try:
        # 启动Acrobat应用
        acrobat = win32com.client.Dispatch('AcroExch.App')

        # 使用tqdm显示进度
        with tqdm(total=ocr_count, desc='OCR处理') as pbar:
            for pdf_name in non_editable_pdfs:
                pdf_path = os.path.join(PDF_DIR, pdf_name)
                output_path = os.path.join(
                    OUTPUT_DIR, os.path.splitext(pdf_name)[0] + '_ocr.pdf'
                )

                logger.info(f'处理: {pdf_name}')

                if success:
                    logger.info(f'OCR处理成功: {pdf_name}')
                else:
                    logger.error(f'OCR处理失败: {pdf_name}')

                pbar.update(1)

    except Exception as e:
        logger.error(f'OCR处理过程中发生错误: {e}')
    finally:
        # 关闭Acrobat
        if acrobat:
            try:
                acrobat.Exit()
                logger.info('已关闭Acrobat应用')
            except:
                logger.warning('关闭Acrobat应用时发生错误')

    # 计算总耗时
    end_time = time.time()
    total_time = end_time - start_time
    logger.info(f'批量OCR处理完成！总耗时: {total_time:.2f} 秒')


if __name__ == '__main__':
    main()
