from playwright.sync_api import sync_playwright
import pandas as pd
from time import sleep
import logging
import time
from ddddocr import DdddOcr
import numpy as np
import cv2

# 配置日志
logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def format_time_diff(seconds):
    """将秒数转换为人类易读的格式"""
    minutes, seconds = divmod(seconds, 60)
    hours, minutes = divmod(minutes, 60)

    if hours > 0:
        return f'{int(hours)}小时{int(minutes)}分钟{seconds:.2f}秒'
    elif minutes > 0:
        return f'{int(minutes)}分钟{seconds:.2f}秒'
    else:
        return f'{seconds:.2f}秒'


def enhance_captcha_image(image_bytes):
    """增强验证码图片以提高识别率"""
    # 将字节数据转换为numpy数组
    nparr = np.frombuffer(image_bytes, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 自适应阈值二值化
    binary = cv2.adaptiveThreshold(
        gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
    )

    # 降噪
    denoised = cv2.fastNlMeansDenoising(binary)

    # 转回字节流
    success, buffer = cv2.imencode('.png', denoised)
    if success:
        return buffer.tobytes()
    return image_bytes


def scrape_data(frame_locator):
    """抽取iframe内的表格数据"""
    data = []

    try:
        # 等待表格加载
        logger.info('等待表格加载...')
        # 尝试定位表格 - 根据您提供的HTML使用mini-grid-table选择器
        # 正确使用FrameLocator对象
        # 不等待表格可见，因为表格可能是隐藏的，而是等待表格存在
        frame_locator.locator('table.mini-grid-table').first.wait_for(
            state='attached', timeout=10000
        )

        # 等待表格行存在
        frame_locator.locator('.mini-grid-row').first.wait_for(
            state='attached', timeout=10000
        )

        # 获取表头 - 根据您提供的HTML代码更新选择器
        headers = []
        try:
            # 使用精确的选择器获取表头单元格，直接定位到span.mini-grid-headertxt
            header_cells = frame_locator.locator('span.mini-grid-headertxt').all()
            logger.info(f'找到 {len(header_cells)} 个表头单元格')

            for cell in header_cells:
                text = cell.inner_text().strip()
                # 过滤掉空文本和只包含特殊字符的文本
                if (
                    text
                    and text != ''
                    and not text.startswith('\n')
                    and not text == '\ue603'
                ):
                    headers.append(text)

            # 如果没有找到表头，尝试使用title属性
            if not headers or len(headers) < 5:  # 如果找到的表头少于5个，可能是不完整的
                logger.warning('使用默认选择器未找到足够的表头，尝试使用title属性')
                # 使用title属性获取表头
                headers = []
                header_cells = frame_locator.locator(
                    '.mini-grid-headerCell-inner[title]'
                ).all()
                logger.info(f'找到 {len(header_cells)} 个带title属性的表头单元格')

                for cell in header_cells:
                    # 获取title属性值
                    title = cell.get_attribute('title')
                    if title and title.strip() != '':
                        headers.append(title.strip())

            # 如果还是没有找到足够的表头，使用预定义的表头
            if not headers or len(headers) < 5:
                logger.warning('使用title属性仍未找到足够的表头，使用预定义的表头')
                # 根据您提供的HTML结构，使用预定义的表头
                headers = [
                    '选择',
                    '操作',
                    '序',
                    '状态',
                    '合同编号',
                    '集团统一编码',
                    '合同名称',
                    '下一步审批人',
                    '乙方名称',
                    '买方经办人',
                    '合同截止日期',
                    '合同签订时间',
                    '合同金额(含税)',
                    '合同金额(不含税)',
                    '合同分类',
                    '单据提报人',
                    '审核状态',
                    'mq推送状态',
                    'mq接收状态',
                ]
                logger.info(f'使用预定义的表头，共 {len(headers)} 个')
            else:
                # 确保表头第一列是"选择"，因为这是复选框列
                if len(headers) > 0 and headers[0] != '选择':
                    logger.info('在表头开头添加"选择"列')
                    headers.insert(0, '选择')
        except Exception as e:
            logger.error(f'获取表头时出错: {str(e)}')

        logger.info(f'表头: {headers}')
        if headers:
            data.append(headers)  # 添加表头作为第一行
        else:
            logger.warning('未找到表头，将使用默认列名')
            # 使用默认列名
            default_headers = [
                '序号',
                '操作',
                '状态',
                '合同编号',
                '合同名称',
                '乙方名称',
                '合同金额',
                '签订日期',
                '截止日期',
            ]
            data.append(default_headers)

        # 获取表格数据行
        try:
            # 尝试不同的选择器来获取表格行
            rows = frame_locator.locator('.mini-grid-row').all()
            logger.info(f'找到 {len(rows)} 行数据')

            # 如果没有找到行，尝试其他选择器
            if len(rows) == 0:
                logger.warning('使用默认选择器未找到表格行，尝试备用选择器')
                rows = frame_locator.locator('tr.mini-grid-row').all()
                logger.info(f'使用备用选择器找到 {len(rows)} 行数据')

            # 如果还是没有找到行，尝试更通用的选择器
            if len(rows) == 0:
                logger.warning('使用备用选择器仍未找到表格行，尝试更通用的选择器')
                rows = frame_locator.locator('tbody tr').all()
                logger.info(f'使用通用选择器找到 {len(rows)} 行数据')

            for row in rows:
                row_data = []
                try:
                    # 尝试不同的选择器来获取单元格
                    cells = row.locator('.mini-grid-cell-inner').all()

                    # 如果没有找到单元格，尝试其他选择器
                    if len(cells) == 0:
                        cells = row.locator('td div').all()

                    # 如果还是没有找到单元格，尝试更通用的选择器
                    if len(cells) == 0:
                        cells = row.locator('td').all()

                    for cell in cells:
                        text = cell.inner_text().strip()
                        row_data.append(text)

                    if (
                        any(row_data) and len(row_data) > 1
                    ):  # 确保行不是空的且有多个单元格
                        data.append(row_data)
                except Exception as e:
                    logger.error(f'处理表格行时出错: {str(e)}')
                    continue

            logger.info(f'成功抓取 {len(data) - 1} 行数据')
        except Exception as e:
            logger.error(f'获取表格行时出错: {str(e)}')
    except Exception as e:
        logger.error(f'抓取表格数据时出错: {str(e)}')
        # 不需要截图，直接记录错误信息即可

    return data


def main():
    # 记录程序开始时间
    start_time = time.time()
    # 创建字典记录各阶段耗时
    time_stats = {
        '登录': 0,
        '导航到电子采购': 0,
        '数据抓取': 0,
        '数据处理与保存': 0,
        '总耗时': 0,
    }

    try:
        # 记录阶段开始时间
        stage_start_time = time.time()
        with sync_playwright() as p:
            # 启动浏览器
            browser = p.chromium.launch(
                headless=False
            )  # 设置headless=True可以在后台运行
            # 创建一个最大化的浏览器窗口
            context = browser.new_context(viewport={'width': 1920, 'height': 1080})
            page = context.new_page()

            # 访问登录页面
            logger.info('访问登录页面...')
            try:
                page.goto(
                    'https://sso.crrcgo.cc/login?returnUrl=https%3A%2F%2Fwww.crrcgo.cc%2F%23%2FhomePage',
                    timeout=120000,
                    wait_until='networkidle',
                )
                # 确保页面完全加载
                page.wait_for_load_state('domcontentloaded')
                page.wait_for_load_state('networkidle')
                # 等待登录表单元素可见
                page.wait_for_selector('form', state='visible', timeout=30000)
                logger.info('登录页面加载成功')
            except Exception as e:
                logger.error(f'访问登录页面时出错: {str(e)}')
                # 尝试重新加载页面
                logger.info('尝试重新加载页面...')
                try:
                    page.reload(timeout=120000, wait_until='networkidle')
                    page.wait_for_selector('form', state='visible', timeout=30000)
                    logger.info('页面重新加载成功')
                except Exception as reload_error:
                    logger.error(f'重新加载页面时出错: {str(reload_error)}')
                    raise Exception('无法加载登录页面，请检查网络连接')

            # 等待用户名和密码输入框可用
            page.wait_for_selector("input[name='username']", state='visible')
            page.wait_for_selector("input[name='password']", state='visible')

            # 清空并填写登录信息
            page.fill("input[name='username']", '')  # 先清空
            page.fill("input[name='username']", '010800006291')
            page.fill("input[name='password']", '')  # 先清空
            page.fill("input[name='password']", 'Z6h2en91@')

            # 处理验证码，最多尝试五次
            max_captcha_attempts = 5
            login_success = False

            # 初始化 OCR 对象（只初始化一次）
            ocr = DdddOcr()

            for attempt in range(max_captcha_attempts):
                logger.info(f'处理验证码... 第 {attempt + 1} 次尝试')

                try:
                    # 等待验证码输入框和图片加载
                    page.wait_for_selector(
                        "input[name='validCode']", state='visible', timeout=10000
                    )
                    page.wait_for_selector('form img', state='visible', timeout=10000)

                    # 确保验证码图片完全加载
                    sleep(2)

                    # 尝试定位验证码图片
                    captcha_img = None
                    for selector in [
                        'form img',
                        "img[src*='validCode']",
                        "img[src*='captcha']",
                    ]:
                        try:
                            element = page.locator(selector).first
                            if element:
                                captcha_img = element.screenshot()
                                logger.info(f'使用选择器 {selector} 成功获取验证码图片')
                                break
                        except Exception:
                            continue

                    if not captcha_img:
                        raise Exception('无法获取验证码图片')

                    # 使用ddddocr识别验证码
                    logger.info('开始识别验证码...')
                    # 增强验证码图片
                    processed_captcha = enhance_captcha_image(captcha_img)
                    code = ocr.classification(processed_captcha)

                    if not code or len(code.strip()) != 4:
                        logger.warning(f'验证码识别结果异常: {code}')
                        raise Exception('验证码识别结果异常')

                    code = code.strip().upper()  # 转换为大写
                    logger.info(f'验证码识别结果: {code}')

                    # 清空并填写验证码
                    page.fill("input[name='validCode']", '')  # 先清空
                    page.fill("input[name='validCode']", code)
                    sleep(1)  # 等待验证码填写完成

                    # 点击登录按钮
                    page.click('input.loginBtn')

                    # 检查登录结果
                    try:
                        # # 等待可能的错误提示
                        # error_message = page.wait_for_selector(
                        #     ".el-message--error, .el-message--warning",
                        #     state="visible",
                        #     timeout=3000,
                        # )
                        # if error_message:
                        #     error_text = error_message.inner_text()
                        #     logger.warning(f"登录失败: {error_text}")
                        #     # 刷新验证码
                        #     page.locator("form img").click()
                        #     sleep(2)
                        #     continue

                        # 等待登录成功的标志
                        success = page.wait_for_selector(
                            "span[slot='reference']:has-text('电子采购')",
                            timeout=5000,
                            state='visible',
                        )
                        if success:
                            logger.info('登录成功!')
                            login_success = True
                            break

                    except Exception:
                        # 如果超时未找到错误提示或成功标志，刷新验证码重试
                        logger.warning('登录状态未知，尝试刷新验证码')
                        page.locator('form img').click()
                        sleep(2)

                except Exception as e:
                    logger.error(f'验证码处理过程出错: {str(e)}')
                    if attempt < max_captcha_attempts - 1:
                        # 刷新页面重试
                        logger.info('刷新页面重试...')
                        page.reload(timeout=60000, wait_until='networkidle')
                        page.wait_for_selector('form', state='visible', timeout=30000)
                        sleep(3)
                        # 重新填写登录信息
                        page.fill("input[name='username']", '010800006291')
                        page.fill("input[name='password']", 'Z6h2en91@')

            # 检查是否登录成功
            if not login_success:
                raise Exception('多次尝试后仍然无法登录，请稍后重试')

            # 记录登录阶段耗时
            login_end_time = time.time()
            time_stats['登录'] = login_end_time - stage_start_time
            logger.info(f'登录阶段耗时: {format_time_diff(time_stats["登录"])}')

            # 更新阶段开始时间
            stage_start_time = time.time()

            # 等待登录成功并导航
            logger.info('等待登录成功...')
            page.wait_for_load_state('networkidle')

            # 导航到电子采购页面
            logger.info('导航到电子采购页面...')
            # 等待页面完全加载
            page.wait_for_load_state('networkidle')
            # 点击电子采购元素，这会打开新页面
            with page.expect_popup() as popup_info:
                page.click("span[slot='reference']:has-text('电子采购')")
            # 获取新打开的页面
            new_page = popup_info.value
            # 切换到新页面
            page = new_page
            # 等待新页面加载
            page.wait_for_load_state('networkidle')
            # 添加额外的等待时间，以处理可能的延迟
            sleep(3)

            # 点击侧边栏中的"合同管理"菜单
            logger.info('点击合同管理菜单...')
            # 使用更精确的选择器，点击包含合同管理的菜单项
            page.click("div.menu-item[data-code='9504'] > a.menu-link")
            # 等待一下以确保子菜单展开
            sleep(2)

            # 点击子菜单"采购合同管理"
            logger.info('点击采购合同管理子菜单...')
            page.click("div.menu-item[data-code='95040005'] > a.menu-link")
            # 等待页面加载
            page.wait_for_load_state('networkidle')
            # 添加额外的等待时间
            sleep(3)

            # 处理iframe
            logger.info('切换到iframe内部...')
            # 等待iframe加载
            iframe_selector = 'iframe#tab-content-95040005'
            page.wait_for_selector(iframe_selector, state='visible')
            # 获取iframe元素
            iframe = page.frame_locator(iframe_selector)

            # 记录导航到电子采购阶段耗时
            navigation_end_time = time.time()
            time_stats['导航到电子采购'] = navigation_end_time - stage_start_time
            logger.info(
                f'导航到电子采购阶段耗时: {format_time_diff(time_stats["导航到电子采购"])}'
            )

            # 更新阶段开始时间
            stage_start_time = time.time()

            # 收集所有数据
            all_data = []
            page_num = 1

            # 获取总条数和计算最大页数
            try:
                # 等待分页信息元素加载
                pagination_info = iframe.locator('.pagination-pagerinfo').first
                pagination_info.wait_for(state='attached', timeout=5000)
                total_text = pagination_info.inner_text()
                # 从文本中提取总条数，例如从"共253条"中提取"253"
                import re

                total_count_match = re.search(r'\d+', total_text)
                if total_count_match:
                    total_count = int(total_count_match.group())
                    # 默认每页显示20条
                    items_per_page = 20
                    max_pages = (
                        total_count + items_per_page - 1
                    ) // items_per_page  # 向上取整
                    logger.info(f'总条数: {total_count}, 最大页数: {max_pages}')
                else:
                    # 如果无法获取总条数，设置一个默认的最大页数
                    max_pages = 100
                    logger.warning(f'无法获取总条数，设置默认最大页数为 {max_pages}')
            except Exception as e:
                # 如果出错，设置一个默认的最大页数
                max_pages = 100
                logger.warning(
                    f'获取总条数时出错: {str(e)}, 设置默认最大页数为 {max_pages}'
                )

            while page_num <= max_pages:
                logger.info(f'正在抓取第 {page_num}/{max_pages} 页...')

                # 抓取当前页面数据
                page_data = scrape_data(iframe)

                # 如果是第一页，直接添加所有数据（包括表头）
                if page_num == 1:
                    all_data.extend(page_data)
                # 如果不是第一页，只添加数据行，跳过表头
                else:
                    # 确保 page_data 有数据且长度大于1（至少有表头和一行数据）
                    if page_data and len(page_data) > 1:
                        # 只添加数据行，跳过表头（第一行）
                        all_data.extend(page_data[1:])
                        logger.info(f'添加了 {len(page_data) - 1} 行数据（跳过表头）')

                # 检查是否有下一页
                # 注意：这里需要在iframe内查找下一页按钮
                # 根据您提供的HTML元素使用正确的选择器
                next_button = iframe.locator(
                    'a.pagination-button.pagination-next:not(.mini-disabled)'
                ).count()
                if next_button == 0:
                    logger.info('没有找到下一页按钮或已到最后一页')
                    break

                # 点击下一页
                logger.info('点击下一页按钮')
                iframe.locator('a.pagination-button.pagination-next').click()
                # 等待表格数据加载
                sleep(2)  # 等待页面加载
                # 等待表格刷新 - 正确使用FrameLocator对象
                # 使用attached状态而不是visible状态
                iframe.locator('.mini-grid-row').first.wait_for(
                    state='attached', timeout=10000
                )
                # 注意：点击下一页后不需要重新切换框架，因为我们仍然在同一个iframe内
                page_num += 1

            # 记录数据抓取阶段耗时
            scraping_end_time = time.time()
            time_stats['数据抓取'] = scraping_end_time - stage_start_time
            logger.info(f'数据抓取阶段耗时: {format_time_diff(time_stats["数据抓取"])}')

            # 更新阶段开始时间
            stage_start_time = time.time()

            # 保存数据到Excel
            if all_data and len(all_data) > 1:
                try:
                    # 获取表头和数据行
                    headers = all_data[0]
                    data_rows = all_data[1:]

                    # 检查数据行的列数
                    row_lengths = [len(row) for row in data_rows]
                    max_cols = max(row_lengths) if row_lengths else 0
                    min_cols = min(row_lengths) if row_lengths else 0
                    header_count = len(headers)

                    logger.info(
                        f'表头列数: {header_count}, 数据行最小列数: {min_cols}, 最大列数: {max_cols}'
                    )

                    # 处理数据行，确保列数与表头匹配
                    uniform_data = []
                    for row in data_rows:
                        # 如果行的列数少于表头列数，添加空值
                        if len(row) < len(headers):
                            uniform_data.append(row + [''] * (len(headers) - len(row)))
                        # 如果行的列数多于表头列数，截取
                        elif len(row) > len(headers):
                            uniform_data.append(row[: len(headers)])
                        else:
                            uniform_data.append(row)

                    # 创建DataFrame并保存
                    df = pd.DataFrame(uniform_data, columns=headers)
                    df.to_excel('采购合同数据.xlsx', index=False)
                    df.to_csv('采购合同数据.csv', index=False, encoding='utf-8-sig')
                    logger.info('数据已保存到 采购合同数据.xlsx 和 采购合同数据.csv')
                except Exception as e:
                    logger.error(f'保存数据到Excel时出错: {str(e)}')
                    # 尝试保存原始数据
                    with open('采购合同数据.csv', 'w', encoding='utf-8-sig') as f:
                        for row in all_data:
                            f.write(','.join([str(cell) for cell in row]) + '\n')
                    logger.info('原始数据已保存到 采购合同数据.csv')
            else:
                logger.warning('没有抓取到任何数据')

            # 关闭浏览器
            browser.close()

            # 记录数据处理与保存阶段耗时
            data_processing_end_time = time.time()
            time_stats['数据处理与保存'] = data_processing_end_time - stage_start_time
            logger.info(
                f'数据处理与保存阶段耗时: {format_time_diff(time_stats["数据处理与保存"])}'
            )

            # 计算总耗时
            end_time = time.time()
            time_stats['总耗时'] = end_time - start_time

            # 输出时间统计报告
            logger.info('=' * 50)
            logger.info('程序运行时间统计报告')
            logger.info('=' * 50)
            for stage, duration in time_stats.items():
                logger.info(f'{stage}: {format_time_diff(duration)}')
            logger.info('=' * 50)

    except Exception as e:
        # 即使出错也记录已运行时间
        end_time = time.time()
        total_time = end_time - start_time
        logger.error(f'发生错误: {str(e)}')
        logger.error(f'程序运行总时间: {format_time_diff(total_time)}')
        raise


if __name__ == '__main__':
    main()
