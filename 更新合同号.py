#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-06-04 20:31:02
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-04 20:42:50
# 文件相对于项目的路径   : \Crawl_AI\更新合同号.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
import sqlite3
import re


def main():
    with sqlite3.connect(r"d:\user\PythonProject\ZConline\app.db", timeout=60) as conn:
        conn.execute("PRAGMA journal_mode=WAL;")
        cursor = conn.cursor()

        cursor.execute("SELECT 平台合同编号, 中车合同编号,合同名称 FROM 合同编号对照")
        rows = cursor.fetchall()

        for row in rows:
            platform_contract_no = row[0]
            crrc_contract_no = row[1]
            contract_name = row[2].upper().strip()

            if not crrc_contract_no:
                print(f"{platform_contract_no}:{contract_name}")
                contract_no = re.search(r"(ZC\d{2}-A\d{3})", contract_name)
                if contract_no:
                    sql = f"UPDATE 合同编号对照 SET 中车合同编号 = '{contract_no.group(1)}' WHERE 平台合同编号 = '{platform_contract_no}'"
                    cursor.execute(sql)
                    conn.commit()


main()
