#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-04-26 18:51:57
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-05-16 09:27:34
# 文件相对于项目的路径   : \Crawl_AI\bgw.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
from playwright.sync_api import sync_playwright
import pandas as pd
from time import sleep
from ddddocr import DdddOcr
import re


def scrape_data(frame_locator):
    data = []
    print('等待表格加载...')

    frame_locator.locator('table.mini-grid-table').first.wait_for(
        state='attached', timeout=10000
    )

    frame_locator.locator('.mini-grid-row').first.wait_for(
        state='attached', timeout=10000
    )

    # 获取表头
    headers = []
    header_cells = frame_locator.locator('span.mini-grid-headertxt').all()
    print(f'找到 {len(header_cells)} 个表头单元格')

    for cell in header_cells:
        text = cell.inner_text().strip()
        if text and text != '' and not text.startswith('\n') and not text == '\ue603':
            headers.append(text)

    # 如果没有找到足够的表头，尝试使用title属性
    if not headers or len(headers) < 5:
        print('使用默认选择器未找到足够的表头，尝试使用title属性')
        headers = []
        header_cells = frame_locator.locator('.mini-grid-headerCell-inner[title]').all()
        print(f'找到 {len(header_cells)} 个带title属性的表头单元格')

        for cell in header_cells:
            title = cell.get_attribute('title')
            if title and title.strip() != '':
                headers.append(title.strip())

    # 如果还是没有找到足够的表头，使用预定义的表头
    if not headers or len(headers) < 5:
        print('使用title属性仍未找到足够的表头，使用预定义的表头')
        headers = [
            '选择',
            '操作',
            '序',
            '状态',
            '合同编号',
            '集团统一编码',
            '合同名称',
            '下一步审批人',
            '乙方名称',
            '买方经办人',
            '合同截止日期',
            '合同签订时间',
            '合同金额(含税)',
            '合同金额(不含税)',
            '合同分类',
            '单据提报人',
            '审核状态',
            'mq推送状态',
            'mq接收状态',
        ]
        print(f'使用预定义的表头，共 {len(headers)} 个')
    else:
        # 确保表头第一列是"选择"
        if len(headers) > 0 and headers[0] != '选择':
            print('在表头开头添加"选择"列')
            headers.insert(0, '选择')

    print(f'表头: {headers}')
    if headers:
        data.append(headers)
    else:
        print('未找到表头，将使用默认列名')
        default_headers = [
            '序号',
            '操作',
            '状态',
            '合同编号',
            '合同名称',
            '乙方名称',
            '合同金额',
            '签订日期',
            '截止日期',
        ]
        data.append(default_headers)

    # 获取表格数据行
    rows = frame_locator.locator('.mini-grid-row').all()
    print(f'找到 {len(rows)} 行数据')

    if len(rows) == 0:
        print('使用默认选择器未找到表格行，尝试备用选择器')
        rows = frame_locator.locator('tr.mini-grid-row').all()
        print(f'使用备用选择器找到 {len(rows)} 行数据')

    if len(rows) == 0:
        print('使用备用选择器仍未找到表格行，尝试更通用的选择器')
        rows = frame_locator.locator('tbody tr').all()
        print(f'使用通用选择器找到 {len(rows)} 行数据')

    for row in rows:
        row_data = []
        cells = row.locator('.mini-grid-cell-inner').all()

        if len(cells) == 0:
            cells = row.locator('td div').all()

        if len(cells) == 0:
            cells = row.locator('td').all()

        for cell in cells:
            text = cell.inner_text().strip()
            row_data.append(text)

        if any(row_data) and len(row_data) > 1:
            data.append(row_data)

    print(f'成功抓取 {len(data) - 1} 行数据')
    return data


def main():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(viewport={'width': 1920, 'height': 1080})
        page = context.new_page()

        # 访问登录页面
        print('访问登录页面...')
        page.goto(
            'https://sso.crrcgo.cc/login?returnUrl=https%3A%2F%2Fwww.crrcgo.cc%2F%23%2FhomePage'
        )
        print('登录页面加载成功')

        # 填写登录信息
        page.fill("input[name='username']", '010800006291')
        page.fill("input[name='password']", 'Z6h2en91@')

        # 处理验证码，最多尝试三次
        max_captcha_attempts = 3
        login_success = False

        for attempt in range(max_captcha_attempts):
            print(f'处理验证码... 第 {attempt + 1} 次尝试')

            print('等待验证码图片元素加载...')
            page.wait_for_selector('form img', state='visible', timeout=10000)
            selectors = [
                'form img',
                "form[action*='login'] img",
                "img[src*='validCode']",
                "img[src*='captcha']",
                'form div img',
            ]

            captcha_element = None
            for selector in selectors:
                elements = page.locator(selector).all()
                if elements:
                    print(f"使用选择器 '{selector}' 找到 {len(elements)} 个图片元素")
                    for element in elements:
                        box = element.bounding_box()
                        if (
                            box
                            and 50 <= box['width'] <= 200
                            and 20 <= box['height'] <= 100
                        ):
                            captcha_element = element
                            print(
                                f'找到可能的验证码图片，尺寸: {box["width"]}x{box["height"]}'
                            )
                            break

                if captcha_element:
                    break

            if not captcha_element:
                print('未找到合适的验证码图片，使用默认选择器')
                captcha_element = page.locator('form img').first

            captcha_element.wait_for(state='visible', timeout=5000)
            print('截取验证码图片...')
            captcha_img = captcha_element.screenshot()
            print('验证码图片截取成功')

            print('开始识别验证码...')
            ocr = DdddOcr()
            code = ocr.classification(captcha_img)
            if code:
                code = code.strip().replace(' ', '').replace('\n', '')
                expected_length = 4
                if len(code) != expected_length:
                    print(
                        f'识别的验证码长度异常: {len(code)}，期望长度: {expected_length}'
                    )
                    # 如果长度不对，可以尝试补全或截断
                    if len(code) < expected_length:
                        code = code + '0' * (expected_length - len(code))
                    else:
                        code = code[:expected_length]

                print(f'验证码识别结果: {code}')
            else:
                # 如果识别结果为空，使用默认值
                print('验证码识别结果为空，使用默认值')
                code = '1234'

            # 填写验证码
            page.fill("input[name='validCode']", code)

            # 点击登录按钮
            page.click('input.loginBtn')

            # 等待一下看是否登录成功
            login_success = False
            try:
                page.wait_for_selector(
                    "span[slot='reference']:has-text('电子采购')", timeout=5000
                )
                print('登录成功!')
                login_success = True
                break
            except Exception as e:
                print(f'登录失败，可能是验证码错误: {str(e)}')
                if attempt < max_captcha_attempts - 1:
                    # 点击验证码图片刷新
                    print('点击验证码图片刷新...')
                    captcha_element.click()
                    sleep(2)  # 等待验证码刷新完成

        # 检查是否登录成功
        if not login_success:
            print('多次尝试后仍然无法登录，可能是验证码识别问题')
            browser.close()
            return

        # 等待登录成功并导航
        print('等待登录成功...')
        page.wait_for_load_state('networkidle')

        # 导航到电子采购页面
        print('导航到电子采购页面...')
        page.wait_for_load_state('networkidle')

        # 点击电子采购元素，这会打开新页面
        with page.expect_popup() as popup_info:
            page.click("span[slot='reference']:has-text('电子采购')")

        # 获取新打开的页面
        page = popup_info.value
        page.wait_for_load_state('networkidle')
        sleep(3)

        # 点击侧边栏中的"合同管理"菜单
        print('点击合同管理菜单...')
        page.click("div.menu-item[data-code='9504'] > a.menu-link")
        sleep(2)

        # 点击子菜单"采购合同管理"
        print('点击采购合同管理子菜单...')
        page.click("div.menu-item[data-code='95040005'] > a.menu-link")
        page.wait_for_load_state('networkidle')
        sleep(3)

        # 处理iframe
        print('切换到iframe内部...')
        iframe_selector = 'iframe#tab-content-95040005'
        page.wait_for_selector(iframe_selector, state='visible')
        iframe = page.frame_locator(iframe_selector)

        # 收集所有数据
        all_data = []
        page_num = 1

        # 获取总条数和计算最大页数
        max_pages = 100
        pagination_info = iframe.locator('.pagination-pagerinfo').first
        if pagination_info:
            pagination_info.wait_for(state='attached', timeout=5000)
            total_text = pagination_info.inner_text()
            total_count_match = re.search(r'\d+', total_text)
            if total_count_match:
                total_count = int(total_count_match.group())
                items_per_page = 20
                max_pages = (total_count + items_per_page - 1) // items_per_page
                print(f'总条数: {total_count}, 最大页数: {max_pages}')
            else:
                print(f'无法获取总条数，设置默认最大页数为 {max_pages}')
        else:
            print(f'未找到分页信息，设置默认最大页数为 {max_pages}')

        while page_num <= max_pages:
            print(f'正在抓取第 {page_num}/{max_pages} 页...')

            # 抓取当前页面数据
            page_data = scrape_data(iframe)

            # 如果是第一页，直接添加所有数据（包括表头）
            if page_num == 1:
                all_data.extend(page_data)
            # 如果不是第一页，只添加数据行，跳过表头
            elif page_data and len(page_data) > 1:
                all_data.extend(page_data[1:])
                print(f'添加了 {len(page_data) - 1} 行数据（跳过表头）')

            # 检查是否有下一页
            next_button = iframe.locator(
                'a.pagination-button.pagination-next:not(.mini-disabled)'
            ).count()
            if next_button == 0:
                print('没有找到下一页按钮或已到最后一页')
                break

            # 点击下一页
            print('点击下一页按钮')
            iframe.locator('a.pagination-button.pagination-next').click()
            sleep(2)  # 等待页面加载
            iframe.locator('.mini-grid-row').first.wait_for(
                state='attached', timeout=10000
            )
            page_num += 1

        # 保存数据到Excel
        if all_data and len(all_data) > 1:
            # 获取表头和数据行
            headers = all_data[0]
            data_rows = all_data[1:]

            # 检查数据行的列数
            row_lengths = [len(row) for row in data_rows]
            max_cols = max(row_lengths) if row_lengths else 0
            min_cols = min(row_lengths) if row_lengths else 0
            header_count = len(headers)

            print(
                f'表头列数: {header_count}, 数据行最小列数: {min_cols}, 最大列数: {max_cols}'
            )

            # 处理数据行，确保列数与表头匹配
            uniform_data = []
            for row in data_rows:
                if len(row) < len(headers):
                    uniform_data.append(row + [''] * (len(headers) - len(row)))
                elif len(row) > len(headers):
                    uniform_data.append(row[: len(headers)])
                else:
                    uniform_data.append(row)

            # 创建DataFrame并保存
            df = pd.DataFrame(uniform_data, columns=headers)
            df.to_excel('采购合同数据.xlsx', index=False)
            print('数据已保存到 采购合同数据.xlsx')
        else:
            print('没有抓取到任何数据')

        # 关闭浏览器
        browser.close()


if __name__ == '__main__':
    main()
