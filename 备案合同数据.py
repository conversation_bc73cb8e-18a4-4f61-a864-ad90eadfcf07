#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-05-25 07:32:52
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-15 06:19:08
# 文件相对于项目的路径   : \Crawl_AI\备案合同数据.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
# 作者           : KingFreeDom
# 创建时间         : 2025-04-25 14:11:50
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-04-28 10:30:58
# 文件相对于项目的路径   : \Crawl_AI\zcg.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
#!/usr/bin/env python
# -*- coding:utf-8 -*-

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
import pandas as pd
import sys
from typing import List, Optional
from dataclasses import dataclass
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    WebDriverException,
    StaleElementReferenceException,
)
from time import sleep
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('crawler.log'), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


@dataclass
class Config:
    """配置类"""

    CHROME_DRIVER_PATH: str = (
        r'C:\Program Files\Google\Chrome\Application\chromedriver.exe'
    )
    LOGIN_URL: str = 'https://sso.crrcgo.cc/login?v=1&client=cangqiong&returnUrl=https%3A%2F%2Fportal.crrcgo.cc%2F%2F%3F&isPortalMobile=false'
    USERNAME: str = '010800006291'
    PASSWORD: str = 'Z6h2en91@'
    MAX_RETRIES: int = 3
    WAIT_TIMEOUT: int = 20  # 增加等待时间
    PAGE_LOAD_TIMEOUT: int = 30  # 页面加载超时时间
    RETRY_INTERVAL: int = 2  # 重试间隔时间


class ContractCrawler:
    def __init__(self, config: Config):
        self.config = config
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        try:
            from ddddocr import DdddOcr

            self.ocr = DdddOcr(show_ad=False)
        except ImportError as e:
            logger.error('OCR初始化失败，请确保已安装 ddddocr: %s', e)
            sys.exit("缺少 ddddocr 库，请运行 'pip install ddddocr'")

    def setup_driver(self):
        if self.driver and self.wait:
            return
        options = webdriver.ChromeOptions()
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('log-level=3')
        options.add_experimental_option('excludeSwitches', ['enable-logging'])
        options.page_load_strategy = 'normal'
        service = Service(self.config.CHROME_DRIVER_PATH)
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.maximize_window()
        self.driver.set_page_load_timeout(self.config.PAGE_LOAD_TIMEOUT)
        self.wait = WebDriverWait(self.driver, self.config.WAIT_TIMEOUT)
        logger.info('浏览器初始化完成')

    def handle_captcha(self) -> str:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        try:
            captcha_img = self.wait.until(
                EC.visibility_of_element_located(
                    (
                        By.XPATH,
                        "//img[@src='/getCode' and contains(@class, 'validImg')]",
                    )
                )
            )
            time.sleep(0.5)
            img_bytes = captcha_img.screenshot_as_png
            if not img_bytes:
                raise Exception('无法获取验证码截图')

            code = self.ocr.classification(img_bytes)
            if isinstance(code, dict) and 'result' in code:
                code = code['result']
            logger.info('验证码识别结果: %s', code)
            if not code or not isinstance(code, str) or len(code) != 4:
                logger.warning('验证码识别结果异常: %s', code)
                refresh_button = self.driver.find_element(
                    By.XPATH, "//img[@src='/getCode']/following-sibling::span"
                )
                refresh_button.click()
                time.sleep(1)
                return self.handle_captcha()
            return code

        except (TimeoutException, NoSuchElementException) as e:
            logger.error('未找到验证码图片元素: %s', e)
            raise
        except Exception as e:
            logger.error('验证码处理失败: %s', e)
            raise

    def fetch_page_data(self, page_num):
        self.setup_driver()
        if not self.driver:
            raise Exception('Driver 未初始化')

        page_data = []
        try:
            self.driver.get(f'{self.config.LOGIN_URL}?page={page_num}')
            table = self.wait.until(
                EC.presence_of_element_located((By.XPATH, '//table'))
            )
            rows = table.find_elements(By.TAG_NAME, 'tr')
            for row in rows:
                cols = row.find_elements(By.TAG_NAME, 'td')
                if cols:
                    page_data.append([col.text for col in cols])
            return page_data

        except Exception as e:
            logger.error(f'获取第 {page_num} 页数据失败: {e}')
            raise

    def login(self) -> bool:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        try:
            try:
                self.driver.get(self.config.LOGIN_URL)
            except TimeoutException:
                logger.warning('页面加载超时，尝试继续操作')
                self.driver.execute_script('window.stop();')

            username_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, 'username'))
            )
            password_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, 'password'))
            )

            username_input.send_keys(self.config.USERNAME)
            password_input.send_keys(self.config.PASSWORD)

            for attempt in range(self.config.MAX_RETRIES):
                try:
                    code = self.handle_captcha()
                    validcode_input = self.wait.until(
                        EC.presence_of_element_located((By.NAME, 'validCode'))
                    )
                    validcode_input.clear()
                    validcode_input.send_keys(code)

                    login_button = self.wait.until(
                        EC.element_to_be_clickable((By.CLASS_NAME, 'loginBtn'))
                    )
                    login_button.click()

                    try:
                        e_procurement_button = self.wait.until(
                            EC.element_to_be_clickable(
                                (
                                    By.XPATH,
                                    "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]",
                                )
                            )
                        )
                        logger.info('找到电子采购按钮')
                        e_procurement_button.click()

                        try:
                            self.wait.until(EC.number_of_windows_to_be(2))
                            self.driver.switch_to.window(self.driver.window_handles[-1])
                            self.wait.until(
                                EC.presence_of_element_located((By.TAG_NAME, 'body'))
                            )
                            logger.info('电子采购页面已加载')
                            sleep(5)
                            return True
                        except TimeoutException:
                            logger.warning('等待新窗口超时，尝试直接继续')
                            if len(self.driver.window_handles) > 1:
                                self.driver.switch_to.window(
                                    self.driver.window_handles[-1]
                                )
                                return True
                            raise Exception('无法切换到新窗口')

                    except TimeoutException:
                        error_message = self.driver.find_elements(
                            By.CLASS_NAME, 'el-message__content'
                        )
                        if error_message and '验证码错误' in error_message[0].text:
                            if attempt < self.config.MAX_RETRIES - 1:
                                logger.warning('验证码错误，重试中...')
                                continue
                        raise Exception('登录失败')

                except Exception as e:
                    logger.warning('登录尝试 %d 失败: %s', attempt + 1, e)
                    if attempt == self.config.MAX_RETRIES - 1:
                        raise

            return False

        except Exception as e:
            logger.error('登录过程发生错误: %s', e)
            raise

    def navigate_to_contracts(self) -> None:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        try:
            if len(self.driver.window_handles) > 1:
                self.driver.switch_to.window(self.driver.window_handles[-1])

            def retry_click(xpath, max_retries=3):
                for i in range(max_retries):
                    try:
                        element = self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, xpath))
                        )
                        element.click()
                        return True
                    except (TimeoutException, StaleElementReferenceException) as e:
                        if i == max_retries - 1:
                            raise
                        logger.warning(f'点击元素失败，重试中: {e}')
                        time.sleep(self.config.RETRY_INTERVAL)
                return False

            retry_click('//*[@id="theme-side"]/div[1]/div[11]')
            logger.info('已点击电子合同管理菜单')

            retry_click('//*[@id="theme-side"]/div[1]/div[11]/div/div[2]/a')
            logger.info('已点击合同备案菜单')

            sleep(5)

            try:
                iframe = self.wait.until(
                    EC.presence_of_element_located((By.ID, 'tab-content-95130002'))
                )
                self.driver.switch_to.frame(iframe)
                logger.info('已进入合同管理页面')
            except TimeoutException:
                logger.warning('通过ID找不到iframe，尝试其他方法')
                iframes = self.driver.find_elements(By.TAG_NAME, 'iframe')
                if iframes:
                    self.driver.switch_to.frame(iframes[0])
                    logger.info('已通过备选方法进入iframe')
                else:
                    raise Exception('无法找到合同备案页面的iframe')

        except Exception as e:
            logger.error('导航到合同备案页面失败: %s', e)
            raise

    def extract_table_header(self) -> list:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')
        try:
            self.driver.switch_to.default_content()
            iframe = self.wait.until(
                EC.presence_of_element_located((By.ID, 'tab-content-95130002'))
            )
            self.driver.switch_to.frame(iframe)
            # 获取所有mini-grid-table，取第一个为表头table
            header_tables = self.wait.until(
                EC.presence_of_all_elements_located(
                    (By.XPATH, '//table[contains(@class, "mini-grid-table")]')
                )
            )
            if not header_tables:
                raise Exception('未找到任何mini-grid-table')
            header_table = header_tables[1]
            all_trs = header_table.find_elements(By.XPATH, './/tr')
            header_row = None
            for tr in all_trs:
                header_cells = tr.find_elements(
                    By.XPATH, './/td[contains(@class, "mini-grid-headerCell")]'
                )
                if header_cells:
                    header_row = tr
                    break
            if not header_row:
                raise Exception('未找到表头行')
            headers = []
            for cell in header_row.find_elements(
                By.XPATH, './/td[contains(@class, "mini-grid-headerCell")]'
            ):
                text = cell.text.strip()
                if not text:
                    text = cell.get_attribute('title') or ''
                    text = text.strip()
                if not text:
                    divs = cell.find_elements(By.XPATH, './/div')
                    for div in divs:
                        div_title = div.get_attribute('title')
                        if div_title and div_title.strip():
                            text = div_title.strip()
                            break
                headers.append(text)
            # 保证表头和数据列数一致：如表头比数据多，去掉多余表头；如表头比数据少，补空表头
            # sample_data = self.extract_table_data()
            # if sample_data:
            #     data_col_count = len(sample_data[0])
            #     if len(headers) > data_col_count:
            #         headers = headers[:data_col_count]
            #     elif len(headers) < data_col_count:
            #         headers += [
            #             f'列{idx + 1}' for idx in range(len(headers), data_col_count)
            #         ]
            del headers[0]
            logger.info(f'提取到表头: {headers}')
            return headers
        except Exception as e:
            logger.error(f'提取表头失败: {e}')
            raise

    def extract_table_data(self) -> List[List[str]]:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        data = []
        for attempt in range(self.config.MAX_RETRIES):
            try:
                table = self.wait.until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            '//*[@id="datagridlist"]/div/div[2]/div[4]/div[2]/div/table',
                        )
                    )
                )
                rows = table.find_elements(By.TAG_NAME, 'tr')
                for row in rows:
                    try:
                        cells = row.find_elements(By.TAG_NAME, 'td')
                        # 跳过前两个无意义的单元格
                        row_data = [cell.text for cell in cells][2:]
                        if any(cell.strip() for cell in row_data):
                            data.append(row_data)
                    except StaleElementReferenceException:
                        logger.warning('处理行数据时元素已过期，重试整个表格提取')
                        break  # 直接break，进入下一个attempt重试
                else:
                    # 如果没有break，说明本轮成功
                    if data:
                        return data
                    else:
                        logger.warning(
                            f'未提取到数据，尝试重试 ({attempt + 1}/{self.config.MAX_RETRIES})'
                        )
                        time.sleep(self.config.RETRY_INTERVAL)
                        continue
                # 如果break了，进入下一个attempt
                logger.warning(
                    f'表格行元素过期，重试 ({attempt + 1}/{self.config.MAX_RETRIES})'
                )
                time.sleep(self.config.RETRY_INTERVAL)
            except (TimeoutException, StaleElementReferenceException) as e:
                if attempt == self.config.MAX_RETRIES - 1:
                    logger.error(f'提取表格数据失败: {e}')
                    raise
                logger.warning(f'提取表格数据失败，重试中: {e}')
                time.sleep(self.config.RETRY_INTERVAL)
        return data

    def get_total_pages(self) -> int:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')
        try:
            for attempt in range(self.config.MAX_RETRIES):
                try:
                    pages_text = self.driver.find_element(
                        By.XPATH, '//*[@id="mini-42"]/div[1]'
                    ).text
                    # 兼容“共N条”或“共N页”等格式
                    match = re.search(r'共(\d+)页', pages_text)
                    if match:
                        pages = int(match.group(1))
                        logger.info(f'总页数: {pages}')
                        return max(1, pages)
                    match2 = re.search(r'共(\d+)条', pages_text)
                    if match2:
                        total_records = int(match2.group(1))
                        pages = (total_records - 1) // 20 + 1
                        logger.info(f'总记录数: {total_records}, 总页数: {pages}')
                        return max(1, pages)
                    raise ValueError(f'无法从文本中提取页数: {pages_text}')
                except (NoSuchElementException, StaleElementReferenceException) as e:
                    if attempt == self.config.MAX_RETRIES - 1:
                        raise
                    logger.warning(f'获取总页数失败，重试中: {e}')
                    time.sleep(self.config.RETRY_INTERVAL)
            logger.warning('无法获取总页数，使用默认值1')
            return 1
        except Exception as e:
            logger.error('获取总页数失败: %s', e)
            return 1

    def navigate_to_page(self, page: int) -> bool:
        self.setup_driver()
        if not self.driver or not self.wait:
            raise Exception('Driver 未初始化')

        try:
            if page == 1:
                return True

            for attempt in range(self.config.MAX_RETRIES):
                try:
                    page_input = self.driver.find_element(
                        By.CLASS_NAME, 'mini-grid-rows-content'
                    )
                    page_input.clear()
                    page_input.send_keys(str(page))
                    page_input.send_keys(webdriver.Keys.ENTER)
                    time.sleep(2)
                    return True
                except Exception:
                    try:
                        next_page = self.wait.until(
                            EC.element_to_be_clickable(
                                (By.XPATH, '//*[@id="mini-42"]/div[2]/a[4]')
                            )
                        )
                        next_page.click()
                        time.sleep(2)
                        return True
                    except (TimeoutException, StaleElementReferenceException) as e:
                        if attempt == self.config.MAX_RETRIES - 1:
                            logger.error(f'导航到第 {page} 页失败: {e}')
                            return False
                        logger.warning(f'导航到第 {page} 页失败，重试中: {e}')
                        time.sleep(self.config.RETRY_INTERVAL)

            return False

        except Exception as e:
            logger.error(f'导航到第 {page} 页时发生错误: {e}')
            return False

    def crawl_data(self) -> None:
        try:
            self.setup_driver()
            if not self.login():
                raise Exception('登录失败')
            self.navigate_to_contracts()
            total_pages = self.get_total_pages()
            if total_pages <= 0:
                logger.warning('总页数为0，强制设为1')
                total_pages = 1
            # 先提取表头
            headers = self.extract_table_header()
            all_data = []
            # 提取第一页数据
            first_page_data = self.extract_table_data()
            if first_page_data:
                all_data.extend(first_page_data)
                logger.info('已完成第 1/%d 页数据抓取', total_pages)
            else:
                logger.warning('第1页未获取到数据')
            # 翻页抓取
            for page in range(2, total_pages + 1):
                try:
                    if not self.navigate_to_page(page):
                        logger.warning(f'无法导航到第{page}页，跳过')
                        continue
                    page_data = self.extract_table_data()
                    if page_data:
                        all_data.extend(page_data)
                        logger.info('已完成第 %d/%d 页数据抓取', page, total_pages)
                    else:
                        logger.warning(f'第{page}页未获取到数据')
                except Exception as e:
                    logger.error('第 %d 页数据抓取失败: %s', page, e)
                    continue
            # 保存数据
            if all_data and headers:
                df = pd.DataFrame(all_data, columns=headers)
                df = df.dropna(how='all').dropna(axis=1, how='all')
                output_file = '已备案合同数据.xlsx'
                df.to_excel(output_file, index=False)
                logger.info(f'数据已保存到 {output_file}，共 {len(df)} 条记录')
            else:
                logger.warning('未获取到任何数据或表头，无法保存')
        except Exception as e:
            logger.error('数据抓取过程发生错误: %s', e)
            raise
        finally:
            if self.driver:
                try:
                    self.driver.quit()
                    logger.info('浏览器已关闭')
                except Exception as e:
                    logger.warning(f'关闭浏览器时发生错误: {e}')


def main():
    config = Config()
    crawler = ContractCrawler(config)

    try:
        start_time = time.time()
        crawler.crawl_data()
        duration = (time.time() - start_time) / 60
        logger.info('任务完成，运行时间：%.2f 分钟', duration)
    except Exception as e:
        logger.error('程序执行失败: %s', e)
        sys.exit(1)


if __name__ == '__main__':
    main()
