#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
简化的SeleniumBase爬虫 - 直接运行版本
"""

import time
import logging
from seleniumbase import SB
import pandas as pd
import sys
from typing import List
from dataclasses import dataclass, field
from time import sleep
import sqlite3
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('crawler_simple.log'), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


@dataclass
class Config:
    """配置类"""

    LOGIN_URL: str = 'https://sso.crrcgo.cc/login?v=1&client=cangqiong&returnUrl=https%3A%2F%2Fportal.crrcgo.cc%2F%2F%3F&isPortalMobile=false'
    USERNAME: str = '010800006291'
    PASSWORD: str = 'Z6h2en91@'
    DATABASE_PATH: str = r'd:\user\pythonproject\zconline\app.db'
    MAX_RETRIES: int = 3
    WAIT_TIMEOUT: int = 20
    PAGE_LOAD_TIMEOUT: int = 30
    RETRY_INTERVAL: int = 2
    # 本地ChromeDriver可能的路径（按优先级排序）
    CHROME_DRIVER_PATHS: list = field(
        default_factory=lambda: [
            r'C:\Program Files\Google\Chrome\Application\chromedriver.exe',  # 您确实有的路径
            r'C:\chromedriver\chromedriver.exe',
            r'chromedriver.exe',
            r'./chromedriver.exe',
        ]
    )


class SimpleContractCrawler:
    """简化的合同爬虫类"""

    def __init__(self):
        self.config = Config()
        try:
            from ddddocr import DdddOcr

            self.ocr = DdddOcr(show_ad=False)
        except ImportError as e:
            logger.error('OCR初始化失败，请确保已安装 ddddocr: %s', e)
            sys.exit("缺少 ddddocr 库，请运行 'pip install ddddocr'")

    def handle_captcha(self, sb) -> str:
        """处理验证码识别"""
        try:
            # 等待验证码图片加载
            sb.wait_for_element_visible(
                "//img[@src='/getCode' and contains(@class, 'validImg')]",
                timeout=self.config.WAIT_TIMEOUT,
            )

            time.sleep(0.5)
            captcha_img = sb.find_element(
                "//img[@src='/getCode' and contains(@class, 'validImg')]"
            )
            img_bytes = captcha_img.screenshot_as_png

            if not img_bytes:
                raise Exception('无法获取验证码截图')

            code = self.ocr.classification(img_bytes)
            if isinstance(code, dict) and 'result' in code:
                code = code['result']

            logger.info('验证码识别结果: %s', code)

            # 只要识别到内容就尝试使用，不管长度
            if not code or not isinstance(code, str):
                logger.warning('验证码识别失败，无内容')
                return ''

            # 清理验证码（去除空格等）
            code = code.strip()
            logger.info('清理后的验证码: %s (长度: %d)', code, len(code))
            return code

        except Exception as e:
            logger.error('验证码处理失败: %s', e)
            raise

    def login(self, sb) -> bool:
        """登录系统"""
        try:
            for attempt in range(self.config.MAX_RETRIES):
                try:
                    logger.info(f'登录尝试 {attempt + 1}/{self.config.MAX_RETRIES}')

                    # 每次尝试都重新打开登录页面
                    sb.open(self.config.LOGIN_URL)
                    time.sleep(2)  # 等待页面加载

                    # 填写用户名和密码
                    sb.type('[name="username"]', self.config.USERNAME)
                    sb.type('[name="password"]', self.config.PASSWORD)

                    # 处理验证码
                    code = self.handle_captcha(sb)

                    if not code:
                        logger.warning('验证码识别失败，重新尝试')
                        continue

                    # 清空验证码输入框并输入识别结果
                    sb.clear('[name="validCode"]')
                    sb.type('[name="validCode"]', code)

                    # 点击登录按钮
                    sb.click('.loginBtn')

                    try:
                        # 等待电子采购按钮出现并点击
                        e_procurement_selector = "//a[contains(@class, 'kd-cq-btn') and .//span[contains(@class, '_1RGaSniK') and text()='电子采购']]"
                        sb.wait_for_element_clickable(
                            e_procurement_selector, timeout=self.config.WAIT_TIMEOUT
                        )
                        logger.info('登录成功！找到电子采购按钮')
                        sb.click(e_procurement_selector)

                        # 等待新窗口打开并切换
                        time.sleep(3)  # 等待新窗口打开
                        sb.switch_to_newest_window()
                        sb.maximize_window()  # 最大化新窗口

                        # 等待页面加载
                        sb.wait_for_element_present(
                            'body', timeout=self.config.WAIT_TIMEOUT
                        )
                        logger.info('电子采购页面已加载')
                        sleep(5)
                        return True

                    except Exception as login_error:
                        # 检查是否有错误消息
                        try:
                            error_elements = sb.find_elements('.el-message__content')
                            if error_elements:
                                error_msg = error_elements[0].text
                                if '验证码错误' in error_msg:
                                    logger.warning(f'验证码错误: {error_msg}，重新尝试')
                                    continue
                                else:
                                    logger.warning(f'登录错误: {error_msg}')
                        except:
                            pass

                        logger.warning(f'登录失败: {login_error}，重新尝试')
                        continue

                except Exception as e:
                    logger.warning('登录尝试 %d 失败: %s', attempt + 1, e)
                    if attempt == self.config.MAX_RETRIES - 1:
                        raise

            logger.error('所有登录尝试都失败了')
            return False

        except Exception as e:
            logger.error('登录过程发生错误: %s', e)
            raise

    def navigate_to_contracts(self, sb) -> None:
        """导航到合同管理页面"""
        try:
            # 确保在正确的窗口
            if len(sb.driver.window_handles) > 1:
                sb.switch_to_newest_window()

            def retry_click(xpath, max_retries=3):
                for i in range(max_retries):
                    try:
                        sb.wait_for_element_clickable(
                            xpath, timeout=self.config.WAIT_TIMEOUT
                        )
                        sb.click(xpath)
                        return True
                    except Exception as e:
                        if i == max_retries - 1:
                            raise
                        logger.warning(f'点击元素失败，重试中: {e}')
                        time.sleep(self.config.RETRY_INTERVAL)
                return False

            # 点击采购数据菜单
            retry_click('//*[@id="theme-side"]/div[1]/div[15]')
            logger.info('已点击采购数据菜单')

            # 点击合同信息菜单
            retry_click('//*[@id="theme-side"]/div[1]/div[15]/div/div/a')
            logger.info('已点击合同信息菜单')

            sleep(5)

            try:
                # 切换到iframe
                iframe_selector = '#tab-content-95190001'
                sb.wait_for_element_present(
                    iframe_selector, timeout=self.config.WAIT_TIMEOUT
                )
                sb.switch_to_frame(iframe_selector)
                logger.info('已进入合同信息页面')
            except Exception:
                logger.warning('通过ID找不到iframe，尝试其他方法')
                # 尝试切换到第一个iframe
                iframes = sb.find_elements('iframe')
                if iframes:
                    sb.switch_to_frame(iframes[0])
                    logger.info('已通过备选方法进入iframe')
                else:
                    raise Exception('无法找到合同管理页面的iframe')

        except Exception as e:
            logger.error('导航到合同管理页面失败: %s', e)
            raise

    def extract_table_header(self, sb) -> list:
        """提取表头信息"""
        try:
            # 确保在正确的iframe中
            sb.switch_to_default_content()
            iframe_selector = '#tab-content-95190001'
            sb.wait_for_element_present(
                iframe_selector, timeout=self.config.WAIT_TIMEOUT
            )
            sb.switch_to_frame(iframe_selector)

            # 获取表头table
            header_table_selector = '//*[@id="datagrid"]/div/div[2]/div[2]/div[2]/table'
            sb.wait_for_element_present(
                header_table_selector, timeout=self.config.WAIT_TIMEOUT
            )

            header_table = sb.find_element(header_table_selector)
            all_trs = header_table.find_elements('xpath', './/tr')

            header_row = None
            for tr in all_trs:
                header_cells = tr.find_elements(
                    'xpath', './/td[contains(@class, "mini-grid-headerCell")]'
                )
                if header_cells:
                    header_row = tr
                    break

            if not header_row:
                raise Exception('未找到表头行')

            headers = []
            for cell in header_row.find_elements(
                'xpath', './/td[contains(@class, "mini-grid-headerCell")]'
            ):
                text = cell.text.strip()
                if not text:
                    text = cell.get_attribute('title') or ''
                    text = text.strip()
                if not text:
                    divs = cell.find_elements('xpath', './/div')
                    for div in divs:
                        div_title = div.get_attribute('title')
                        if div_title and div_title.strip():
                            text = div_title.strip()
                            break
                headers.append(text)

            # 去掉最后一个空列
            if headers:
                del headers[-1]

            logger.info(f'提取到表头: {headers}')
            return headers

        except Exception as e:
            logger.error(f'提取表头失败: {e}')
            raise

    def extract_table_data(self, sb) -> List[List[str]]:
        """提取表格数据"""
        data = []

        for attempt in range(self.config.MAX_RETRIES):
            try:
                table_selector = (
                    '//*[@id="datagrid"]/div/div[2]/div[4]/div[2]/div/table'
                )
                sb.wait_for_element_present(
                    table_selector, timeout=self.config.WAIT_TIMEOUT
                )

                table = sb.find_element(table_selector)
                rows = table.find_elements('tag name', 'tr')

                for row in rows:
                    try:
                        cells = row.find_elements('tag name', 'td')
                        # 跳过前一个无意义的单元格
                        row_data = [cell.text for cell in cells]
                        if any(cell.strip() for cell in row_data):
                            data.append(row_data[1:])  # 跳过第一列
                    except Exception:
                        logger.warning('处理行数据时元素已过期，重试整个表格提取')
                        break
                else:
                    # 如果没有break，说明本轮成功
                    if data:
                        return data
                    else:
                        logger.warning(
                            f'未提取到数据，尝试重试 ({attempt + 1}/{self.config.MAX_RETRIES})'
                        )
                        time.sleep(self.config.RETRY_INTERVAL)
                        continue

                # 如果break了，进入下一个attempt
                logger.warning(
                    f'表格行元素过期，重试 ({attempt + 1}/{self.config.MAX_RETRIES})'
                )
                time.sleep(self.config.RETRY_INTERVAL)

            except Exception as e:
                if attempt == self.config.MAX_RETRIES - 1:
                    logger.error(f'提取表格数据失败: {e}')
                    raise
                logger.warning(f'提取表格数据失败，重试中: {e}')
                time.sleep(self.config.RETRY_INTERVAL)

        return data

    def get_total_pages(self, sb) -> int:
        """获取总页数"""
        try:
            for attempt in range(self.config.MAX_RETRIES):
                try:
                    # 获取最后一页的页码
                    pages_element = sb.find_element('//*[@id="mini-74"]/div[2]/a[9]')
                    pages = int(pages_element.text)
                    pages = max(1, pages)
                    logger.info(f'总页数: {pages}')
                    return pages

                except Exception as e:
                    if attempt == self.config.MAX_RETRIES - 1:
                        logger.warning(f'获取总页数失败: {e}')
                        return 1
                    logger.warning(f'获取总页数失败，重试中: {e}')
                    time.sleep(self.config.RETRY_INTERVAL)

            logger.warning('无法获取总页数，使用默认值1')
            return 1

        except Exception as e:
            logger.error('获取总页数失败: %s', e)
            return 1

    def navigate_to_page(self, sb, page: int) -> bool:
        """导航到指定页面"""
        try:
            if page == 1:
                return True

            for attempt in range(self.config.MAX_RETRIES):
                try:
                    # 尝试通过页码输入框跳转
                    page_input = sb.find_element('.mini-pager-index')
                    sb.clear(page_input)
                    sb.type(page_input, str(page))
                    page_input.send_keys(sb.driver.keys.ENTER)
                    time.sleep(2)
                    return True

                except Exception:
                    try:
                        # 尝试点击下一页按钮
                        next_page_selector = '//*[@id="mini-74"]/div[2]/a[10]'
                        sb.wait_for_element_clickable(
                            next_page_selector, timeout=self.config.WAIT_TIMEOUT
                        )
                        sb.click(next_page_selector)
                        time.sleep(2)
                        return True

                    except Exception as e:
                        if attempt == self.config.MAX_RETRIES - 1:
                            logger.error(f'导航到第 {page} 页失败: {e}')
                            return False
                        logger.warning(f'导航到第 {page} 页失败，重试中: {e}')
                        time.sleep(self.config.RETRY_INTERVAL)

            return False

        except Exception as e:
            logger.error(f'导航到第 {page} 页时发生错误: {e}')
            return False

    def init_database(self):
        """初始化数据库连接并确保表存在"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='已备案合同明细';"
            )
            table_exists = cursor.fetchone()

            if not table_exists:
                logger.warning(
                    "表 '已备案合同明细' 不存在，程序将继续运行但不会更新数据库"
                )

            conn.close()
            logger.info('数据库连接测试成功')

        except Exception as e:
            logger.error(f'数据库初始化失败: {e}')
            raise

    def save_to_database(self, data: List[List[str]], headers: List[str]) -> int:
        """将数据保存到数据库，返回成功插入的记录数"""
        if not data or not headers:
            logger.warning('没有数据需要保存到数据库')
            return 0

        # 查找合同编号列的索引
        contract_number_index = -1
        for i, header in enumerate(headers):
            if '合同编号' in header:
                contract_number_index = i
                break

        if contract_number_index == -1:
            logger.warning('未找到合同编号列，无法更新数据库')
            return 0

        success_count = 0

        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='已备案合同明细';"
            )
            if not cursor.fetchone():
                logger.warning("表 '已备案合同明细' 不存在，跳过数据库更新")
                conn.close()
                return 0

            for row_data in data:
                try:
                    # 检查合同编号是否为空
                    if (
                        contract_number_index >= len(row_data)
                        or not row_data[contract_number_index]
                        or row_data[contract_number_index].strip() == ''
                    ):
                        logger.debug('合同编号为空，跳过该记录')
                        continue

                    contract_number = row_data[contract_number_index].strip()

                    # 检查合同编号是否已存在
                    cursor.execute(
                        'SELECT COUNT(*) FROM 已备案合同明细 WHERE 合同编号 = ?',
                        (contract_number,),
                    )
                    if cursor.fetchone()[0] > 0:
                        logger.debug(f'合同编号 {contract_number} 已存在，跳过')
                        continue

                    # 构建插入语句
                    adjusted_data = row_data[: len(headers)]
                    if len(adjusted_data) < len(headers):
                        adjusted_data.extend([''] * (len(headers) - len(adjusted_data)))

                    placeholders = ', '.join(['?' for _ in headers])
                    columns = ', '.join([f'`{header}`' for header in headers])

                    insert_sql = f'INSERT INTO 已备案合同明细 ({columns}) VALUES ({placeholders})'
                    cursor.execute(insert_sql, adjusted_data)
                    success_count += 1
                    logger.debug(f'成功插入合同: {contract_number}')

                except Exception as e:
                    logger.warning(f'插入记录时发生错误: {e}')
                    continue

            conn.commit()
            conn.close()
            logger.info(f'成功向数据库插入 {success_count} 条新记录')

        except Exception as e:
            logger.error(f'数据库操作失败: {e}')

        return success_count

    def run(self):
        """运行爬虫"""
        # 检查并设置本地ChromeDriver
        local_driver_path = None
        for path in self.config.CHROME_DRIVER_PATHS:
            if os.path.exists(path):
                local_driver_path = path
                logger.info(f'找到本地ChromeDriver: {path}')
                break

        if local_driver_path:
            # 将驱动目录添加到PATH的开头，让SeleniumBase优先使用
            driver_dir = os.path.dirname(local_driver_path)
            current_path = os.environ.get('PATH', '')
            os.environ['PATH'] = driver_dir + os.pathsep + current_path
            logger.info(f'已将 {driver_dir} 添加到系统PATH开头')
        else:
            logger.warning('未找到本地ChromeDriver，SeleniumBase将尝试自动下载')

        # 使用SeleniumBase，现在应该能找到本地驱动
        try:
            with SB(uc=False, headless=False) as sb:
                try:
                    start_time = time.time()

                    # 初始化数据库连接
                    self.init_database()

                    if not self.login(sb):
                        raise Exception('登录失败')

                    self.navigate_to_contracts(sb)
                    total_pages = self.get_total_pages(sb)

                    if total_pages <= 0:
                        logger.warning('总页数为0，强制设为1')
                        total_pages = 1

                    # 先提取表头
                    headers = self.extract_table_header(sb)
                    all_data = []

                    # 提取第一页数据
                    first_page_data = self.extract_table_data(sb)
                    if first_page_data:
                        all_data.extend(first_page_data)
                        logger.info('已完成第 1/%d 页数据抓取', total_pages)
                    else:
                        logger.warning('第1页未获取到数据')

                    # 翻页抓取
                    for page in range(2, total_pages + 1):
                        try:
                            if not self.navigate_to_page(sb, page):
                                logger.warning(f'无法导航到第{page}页，跳过')
                                continue

                            page_data = self.extract_table_data(sb)
                            if page_data:
                                all_data.extend(page_data)
                                logger.info(
                                    '已完成第 %d/%d 页数据抓取', page, total_pages
                                )
                            else:
                                logger.warning(f'第{page}页未获取到数据')

                        except Exception as e:
                            logger.error('第 %d 页数据抓取失败: %s', page, e)
                            continue

                    # 保存数据
                    if all_data and headers:
                        # 自动检测并详细日志输出表头和数据列数差异
                        data_col_count = len(all_data[0]) if all_data else 0
                        header_col_count = len(headers)

                        if data_col_count != header_col_count:
                            logger.warning(
                                f'表头列数: {header_col_count}，数据列数: {data_col_count}，将自动对齐。'
                            )
                            logger.warning(f'表头内容: {headers}')
                            logger.warning(f'首行数据: {all_data[0]}')

                        min_len = min(header_col_count, data_col_count)
                        headers = headers[:min_len]
                        all_data = [row[:min_len] for row in all_data]

                        # 保存到Excel文件
                        df = pd.DataFrame(all_data, columns=headers)
                        df = df.dropna(how='all').dropna(axis=1, how='all')
                        output_file = '采购数据_合同信息_simple.xlsx'
                        df.to_excel(output_file, index=False)
                        logger.info(f'数据已保存到 {output_file}，共 {len(df)} 条记录')

                        # 保存到数据库
                        try:
                            db_success_count = self.save_to_database(all_data, headers)
                            logger.info(
                                f'数据库更新完成，新增 {db_success_count} 条记录'
                            )
                        except Exception as e:
                            logger.error(f'数据库保存失败: {e}')

                        duration = (time.time() - start_time) / 60
                        logger.info('任务完成，运行时间：%.2f 分钟', duration)

                    else:
                        logger.warning('未获取到任何数据或表头，无法保存')

                except Exception as e:
                    logger.error('数据抓取过程发生错误: %s', e)
                    raise

        except Exception as e:
            logger.error('运行爬虫时发生错误: %s', e)
            raise


def main():
    """主函数"""
    try:
        logger.info('开始运行SeleniumBase爬虫（自动检测本地驱动）...')
        crawler = SimpleContractCrawler()
        crawler.run()
    except Exception as e:
        logger.error('程序执行失败: %s', e)
        sys.exit(1)


if __name__ == '__main__':
    main()
