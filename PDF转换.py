#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-06-10 19:32:30
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-15 12:42:24
# 文件相对于项目的路径   : \Crawl_AI\PDF转换.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
import subprocess
import time
import os
import pyautogui
import psutil
from time import sleep
import re
from glob import glob
import PyPDF2
from threading import Lock
import warnings
import logging

# 配置警告过滤
warnings.filterwarnings('ignore', category=UserWarning, module='PyPDF2')
# 配置logging，只显示ERROR级别以上的信息
logging.getLogger('PyPDF2').setLevel(logging.ERROR)

# 配置区域（用户需自定义部分）
acrobat_path = r'C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe'
PATTERNS = [
    re.compile(r'ZC\d{2}-A\d{3}'),
    # 新规则：CRRC+2位数字+1位字母或数字+3位数字+1位字母或数字+7位数字
    re.compile(r'CRRC\d{2}[A-Z0-9]\d{3}[A-Z0-9]\d{7}'),
]
CONTRACT_DIR = r'D:\user\合同汇总'


# pdf_list = [
#     r'D:\user\合同汇总\2024年度厂房租赁合同补充协议.pdf',
#     r'D:\user\合同汇总\2025年度厂房租赁合同.pdf',
#     r'D:\user\合同汇总\公铁两用车维修合同.pdf',
# ]
def starts_with_pattern(text):
    """
    判断字符串是否以满足PATTERNS任一规则的字符串开头
    Args:
        text: 要检查的字符串
    Returns:
        匹配到的字符串或None
    """
    for pattern in PATTERNS:
        match = pattern.match(text)  # 使用match()而不是search()
        if match:
            return True
            # return match.group(0)
    return False


def read_pdf_text(path):
    """尝试使用PyPDF2直接提取文本
    Args:
        path: PDF文件路径
    Returns:
        str: 提取的文本，如果提取失败返回空字符串
    """
    try:
        with open(path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            text = []
            for page in reader.pages:
                try:
                    extracted = page.extract_text()
                    if extracted:
                        text.append(extracted)
                except Exception as e:
                    print(f'页面提取失败: {str(e)}')
                    continue

            return '\n'.join(text).strip()
    except Exception as e:
        print(f'PDF读取失败 {path}: {str(e)}')
        return ''


def wait_for_ocr_complete(gw, pdf_path):
    """等待Adobe OCR识别完成
    Args:
        gw: pygetwindow模块
        pdf_path: PDF文件路径
    Returns:
        bool: 是否成功完成OCR识别
    """
    max_wait = 180  # 最大等待时间3分钟
    start_time = time.time()

    def check_progress_bar(window):
        """
        检查Adobe Acrobat窗口底部是否有进度条
        有进度条返回True，OCR未完成；无进度条返回False，OCR已完成
        """
        try:
            # 截取窗口底部区域
            bar_x_start = window.left + int(window.width * 0.05)
            bar_x_end = window.left + int(window.width * 0.95)
            bar_y_start = window.top + int(window.height * 0.90)
            bar_y_end = window.top + int(window.height * 0.97)

            screenshot = pyautogui.screenshot(
                region=(
                    bar_x_start,
                    bar_y_start,
                    bar_x_end - bar_x_start,
                    bar_y_end - bar_y_start,
                )
            )

            # 统计蓝色/白色像素数量
            blue_white_pixels = 0
            total_pixels = 0

            for x in range(screenshot.width):
                for y in range(screenshot.height):
                    r, g, b = screenshot.getpixel((x, y))
                    # 蓝色进度条
                    if 0 <= r <= 80 and 100 <= g <= 180 and 180 <= b <= 255:
                        blue_white_pixels += 1
                    # 白色进度条
                    elif 220 <= r <= 255 and 220 <= g <= 255 and 220 <= b <= 255:
                        blue_white_pixels += 1
                    total_pixels += 1

            # 如果蓝/白像素占比大于20%，说明有进度条
            if total_pixels == 0:
                return False
            ratio = blue_white_pixels / total_pixels
            return ratio > 0.20

        except Exception as e:
            print(f'检查进度条时出错: {e}')
            return False

    print('等待OCR处理...')
    progress_bar_gone_count = 0  # 连续未检测到进度条的次数
    progress_bar_detected = False  # 是否检测到过进度条

    while time.time() - start_time < max_wait:
        windows = gw.getWindowsWithTitle('Adobe Acrobat')
        if not windows:
            print('找不到Adobe窗口')
            return False

        window = windows[0]
        has_progress = check_progress_bar(window)

        if has_progress:
            progress_bar_detected = True
            progress_bar_gone_count = 0  # 重置计数器
            print('.', end='', flush=True)  # 显示进度
        elif progress_bar_detected:  # 只有在之前检测到过进度条的情况下才开始计数
            progress_bar_gone_count += 1
            if progress_bar_gone_count >= 5:  # 连续5次未检测到进度条，认为转换完成
                print('\n检测到转换完成')
                return True

        sleep(0.2)  # 每0.2秒检查一次

    print(f'\nOCR识别超时。PDF: {pdf_path}')
    return False  # 超时返回False


def convert_pdf_to_word(pdf_path):
    """核心转换函数"""
    # 生成Word文件名
    word_name = pdf_path.replace('.pdf', '.docx')

    # 检查是否已存在转换结果
    if os.path.exists(word_name):
        print(f'[跳过] {word_name} 已存在')
        return

    # 启动Adobe进程（使用上下文管理器确保资源释放）
    with subprocess.Popen([acrobat_path, pdf_path]) as process:
        try:
            time.sleep(5)  # 等待程序初始化

            try:
                # 导入并使用pygetwindow控制窗口
                import pygetwindow as gw

                # 等待Adobe Acrobat窗口出现并获取窗口对象
                max_attempts = 10
                acrobat_win = None
                for attempt in range(max_attempts):
                    windows = gw.getWindowsWithTitle('Adobe Acrobat')
                    if windows and len(windows) > 0:
                        acrobat_win = windows[0]
                        break
                    sleep(1)
                    if attempt == max_attempts - 1:
                        raise Exception('无法找到Adobe Acrobat窗口')

                if acrobat_win is None:
                    raise Exception('无法获取Adobe Acrobat窗口')  # 激活并最大化窗口
                try:
                    acrobat_win.activate()
                    sleep(1)  # 等待窗口激活

                    # 使用alt+space+x来最大化窗口（更可靠的方法）
                    pyautogui.hotkey('alt', 'space')
                    sleep(0.5)
                    pyautogui.press('x')
                    sleep(1)  # 等待最大化完成

                    # 再次检查窗口状态
                    pyautogui.hotkey('alt', 'space')
                    sleep(0.5)
                    pyautogui.press('x')
                    sleep(1)  # 确保窗口保持最大化
                except:
                    # 如果activate失败，使用备用方案
                    # pyautogui.hotkey('alt', 'tab')
                    # sleep(1)
                    # pyautogui.hotkey('alt', 'space', 'x')
                    # sleep(1)
                    pass

                # 重新获取窗口位置
                windows = gw.getWindowsWithTitle('Adobe Acrobat')
                if windows and len(windows) > 0:
                    acrobat_win = windows[0]
                else:
                    raise Exception('无法重新获取Adobe Acrobat窗口')

                # 计算窗口中心位置
                center_x = acrobat_win.left + acrobat_win.width // 2
                center_y = acrobat_win.top + acrobat_win.height // 2

                # 在窗口中心位置进行操作
                pyautogui.moveTo(center_x, center_y)
                sleep(0.5)
                pyautogui.click()
                sleep(0.5)
                pyautogui.rightClick()
                sleep(0.5)
                pyautogui.press('r')  # 识别文本选项
                sleep(0.5)
                pyautogui.press('enter')  # 等待OCR完成

                print(f'正在OCR识别 {pdf_path}，请等待...')
                if wait_for_ocr_complete(gw, pdf_path):
                    print('OCR识别完成')
                    # 保存文件
                    # pyautogui.hotkey('alt', 'f')
                    # sleep(1)
                    pyautogui.hotkey('ctrl', 'shift', 's')
                    sleep(1)
                    pyautogui.press('enter')  # 使用默认文件名保存
                    sleep(1)
                    pyautogui.press('y')  # 使用默认文件名保存
                    sleep(1)
                    pyautogui.hotkey('ctrl', 'w')  # 关闭文档
                    # sleep(1)
                    pyautogui.hotkey('alt', 'f4')  # 关闭程序
                else:
                    print('OCR识别超时或失败')
                    raise Exception('OCR识别超时')

            except Exception as e:
                print(f'窗口操作异常: {e}')
                # 确保程序正常退出
                try:
                    pyautogui.hotkey('alt', 'f4')
                except:
                    pass

            # print(f'{pdf_path} 已成功转换！')

        finally:  # 确保进程终止（双保险）
            try:
                process.terminate()
            except:
                pass
            # 强制结束所有Acrobat进程
            try:
                for proc in psutil.process_iter(['name']):  # 只获取进程名
                    try:
                        if 'Acrobat' in proc.info['name']:  # 使用info字典获取名称
                            try:
                                proc.kill()
                            except (
                                psutil.NoSuchProcess,
                                psutil.AccessDenied,
                                psutil.TimeoutExpired,
                            ):
                                pass
                    except (
                        psutil.NoSuchProcess,
                        psutil.AccessDenied,
                        psutil.TimeoutExpired,
                    ):
                        continue
            except Exception as e:
                print(f'清理Acrobat进程时出错: {e}')
                pass


# 添加线程锁，用于同步Adobe操作
adobe_lock = Lock()
# 添加当前活动进程计数器
active_process_count = 0
active_process_lock = Lock()


def process_pdf_file(pdf_path):
    """处理单个PDF文件的函数"""
    global active_process_count

    try:
        with active_process_lock:
            # 如果活动进程数超过2，等待
            while active_process_count >= 2:
                active_process_lock.release()
                sleep(5)  # 等待5秒后重试
                active_process_lock.acquire()
            active_process_count += 1

        # 使用锁确保一次只有一个线程操作Adobe
        with adobe_lock:
            convert_pdf_to_word(pdf_path)

    except Exception as e:
        print(f'处理文件 {pdf_path} 时出错: {e}')
    finally:
        with active_process_lock:
            active_process_count -= 1


if __name__ == '__main__':
    files = []
    ext = 'pdf'
    files.extend(glob(os.path.join(CONTRACT_DIR, f'*{ext}')))

    # 读取错误文件列表
    err_file_path = os.path.join(os.path.dirname(__file__), 'err.txt')
    err_files = set()
    if os.path.exists(err_file_path):
        with open(err_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    err_files.add(line)

    for file in files:
        basename = os.path.basename(file)
        if basename in err_files:
            print(f'[跳过] {basename} 在err.txt中，跳过转换')
            continue
        if not read_pdf_text(file):
            try:
                convert_pdf_to_word(file)
                if read_pdf_text(file):
                    print(f'文件 {file} 转换成功！')
                else:
                    print(f'文件 {file} 转换失败！')
                    with open(err_file_path, 'a', encoding='utf-8') as ef:
                        ef.write(basename + '\n')
            except Exception as e:
                print(f'文件 {file} 转换异常: {e}')
                with open(err_file_path, 'a', encoding='utf-8') as ef:
                    ef.write(basename + '\n')
    print('批量转换完成！')
