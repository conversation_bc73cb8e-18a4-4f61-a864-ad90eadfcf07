#!/usr/bin/env python
# -*- coding:utf-8 -*-
#
# 作者           : KingFreeDom
# 创建时间         : 2025-06-05 19:49:59
# 最近一次编辑者      : KingFreeDom
# 最近一次编辑时间     : 2025-06-12 10:10:55
# 文件相对于项目的路径   : \Crawl_AI\更新品类及合同文件.py
#
# Copyright (c) 2025 by 中车眉山车辆有限公司/KingFreeDom, All Rights Reserved.
#
import sqlite3

import os
import re
import pandas as pd


def find_files_starting_with(directory, prefix):
    """在指定目录下查找文件名以prefix开头的文件"""
    result = []
    for filename in os.listdir(directory):
        if filename.startswith(prefix):
            result.append(os.path.join(directory, filename))
    return result


def update_cntract_files(cursor):
    PATTERNS = [
        re.compile(r'ZC\d{2}-A\d{3}'),
        re.compile(r'CRRC\d{2}[A-Z0-9]\d{3}[A-Z0-9]\d{7}'),
    ]

    directory = 'd:/user/合同备案'  # 替换为你的目录路径
    for root, dirnames, filenames in os.walk(directory):
        for filename in filenames:
            filename = filename.upper()
            if '_' in filename:
                prefix = filename.split('_')[0]
            else:
                continue

            # 验证第一部分是否符合合同号格式
            for pat in PATTERNS:
                if pat.match(prefix):
                    contract_no = prefix
                    sql = "select 合同编号 from 品类及合同文件 where 合同编号='{contract_no}'"
                    cursor.execute(sql)
                    rows = cursor.fetchall()
                    if len(rows) > 0:
                        sql = f"update 品类及合同文件 set 合同文件名='{filename}' where 合同编号='{contract_no}'"
                        cursor.execute(sql)
                    else:
                        sql = f"insert into 品类及合同文件 (合同编号,合同文件名) values ('{contract_no}','{filename}')"
                        cursor.execute(sql)
                    continue

    cursor.connection.commit()


def update_from_excel(cursor, filename):
    directory = 'd:/user/合同汇总'  # 替换为你的目录路径
    df = pd.read_excel(filename)
    for index, row in df.iterrows():
        contract_no = row['合同编号']
        try:
            合同名称 = '' if pd.isna(row['合同名称']) else str(row['合同名称'])
        except Exception as e:
            print(e)
            # continue
        try:
            合同金额 = '' if pd.isna(row['合同金额']) else str(row['合同金额'])
        except Exception as e:
            print(e)
            # continue
        try:
            对手方名称 = '' if pd.isna(row['对手方名称']) else str(row['对手方名称'])
        except Exception as e:
            print(e)
            # continue
        try:
            约定税率 = '' if pd.isna(row['约定税率']) else str(row['约定税率'])
        except Exception as e:
            print(e)
            # continue
        try:
            品类编码 = '' if pd.isna(row['品类编码']) else str(row['品类编码'])
        except Exception as e:
            print(e)
            # continue
        try:
            品类名称 = '' if pd.isna(row['品类名称']) else str(row['品类名称'])
        except Exception as e:
            print(e)
            # continue
        try:
            合同文件名 = find_files_starting_with(directory, contract_no + '_')[0]
        except Exception as e:
            print(e)
            合同文件名 = ''
            # continue
        try:
            cursor.execute(
                'SELECT 1 FROM 品类及合同文件 WHERE 合同编号=?', (contract_no,)
            )
            exists = cursor.fetchone()
            if exists:
                cursor.execute(
                    'UPDATE 品类及合同文件 SET 合同名称=?,合同金额=?,对手方名称=?,约定税率=?,品类编码=?,品类名称=?,合同文件名=?  WHERE 合同编号=?',
                    (
                        合同名称,
                        合同金额,
                        对手方名称,
                        约定税率,
                        品类编码,
                        品类名称,
                        合同文件名,
                        contract_no,
                    ),
                )
                cursor.connection.commit()
                print(f'更新合同编号{contract_no}成功')
            else:
                cursor.execute(
                    'INSERT INTO 品类及合同文件 (合同编号,合同名称,合同金额,对手方名称,约定税率,品类编码,品类名称,合同文件名) VALUES (?,?,?,?,?,?,?,?)',
                    (
                        contract_no,
                        合同名称,
                        合同金额,
                        对手方名称,
                        约定税率,
                        品类编码,
                        品类名称,
                        合同文件名,
                    ),
                )
                cursor.connection.commit()

                print(f'插入合同编号{contract_no}成功')
        except Exception as e:
            print(e)
            continue


if __name__ == '__main__':
    filename = '品类及合同文件.xlsx'
    with sqlite3.connect(r'd:\user\PythonProject\ZConline\app.db', timeout=60) as conn:
        conn.execute('PRAGMA journal_mode=WAL;')
        cursor = conn.cursor()
        update_from_excel(cursor, filename)
        # update_cntract_files(cursor)
        conn.commit()
